# 工作联系单查询

## 基本信息
- **子菜单名**: 工作联系单查询
- **路由**: ~/ModuleXZ/WorkOrder/ShowIndex
- **模块名**: ModuleXZ
- **视图名**: WorkOrder
- **功能描述**: 用于查询和统计工作联系单记录，支持工作任务的跟踪和分析

## 页面逻辑分析

### 主要Controller方法
**WorkOrderController.cs**

1. **ShowIndex(ModelWorkOrderIndex model, ExportHelper export)** - 工作联系单查询列表
   - 功能：显示工作联系单查询结果
   - 逻辑：
     - 设置导出对象model.ExportObject = export
     - 调用model.RetriveDataShow()获取查询数据
     - 返回查询结果视图

2. **QueryDetail(ModelWorkOrderCreate model)** - 工作联系单详情查询
   - 功能：查看工作联系单详细信息
   - 逻辑：调用model.RetriveDataShow()获取详情数据

3. **TaskProgress(ModelWorkOrderProgress model)** - 工作进度查询
   - 功能：查询工作任务的执行进度
   - 逻辑：调用model.RetriveData()获取进度数据

4. **ExportWorkOrderData(ModelWorkOrderIndex model)** - 导出工作联系单数据
   - 功能：导出工作联系单数据到Excel
   - 逻辑：
     - 调用model.RetriveDataShow()获取数据
     - 生成Excel文件
     - 返回文件下载

### 主要Model类
**ModelWorkOrder.cs**

1. **ModelWorkOrderIndex** - 工作联系单查询模型
   - 属性：
     - SearchEntity：查询条件实体
     - GridDataSources：查询结果数据
     - Count：总记录数
     - ExportObject：导出对象
   - 方法：
     - RetriveDataShow()：执行查询操作
     - 支持多种查询条件组合

2. **ModelWorkOrderCreate** - 工作联系单详情模型
   - 方法：
     - RetriveDataShow()：获取工作联系单详情用于查看

## 数据库使用情况

### 主要数据表
1. **UTXZWorkOrder** - 工作联系单主表
   - 查询字段：
     - ApplyUserID：申请人
     - UnitID：单位
     - WorkContent：工作内容
     - WorkType：工作类型
     - Priority：优先级
     - Status：状态
     - CreateDate：创建时间
     - CompleteDate：完成时间
   - 操作：多条件查询、统计分析

2. **UTXZWorkOrderUser** - 工作联系单用户关联表
   - 查询字段：
     - WorkOrderID：工作联系单ID
     - UserID：用户ID
     - UserType：用户类型（执行人、监督人等）
     - AssignDate：分配时间
   - 操作：人员分配查询

3. **UTXZWorkOrderProgress** - 工作进度记录表
   - 字段：
     - WorkOrderID：工作联系单ID
     - ProgressDate：进度日期
     - ProgressContent：进度内容
     - ProgressPercent：完成百分比
     - ReportUser：汇报人
   - 操作：进度跟踪查询

### 主要Service接口
1. **IServiceUTXZWorkOrder** - 工作联系单服务
   - 查询相关方法：
     - SearchWorkOrder()：按条件查询工作联系单
     - GetWorkOrderStatistics()：获取工作统计数据
     - ExportWorkOrderData()：导出工作数据

2. **IServiceUTXZWorkOrderUser** - 工作用户服务
   - 方法：用户分配查询

## 查询功能
### 基本查询条件
1. **时间范围**：
   - 创建时间范围
   - 计划完成时间范围
   - 实际完成时间范围

2. **人员条件**：
   - 申请人
   - 执行人
   - 监督人
   - 部门

3. **状态条件**：
   - 工作状态
   - 完成状态
   - 审批状态

4. **工作属性**：
   - 工作类型
   - 优先级
   - 紧急程度

### 高级查询功能
1. **关键字查询**：在工作内容中搜索关键字
2. **组合查询**：支持多个条件的组合查询
3. **进度查询**：按完成进度查询
4. **延期查询**：查询延期的工作任务

## 查询结果展示
### 列表显示
- **工作信息**：工作内容、工作类型、优先级
- **人员信息**：申请人、执行人、监督人
- **时间信息**：创建时间、计划完成时间、实际完成时间
- **状态信息**：当前状态、完成进度

### 详情显示
- **完整的工作联系单信息**
- **人员分配详情**
- **工作进度记录**
- **相关附件**

## 统计分析功能
### 基础统计
1. **数量统计**：
   - 总工作数量
   - 各状态工作数量
   - 各类型工作数量

2. **时间统计**：
   - 平均完成时间
   - 延期工作统计
   - 工作效率分析

3. **人员统计**：
   - 个人工作量统计
   - 部门工作量统计
   - 工作分配统计

### 高级分析
1. **效率分析**：工作完成效率和质量分析
2. **趋势分析**：工作量的时间趋势
3. **负载分析**：人员工作负载分析
4. **延期分析**：工作延期原因分析

## 进度跟踪功能
### 进度查询
1. **实时进度**：查看工作的实时进度
2. **进度历史**：查看进度更新历史
3. **里程碑**：查看关键里程碑完成情况
4. **预警信息**：查看进度预警信息

### 进度分析
1. **完成率分析**：分析工作完成率
2. **进度偏差**：分析进度偏差情况
3. **资源利用**：分析资源利用效率
4. **质量评估**：评估工作完成质量

## 导出功能
### 支持格式
- **Excel格式**：详细的工作记录表
- **PDF格式**：工作报告
- **Project格式**：项目管理文件

### 导出内容
1. **基础数据导出**：工作联系单基本信息
2. **详细数据导出**：包含所有字段的详细数据
3. **统计数据导出**：统计分析结果
4. **进度报告导出**：工作进度报告

## 权限控制
### 查询权限
- **个人查询**：员工只能查询与自己相关的工作
- **部门查询**：部门负责人可查询本部门工作
- **全局查询**：管理员可查询所有工作

### 数据权限
- **敏感信息保护**：保护敏感的工作内容
- **级别控制**：不同级别用户看到不同详细程度的信息

## 报表功能
1. **工作统计报表**：工作量和完成情况统计
2. **效率分析报表**：工作效率和质量分析
3. **人员工作量报表**：个人和部门工作量统计
4. **延期分析报表**：工作延期情况分析

## 预警功能
1. **延期预警**：工作即将延期的预警
2. **负载预警**：人员工作负载过重预警
3. **质量预警**：工作质量问题预警
4. **资源预警**：资源不足预警

## 性能优化
1. **索引优化**：在常用查询字段上建立索引
2. **分页查询**：大数据量时使用分页
3. **缓存机制**：常用查询结果缓存
4. **异步处理**：大数据量导出使用异步处理

## 相关枚举
- **EnumWorkOrderStatus**：工作联系单状态枚举
- **EnumWorkType**：工作类型枚举
- **EnumPriority**：优先级枚举
