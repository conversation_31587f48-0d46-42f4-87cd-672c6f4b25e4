# 项目基础信息维护模块分析

## 模块概述

项目基础信息维护模块是项目管理系统的核心基础模块，负责管理项目的基本信息和配置参数。该模块提供项目信息的创建、编辑、查看等功能，为整个物业管理系统提供项目基础数据支撑，是所有其他业务模块的数据基础。

## 路由信息

- **子菜单名**: 项目基础信息维护
- **路由地址**: ~/ModuleUnit/Unit/ProjectTree
- **模块名**: ModuleUnit
- **视图名**: Unit

## 页面逻辑分析

### 主要功能

1. **项目基础信息管理**
   - 项目基本信息的查看和编辑
   - 项目编码、名称、地址等基础数据维护
   - 项目联系信息和负责人管理
   - 项目面积和建设信息管理

2. **项目层级管理**
   - 支持集团-分公司-项目的层级结构
   - 父子项目关系维护
   - 项目等级设置和管理

3. **项目配置管理**
   - 项目背景图片上传和管理
   - 项目地图坐标设置
   - 考勤机编号配置
   - 默认设备巡查人员设置

4. **项目树形结构**
   - 多项目支持和项目选择
   - 项目层级树形展示
   - 动态加载的项目结构

### 控制器分析

#### UnitController (ModuleUnit)

**主要方法**:
- `ProjectTree()`: 项目管理树形结构页面
- `UnitInfo()`: 项目基础信息维护页面
- `UnitInfo(POST)`: 项目信息保存操作

**核心业务逻辑**:
```csharp
// 项目管理树形结构
public ActionResult ProjectTree(ModelSysOrganizationIndex model)
{
   IList<UTBaseUnitManage>UTBaseUnitManageList= SessionManager.CurrentUserProjects;
   if (UTBaseUnitManageList == null || UTBaseUnitManageList.Count==0|| UTBaseUnitManageList.Count==1)
   {
       return RedirectToAction("UnitInfo", new { UnitID = SessionManager.CurrentUserAccount.UnitID });
   }
   else
   {
       return View(model);
   }
}

// 项目基础信息维护页面
public ActionResult UnitInfo(ModelUnit model)
{
    if (model.UnitID == null || model.UnitID == Guid.Empty)
    {
        if (SessionManager.CurrentUserAccount != null && SessionManager.CurrentUserAccount.UnitID != null && SessionManager.CurrentUserAccount.UnitID != Guid.Empty)
        {
            model.UnitID = SessionManager.CurrentUserAccount.UnitID;
        }
    }
    model.RetriveData();
    return View(model);
}

// 项目信息保存
[HttpPost]
public ActionResult UnitInfo(ModelUnit model, HttpPostedFileBase FileName, HttpPostedFileBase ChapterFile, FormCollection collection)
{
    try
    {
        model.Save();
        return RedirectToAction("UnitInfo", new { UnitID = model.UTJTUnitManageEnt.ID, pagestate = (int)EnumPageState.编辑 });
    }
    catch
    {
        Error = "操作失败";
        model.RetriveData();
        return View(model);
    }
}
```

### 模型分析

#### ModelUnit
- 负责项目基础信息的数据处理
- 支持项目信息的查看和编辑
- 权限控制和操作验证

**核心保存逻辑**:
```csharp
public string Save()
{
    string result = string.Empty;
    try
    {
        if (UTJTUnitManageEnt.ID == null || UTJTUnitManageEnt.ID == Guid.Empty)
        {
            // 新增项目
            if (FileName != null)
            {
                UTJTUnitManageEnt.BackgroundImg = UploadHelperC.SaveFile(FileName, "Attachment");
                System.Drawing.Image img = System.Drawing.Image.FromStream(FileName.InputStream);
                UTJTUnitManageEnt.BackHeight = img.Height;
                UTJTUnitManageEnt.BackWidth = img.Width;
            }
            if (ChapterFile != null)
            {
                UTJTUnitManageEnt.ChapterFile = UploadHelperC.SaveFile(ChapterFile, "Attachment");
            }
            UTJTUnitManageEnt.ID = (Guid)serviceUTJTUnitManage.Save(UTJTUnitManageEnt);
        }
        else
        {
            // 更新项目
            serviceUTJTUnitManage.Update(UTJTUnitManageEnt);
        }
    }
    catch (Exception ex)
    {
        result = ex.Message;
    }
    return result;
}
```

## 数据表结构

### 主表: UT_Base_UnitManage (集团项目架构)

| 字段名 | 数据类型 | 长度 | 允许空 | 说明 |
|--------|----------|------|--------|------|
| ID | uniqueidentifier | - | NO | 主键ID |
| PID | uniqueidentifier | - | YES | 父单位ID |
| UnitCode | nvarchar | 50 | YES | 单位编码 |
| UnitClass | int | - | YES | 单位分类 |
| UnitName | nvarchar | 50 | YES | 单位名称 |
| UnitLevel | int | - | YES | 单位等级（0集团，1分公司，2项目） |
| BuildDate | datetime | - | YES | 建园日期 |
| CompanyName | nvarchar | 50 | YES | 所属公司名称 |
| UnitAddress | nvarchar | 50 | YES | 地址 |
| UnitArea | decimal | - | YES | 占地面积 |
| BuildArea | decimal | - | YES | 建筑面积 |
| AdminUserID | bigint | - | YES | 管理员用户ID |
| Manager | nvarchar | 50 | YES | 负责人 |
| Tel | nvarchar | 50 | YES | 电话 |
| Fax | nvarchar | 50 | YES | 传真 |
| Email | nvarchar | 50 | YES | 邮箱 |
| CreateDate | datetime | - | YES | 创建时间 |
| CreateUser | bigint | - | YES | 创建用户 |
| UpdateDate | datetime | - | YES | 更新时间 |
| UpdateUser | bigint | - | YES | 更新用户 |
| IsProject | int | - | YES | 是否项目 |
| BackgroundImg | nvarchar | 255 | YES | 背景图 |
| BackWidth | int | - | YES | 背景图宽 |
| BackHeight | int | - | YES | 背景图高 |
| KQSN | nvarchar | 50 | YES | 考勤机编号 |
| PointX | nvarchar | 50 | YES | 地图坐标X |
| PointY | nvarchar | 50 | YES | 地图坐标Y |
| EqUserIDStr | nvarchar | 500 | YES | 默认设备巡查人ID |
| EqUserNameStr | nvarchar | 500 | YES | 默认设备巡查人姓名 |
| ChapterFile | nvarchar | 255 | YES | 印章文件 |

**建表SQL**:
```sql
create table [dbo].[UT_Base_UnitManage] (  
    [ID] uniqueidentifier  NOT NULL ,  
    [PID] uniqueidentifier  NULL ,  
    [UnitCode] nvarchar(50)  NULL ,  
    [UnitClass] int  NULL ,  
    [UnitName] nvarchar(50)  NULL ,  
    [UnitLevel] int  NULL ,  
    [BuildDate] datetime  NULL ,  
    [CompanyName] nvarchar(50)  NULL ,  
    [UnitAddress] nvarchar(50)  NULL ,  
    [UnitArea] decimal(18,2)  NULL ,  
    [BuildArea] decimal(18,2)  NULL ,  
    [AdminUserID] bigint  NULL ,  
    [Manager] nvarchar(50)  NULL ,  
    [Tel] nvarchar(50)  NULL ,  
    [Fax] nvarchar(50)  NULL ,  
    [Email] nvarchar(50)  NULL ,  
    [CreateDate] datetime  NULL ,  
    [CreateUser] bigint  NULL ,  
    [UpdateDate] datetime  NULL ,  
    [UpdateUser] bigint  NULL ,  
    [IsProject] int  NULL ,  
    [BackgroundImg] nvarchar(255)  NULL ,  
    [BackWidth] int  NULL ,  
    [BackHeight] int  NULL ,  
    [KQSN] nvarchar(50)  NULL ,  
    [PointX] nvarchar(50)  NULL ,  
    [PointY] nvarchar(50)  NULL ,  
    [EqUserIDStr] nvarchar(500)  NULL ,  
    [EqUserNameStr] nvarchar(500)  NULL ,  
    [ChapterFile] nvarchar(255)  NULL 
);  
ALTER TABLE [dbo].[UT_Base_UnitManage] ADD CONSTRAINT PK_UT_Base_UnitManage PRIMARY KEY  ([ID]);
```

## 业务流程

### 项目信息查看流程
1. 用户访问项目基础信息维护页面
2. 系统根据用户权限加载可访问的项目
3. 如果用户只有一个项目权限，直接跳转到项目信息页面
4. 如果用户有多个项目权限，显示项目选择树
5. 用户选择项目后，加载项目详细信息

### 项目信息编辑流程
1. 用户在项目信息页面点击编辑
2. 系统验证用户是否有编辑权限
3. 用户修改项目基础信息
4. 上传背景图片或印章文件（可选）
5. 系统验证数据完整性
6. 保存项目信息到数据库
7. 更新相关缓存和索引

### 背景图片管理流程
1. 用户选择背景图片文件上传
2. 系统验证文件格式和大小
3. 保存图片到指定目录（Attachment）
4. 自动获取图片宽度和高度
5. 更新数据库中的图片路径和尺寸信息

### 项目层级管理流程
1. 设置项目的父级单位（PID）
2. 确定项目等级（UnitLevel）
3. 维护项目层级关系
4. 更新项目树形结构缓存

## 权限控制

### 功能权限
- 项目信息查看权限
- 项目信息编辑权限
- 背景图片上传权限
- 印章文件管理权限
- 项目配置修改权限

### 权限验证机制
```csharp
// 权限检查逻辑
IsOperate = 0;
if (SessionManager.CurrentUserAccount.UserType != null)//项目架构新增，修改，删除
{
    UTBasePostAuth UTBasePostAuthEnt = serviceUTBasePostAuth.GetFirst(ExpressionWrapper<UTBasePostAuth>.Where(x => x.MenuValue == 7));
    if (UTBasePostAuthEnt != null)
    {
        IList<long?> UserTypeList = serviceUTBasePostAuthDetail.ListBy(ExpressionWrapper<UTBasePostAuthDetail>.Where(x => x.PostAuthID == UTBasePostAuthEnt.ID && x.UserID != null)).Select(x => x.UserID).Distinct().ToList();
        if (UserTypeList != null && UserTypeList.Contains(SessionManager.CurrentUserAccount.UserID))
        {
            IsOperate = 1;
        }
    }
}
```

### 数据权限
- 按项目隔离数据访问
- 用户只能查看和编辑所属项目信息
- 集团管理员可以管理所有项目
- 分公司管理员可以管理下属项目

## 特殊功能

### 地图坐标管理
- 通过PointX、PointY字段存储项目地理坐标
- 支持地图定位和导航功能
- 用于移动端位置服务

### 考勤机集成
- 通过KQSN字段配置考勤机编号
- 支持考勤数据的自动同步
- 关联考勤管理模块

### 设备巡查人员配置
- 通过EqUserIDStr、EqUserNameStr字段配置默认巡查人员
- 支持多人配置（逗号分隔）
- 用于设备巡检任务的自动分配

### 印章文件管理
- 支持项目印章文件的上传和管理
- 用于各类审批文档的印章使用
- 文件安全存储和访问控制

## 技术特点

1. **多项目支持**: 支持集团-分公司-项目的多级架构
2. **文件管理**: 集成背景图片和印章文件管理
3. **坐标定位**: 支持地图坐标和位置服务
4. **权限控制**: 基于岗位的细粒度权限控制
5. **数据验证**: 完整的数据验证和错误处理
6. **缓存机制**: 项目信息缓存提升性能

## 扩展功能

1. **项目模板**: 支持项目信息模板化管理
2. **批量导入**: 支持Excel批量导入项目信息
3. **项目统计**: 提供项目基础数据统计分析
4. **地图集成**: 集成地图服务和位置展示
5. **移动端**: 支持移动端项目信息查看
6. **API接口**: 提供项目信息的API接口

## 关联模块

1. **组织架构**: 项目下的部门组织管理
2. **建筑物管理**: 项目下的建筑物信息
3. **用户管理**: 项目用户权限控制
4. **设备管理**: 项目设备设施管理
5. **考勤管理**: 项目考勤机配置
6. **巡检管理**: 项目巡检人员配置

## 数据字典

### 单位等级 (UnitLevel)
- 0: 集团
- 1: 分公司
- 2: 项目

### 单位分类 (UnitClass)
- 根据业务需要定义不同的项目分类

### 是否项目 (IsProject)
- 0: 非项目（集团、分公司）
- 1: 项目

## 性能优化

1. **索引优化**: 在PID、UnitLevel等字段建立索引
2. **缓存机制**: 项目基础信息缓存
3. **图片压缩**: 背景图片自动压缩处理
4. **延迟加载**: 大文件延迟加载
5. **CDN加速**: 静态文件CDN加速

## 安全控制

1. **文件上传**: 严格的文件类型和大小限制
2. **路径安全**: 防止路径遍历攻击
3. **权限验证**: 多层次权限验证机制
4. **数据加密**: 敏感信息加密存储
5. **审计日志**: 操作日志记录和审计
