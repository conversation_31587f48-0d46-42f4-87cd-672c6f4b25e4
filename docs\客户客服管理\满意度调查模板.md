# 满意度调查模板模块分析

## 模块概述

满意度调查模板模块是客户客服管理系统中用于创建和管理满意度调查问卷模板的核心模块。该模块提供了灵活的问卷设计功能，支持多种题型、分类管理和评分机制，为后续的满意度调查活动提供标准化的问卷模板。

## 路由信息

- **子菜单名**: 满意度调查模板
- **路由地址**: ~/ModuleKF/SatisfactionClass/Index
- **模块名**: ModuleKF
- **视图名**: SatisfactionClass

## 页面逻辑分析

### 主要功能

1. **调查分类管理**
   - 树形结构的分类管理
   - 支持多级分类嵌套
   - 分类的增删改查操作

2. **问题模板管理**
   - 选择题和简答题支持
   - 问题排序和评分设置
   - 问题选项配置

3. **答案选项管理**
   - 多选项答案配置
   - 答案评分设置
   - 答案排序管理

4. **模板复用**
   - 模板的复制和引用
   - 跨项目模板共享
   - 模板版本管理

### 控制器分析

#### SatisfactionClassController (ModuleKF)

**主要方法**:
- `Index()`: 满意度调查分类主页面
- `Create()`: 创建/编辑分类页面
- `LoadRoot()`: 加载根节点树形数据
- `LoadChildren()`: 加载子节点数据

**核心业务逻辑**:
```csharp
// 分类创建和保存
[HttpPost]
public ActionResult Create(ModelSatisfactionClassCreate model, FormCollection collection)
{
    try
    {
        model.Save();
        return Content(WebTools.ScriptCloseDialog(DialogOption.GetDefaultInstance(new DialogOption()
        {
            RefreshOpener = false,
            CallbackFromOpener = "callback",
            CallbackFromOpenerParameter = string.Format("'{0}','{1}','{2}','{3}'", 
                (int)model.PageState, 
                model.UTUnitSatisfactionClassEnt.ID, 
                model.UTUnitSatisfactionClassEnt.ClassName, 
                model.PClassID)
        })));
    }
    catch
    {
        Error = "操作失败";
        model.RetriveData();
    }
    return View(model);
}

// 树形结构加载
[OutputCache(NoStore = true, Duration = 0)]
public JsonResult LoadRoot()
{
    IList<JsonTreeData> treeDataCollection = new List<JsonTreeData>();
    IList<UTUnitSatisfactionClass> nextLevelPeojects = serviceUTUnitSatisfactionClass.ListBy(ExpressionWrapper<UTUnitSatisfactionClass>.Where(x => x.ClassName != null));
    JsonTreeData temp = new JsonTreeData()
    {
        id = "root" + Guid.Empty.ToString(),
        text = "满意度模板",
        children = nextLevelPeojects.Count(x => (x.PClassID == null || x.PClassID == Guid.Empty)) > 0,
        type = "root"
    };
    treeDataCollection.Add(temp);
    return Json(treeDataCollection, JsonRequestBehavior.AllowGet);
}
```

#### SatisfactionClassQuestionController (ModuleKF)

**主要方法**:
- `Index()`: 问题列表页面
- `Create()`: 创建/编辑问题页面

**核心业务逻辑**:
```csharp
// 问题创建和保存
[HttpPost]
public ActionResult Create(ModelSatisfactionClassQuestionCreate model, FormCollection collection)
{
    try
    {
        model.Save();
        return Content(WebTools.ScriptCloseDialog(DialogOption.GetDefaultInstance(new DialogOption()
        {
            HighlightData = model.UTUnitSatisfactionClassQuestionEnt.ID,
            RefreshOpener = true,
            CallbackFromOpener = "callBackFromUser"
        })));
    }
    catch
    {
        Error = "操作失败";
        model.RetriveData();
    }
    return View(model);
}
```

### 模型分析

#### ModelSatisfactionClassCreate
- 负责满意度调查分类的创建和编辑
- 支持树形结构的父子关系管理
- 处理分类层级和排序

#### ModelSatisfactionClassQuestionCreate
- 负责调查问题的创建和编辑
- 支持选择题和简答题两种类型
- 处理问题选项和评分配置

## 数据表结构

### 主表: UT_Unit_SatisfactionClass (满意度调查分类)

| 字段名 | 数据类型 | 长度 | 允许空 | 说明 |
|--------|----------|------|--------|------|
| ID | uniqueidentifier | - | NO | 主键ID |
| UnitID | uniqueidentifier | - | YES | 项目ID |
| PClassID | uniqueidentifier | - | YES | 父分类ID |
| ClassName | nvarchar | 50 | YES | 分类名称 |
| ClassLevel | int | - | YES | 分类层级 |
| IsLast | int | - | YES | 是否叶子节点 |
| OrderIndex | int | - | YES | 排序索引 |
| Remark | nvarchar | 500 | YES | 备注 |
| CreateDate | datetime | - | YES | 创建时间 |
| CreateUser | bigint | - | YES | 创建用户 |

**建表SQL**:
```sql
create table [dbo].[UT_Unit_SatisfactionClass] (  
    [ID] uniqueidentifier  NOT NULL ,  
    [UnitID] uniqueidentifier  NULL ,  
    [PClassID] uniqueidentifier  NULL ,  
    [ClassName] nvarchar(50)  NULL ,  
    [ClassLevel] int  NULL ,  
    [IsLast] int  NULL ,  
    [OrderIndex] int  NULL ,  
    [Remark] nvarchar(500)  NULL ,  
    [CreateDate] datetime  NULL ,  
    [CreateUser] bigint  NULL 
);  
ALTER TABLE [dbo].[UT_Unit_SatisfactionClass] ADD CONSTRAINT PK_UT_Unit_SatisfactionClass PRIMARY KEY  ([ID]);
```

### 问题表: UT_Unit_SatisfactionClassQuestion (满意度调查类问题)

| 字段名 | 数据类型 | 长度 | 允许空 | 说明 |
|--------|----------|------|--------|------|
| ID | uniqueidentifier | - | NO | 主键ID |
| SatisfactionClassID | uniqueidentifier | - | YES | 分类ID |
| QuestionType | int | - | NO | 题目类型（1选择题，2简答题） |
| QuestionName | nvarchar | 50 | NO | 题目名称 |
| Remark | nvarchar | 50 | YES | 备注 |
| CreateDate | datetime | - | YES | 创建时间 |
| CreateUser | bigint | - | YES | 创建用户 |
| OrderIndex | int | - | YES | 排序索引 |
| Score | decimal | 18,1 | YES | 得分 |

**建表SQL**:
```sql
create table [dbo].[UT_Unit_SatisfactionClassQuestion] (  
    [ID] uniqueidentifier  NOT NULL ,  
    [SatisfactionClassID] uniqueidentifier  NULL ,  
    [QuestionType] int  NOT NULL ,  
    [QuestionName] nvarchar(50)  NOT NULL ,  
    [Remark] nvarchar(50)  NULL ,  
    [CreateDate] datetime  NULL ,  
    [CreateUser] bigint  NULL ,  
    [OrderIndex] int  NULL ,  
    [Score] decimal(18, 1)  NULL 
);  
ALTER TABLE [dbo].[UT_Unit_SatisfactionClassQuestion] ADD CONSTRAINT PK_UT_Unit_SatisfactionClassQuestion PRIMARY KEY  ([ID]);
```

### 答案选项表: UT_Unit_SatisfactionClassQuestionAnswer (满意度调查类问题选项)

| 字段名 | 数据类型 | 长度 | 允许空 | 说明 |
|--------|----------|------|--------|------|
| ID | uniqueidentifier | - | NO | 主键ID |
| QuestionID | uniqueidentifier | - | YES | 问题ID |
| AnswerTitle | nvarchar | 50 | YES | 答案标题 |
| AnswerValue | nvarchar | MAX | YES | 答案内容 |
| Remark | nvarchar | 50 | YES | 备注 |
| OrderIndex | int | - | YES | 排序索引 |
| CreateDate | datetime | - | YES | 创建时间 |
| CreateUser | bigint | - | YES | 创建用户 |
| Score | decimal | 18,1 | YES | 得分 |

**建表SQL**:
```sql
create table [dbo].[UT_Unit_SatisfactionClassQuestionAnswer] (  
    [ID] uniqueidentifier  NOT NULL ,  
    [QuestionID] uniqueidentifier  NULL ,  
    [AnswerTitle] nvarchar(50)  NULL ,  
    [AnswerValue] nvarchar(MAX)  NULL ,  
    [Remark] nvarchar(50)  NULL ,  
    [OrderIndex] int  NULL ,  
    [CreateDate] datetime  NULL ,  
    [CreateUser] bigint  NULL ,  
    [Score] decimal(18, 1)  NULL 
);  
ALTER TABLE [dbo].[UT_Unit_SatisfactionClassQuestionAnswer] ADD CONSTRAINT PK_UT_Unit_SatisfactionClassQuestionAnswer PRIMARY KEY  ([ID]);
```

## 实际调查相关表

### UT_Unit_SatisfactionQuestionAnswer (满意度调查问题答案)
- 用于实际调查中的问题答案存储
- 与模板表结构类似，但用于具体调查实例

### UT_Unit_SatisfactionQuestionAnswerResult (满意度调查问题答案结果)
- 存储用户的具体答题结果
- 关联调查详情和问题答案
- 支持评分和统计分析

## 存储过程

### UP_Unit_SatisfactionStaticsDZ
**功能**: 满意度调查统计分析（大众版）

**参数**:
- @ID uniqueidentifier - 调查ID

**主要功能**:
- 统计各个问题的答题情况
- 计算不同选项的选择人数
- 生成满意度分析报告

**SQL实现**:
```sql
CREATE PROCEDURE [dbo].[UP_Unit_SatisfactionStaticsDZ]
@ID	uniqueidentifier
AS
BEGIN
	SELECT   dbo.UT_Unit_SatisfactionQuestion.ID, dbo.UT_Unit_SatisfactionQuestion.SatisfactionID, 
            dbo.UT_Unit_SatisfactionQuestion.QestionType, dbo.UT_Unit_SatisfactionQuestion.Question, 
            dbo.UT_Unit_SatisfactionQuestion.Lastupdate, dbo.UT_Unit_SatisfactionQuestion.AnswerType,
            dbo.UT_Unit_SatisfactionQuestion.ItemType, 
            dbo.UT_Unit_Satisfaction.UnitID, dbo.UT_Unit_Satisfaction.SatisfactionName, 
            dbo.UT_Unit_SatisfactionQuestion.Score,
            dbo.UT_Unit_Satisfaction.CreateDate AS SatisfactionCreateDate, 
            dbo.UT_Unit_Satisfaction.CustomerType, 
            dbo.UT_Unit_Satisfaction.IsScore, c10.personCount10, b.answerPersonCount, 
            c8.personCount8, c9.personCount9, c5.personCount5, c6.personCount6, 
            c7.personCount7, c2.personCount2, c3.personCount3, c1.personCount1, 
            c4.personCount4
	FROM    dbo.UT_Unit_SatisfactionQuestion INNER JOIN
            dbo.UT_Unit_Satisfaction ON dbo.UT_Unit_SatisfactionQuestion.SatisfactionID = dbo.UT_Unit_Satisfaction.ID 
            LEFT OUTER JOIN
            (SELECT COUNT(1) AS answerPersonCount, SatisfactionQuestionID
             FROM dbo.UT_Unit_SatisfactionQuestionAnswerResult
             GROUP BY SatisfactionQuestionID) AS b ON dbo.UT_Unit_SatisfactionQuestion.ID = b.SatisfactionQuestionID 
            -- 各种评分统计子查询...
	WHERE   (dbo.UT_Unit_Satisfaction.ID = @ID)
	ORDER BY dbo.UT_Unit_SatisfactionQuestion.OrderIndex
END
```

## 业务流程

### 模板创建流程
1. **创建分类**
   - 选择父分类（可选）
   - 输入分类名称和描述
   - 设置分类层级和排序
   - 保存分类信息

2. **设计问题**
   - 选择问题所属分类
   - 选择问题类型（选择题/简答题）
   - 输入问题内容
   - 设置问题评分和排序

3. **配置选项**（选择题）
   - 为选择题添加答案选项
   - 设置每个选项的评分
   - 配置选项排序

4. **模板验证**
   - 检查问题完整性
   - 验证评分设置
   - 确认模板可用性

### 模板使用流程
1. **选择模板**
   - 浏览分类树形结构
   - 选择合适的调查模板
   - 预览模板内容

2. **创建调查**
   - 基于模板创建具体调查
   - 设置调查参数
   - 发布调查活动

3. **数据收集**
   - 用户参与调查答题
   - 系统记录答题结果
   - 实时统计参与情况

4. **结果分析**
   - 生成统计报告
   - 分析满意度趋势
   - 导出分析数据

## 题型支持

### 选择题
- **单选题**: 用户只能选择一个答案
- **多选题**: 用户可以选择多个答案
- **评分题**: 1-10分评分选择
- **满意度题**: 非常满意、满意、一般、不满意、非常不满意

### 简答题
- **文本输入**: 用户自由输入文本答案
- **长文本**: 支持多行文本输入
- **建议意见**: 专门的意见建议收集

## 评分机制

### 评分类型
1. **固定评分**: 每个选项固定分值
2. **权重评分**: 根据重要性设置权重
3. **百分制**: 转换为百分制评分
4. **等级制**: A、B、C、D等级评分

### 统计方式
1. **平均分**: 所有参与者的平均得分
2. **总分**: 累计总得分
3. **分布**: 各分数段的人数分布
4. **排名**: 按得分排序

## 微信集成

### 移动端调查
- 微信端问卷展示
- 移动端友好的交互设计
- 支持图片和语音答题
- 实时提交和保存

### 推送通知
- 调查邀请推送
- 答题提醒通知
- 结果反馈推送

## 权限控制

### 功能权限
- 模板创建权限
- 模板编辑权限
- 模板删除权限
- 问题管理权限
- 分类管理权限

### 数据权限
- 按项目隔离模板数据
- 模板共享权限控制
- 跨项目模板访问权限

## 技术特点

1. **树形结构**: 支持无限层级的分类管理
2. **灵活配置**: 多种题型和评分方式
3. **模板复用**: 高效的模板管理和复用
4. **实时统计**: 动态的数据统计和分析
5. **移动适配**: 完美支持移动端使用
6. **数据导出**: 支持多种格式的数据导出

## 扩展功能

1. **模板市场**: 公共模板库和分享机制
2. **智能推荐**: 基于历史数据的模板推荐
3. **A/B测试**: 支持多版本模板对比测试
4. **自动分析**: AI驱动的满意度分析
5. **预警机制**: 满意度异常自动预警
6. **趋势分析**: 长期满意度趋势分析
