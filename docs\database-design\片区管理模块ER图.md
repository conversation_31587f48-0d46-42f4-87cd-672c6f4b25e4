# 片区管理模块ER图

## 表结构说明

### 核心实体表

#### UTBaseUnitManage (项目基本信息表)
- **主键**: ID (Guid)
- **外键**: PID (父单位ID, Guid?)  
- **核心字段**: UnitCode, UnitName, UnitLevel, CompanyName, UnitAddress

#### UTSYSOrganization (组织机构部门表)
- **主键**: OrgID (Int64)
- **外键**: OrgParentID (父级部门ID, Int64?), UnitID (所属单位ID, Guid?)
- **核心字段**: OrgName, OrgType, OrgNumber, ChargePerson

#### UTUnitBuild (建筑物表)
- **主键**: ID (Guid)
- **外键**: UnitID (项目ID, Guid?)
- **核心字段**: BuildName, BuildType, BuildArea, UsedArea, UpFloor, DownFloor

#### UTEQEquipment (设备信息表)
- **主键**: ID (Guid)
- **外键**: UnitID (所属园区, Guid?), BuildID (区域ID, Guid?), EQClassID (设备类型, Guid?)
- **核心字段**: EQCode, EQName, Manager, EQStatus, InstallSite

#### UTWLItemClass (物料分类表)
- **主键**: ID (Guid)
- **外键**: PClassID (父分类ID, Guid?)
- **核心字段**: ClassCode, ClassName, IsLast, OrderIndex

## ER关系图

```mermaid
erDiagram
    UTBaseUnitManage ||--o{ UTSYSOrganization : "项目包含部门"
    UTBaseUnitManage ||--o{ UTUnitBuild : "项目包含建筑物"
    UTUnitBuild ||--o{ UTEQEquipment : "建筑物内设置设备"
    UTBaseEQClass ||--o{ UTEQEquipment : "设备分类关联设备"
    UTSYSOrganization ||--|| UTSYSOrganization : "部门层级关系"
    UTWLItemClass ||--|| UTWLItemClass : "物料分类层级"
    
    UTBaseUnitManage {
        Guid ID PK
        Guid PID FK "父单位ID" 
        string UnitCode "单位编码"
        string UnitName "单位名称"
        int UnitLevel "单位等级(0集团1分公司2项目)"
        int UnitClass "项目类型"
        string CompanyName "所属公司名称"
        string UnitAddress "地址"
        decimal UnitArea "占地面积"
        decimal BuildArea "建筑面积"
        int64 AdminUserID "管理员ID"
        string Manager "负责人"
        string Tel "联系电话"
        DateTime CreateDate "创建时间"
    }
    
    UTSYSOrganization {
        int64 OrgID PK
        string OrgName "部门名称"
        int OrgType "部门类型"
        string OrgNumber "部门编号" 
        int64 OrgParentID FK "父级部门ID"
        string OrgPhone "部门电话"
        string OrgAddress "机构地址"
        string ChargePerson "负责人"
        string OrgLinkman "部门联系人"
        string LinkPhone "联系电话"
        int Level "层级数"
        Guid UnitID FK "所属单位ID"
        int IsLast "是否最终级别"
        int MaintenanceNY "维修组标识"
    }
    
    UTUnitBuild {
        Guid ID PK
        Guid UnitID FK "项目ID"
        int BuildType "建筑类型(住宅商用办公楼综合体)"
        string BuildName "建筑名称"
        string Contacts "联系人"
        string ContactsTel "联系电话"
        decimal BuildArea "建筑面积"
        decimal UsedArea "使用面积"
        int UpFloor "地上层数"
        int DownFloor "地下层数"
        int BuildLevel "建筑物期数"
        DateTime UpdateDate "更新时间"
        int64 UpdateUser "更新人"
    }
    
    UTEQEquipment {
        Guid ID PK
        Guid UnitID FK "所属园区"
        Guid BuildID FK "区域ID"
        Guid EQClassID FK "设备类型"
        string EQCode "设备编号"
        string EQName "设备名称"
        string Manager "责任人"
        decimal Count "数量"
        string CountUnit "单位"
        int EQStatus "状态"
        string EQType "类型"
        int IsNeedInspection "是否需要巡检"
        string Remark "备注"
        string OutCode "出厂编号"
        string EQModel "型号"
        string TechParam "技术参数"
        string InstallSite "安装位置"
        DateTime InstallDate "安装日期"
        DateTime WarrantyDate "质保日期"
        string Brand "品牌"
        decimal Amount "金额"
    }
    
    UTWLItemClass {
        Guid ID PK
        string ClassCode "分类编码"
        string ClassName "分类名称"
        Guid PClassID FK "父分类ID"
        string AllClassName "全名"
        int IsLast "是否最终类型"
        int IsDelete "是否删除"
        string Remark "备注"
        int OrderIndex "排序"
    }
    
    UTBaseEQClass {
        Guid ID PK
        Guid EQSystemID FK "所属系统"
        int ClassLevel "分类级别"
        Guid PClassID FK "上级分类"
        string ClassCode "分类编号"
        string ClassName "分类名称"
        int IsLast "是否最终类型"
        int IsDayCheck "是否日检"
        int IsWeekCheck "是否周检"
        int IsMonthCheck "是否月检"
        int IsQuarterCheck "是否季检"
        int IsYearCheck "是否年检"
    }
```

## 业务关系说明

### 片区管理核心业务流程

1. **项目基本信息管理**
   - UTBaseUnitManage表存储项目的基本信息
   - 包括项目名称、地址、面积、负责人等关键信息
   - 支持项目层级结构(集团-分公司-项目)

2. **组织架构管理**  
   - UTSYSOrganization表管理项目下的部门结构
   - 支持部门层级关系，通过OrgParentID实现树形结构
   - UnitID关联到具体项目，实现项目独立的组织架构

3. **建筑物管理**
   - UTUnitBuild表管理项目下的建筑物信息
   - 支持不同建筑类型：住宅、商用、办公楼、综合体
   - 记录建筑面积、层数、联系人等详细信息

4. **设备设施管理**
   - UTEQEquipment表管理项目下的所有设备
   - 通过BuildID关联到具体建筑物
   - 通过EQClassID关联到设备分类
   - 支持设备的全生命周期管理：安装、维保、报废

5. **物料分类管理**
   - UTWLItemClass表支持多级物料分类
   - 通过PClassID实现父子关系
   - 支持分类的启用/禁用状态管理

### 关键业务约束

1. **数据完整性约束**
   - 设备必须关联到具体建筑物和设备分类
   - 部门必须关联到具体项目
   - 建筑物必须关联到具体项目

2. **业务逻辑约束**
   - 项目级别的设备只能安装在该项目的建筑物内
   - 部门层级不能形成循环引用
   - 物料分类层级不能超过预设限制

3. **权限控制约束**
   - 项目级用户只能管理本项目的数据
   - 不同角色对设备的操作权限不同
   - 物料分类的修改需要特定权限 