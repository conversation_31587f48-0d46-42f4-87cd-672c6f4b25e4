	using System.Collections.Generic; 
	using System.Text; 
	using System;
	using System.ServiceModel;
	using System.ServiceModel.Web;
	using NHibernate;
	using NHibernate.Linq;
	using Project.Common;
	using Project.Dal.Base;
	using Project.Biz.Base;
	using PropertySys_ZGHQ.ProcedureEntity;
	using PropertySys_ZGHQ.ProcedureDal;

	namespace PropertySys_ZGHQ.ProcedureBiz
	{
		public class  BizUPSearchHDData : BaseSPBiz
		{
			private DalUPSearchHDData dalUPSearchHDData;
		
			private  BizUPSearchHDData()
			{
				dalUPSearchHDData = DalFactory.Get<DalUPSearchHDData>();
			}

			[OperationContract]
			[WebInvoke(BodyStyle = WebMessageBodyStyle.Wrapped)]
			public IList<UPSearchHDData> Invoke(UPSearchHDDataParameter parameter)
			{
									var result = dalUPSearchHDData.Invoke(parameter);
					return result;
							}
		}
	}

	