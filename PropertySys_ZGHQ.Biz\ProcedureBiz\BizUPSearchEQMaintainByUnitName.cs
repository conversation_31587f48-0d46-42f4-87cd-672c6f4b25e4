	using System.Collections.Generic; 
	using System.Text; 
	using System;
	using System.ServiceModel;
	using System.ServiceModel.Web;
	using NHibernate;
	using NHibernate.Linq;
	using Project.Common;
	using Project.Dal.Base;
	using Project.Biz.Base;
	using PropertySys_ZGHQ.ProcedureEntity;
	using PropertySys_ZGHQ.ProcedureDal;

	namespace PropertySys_ZGHQ.ProcedureBiz
	{
		public class  BizUPSearchEQMaintainByUnitName : BaseSPBiz
		{
			private DalUPSearchEQMaintainByUnitName dalUPSearchEQMaintainByUnitName;
		
			private  BizUPSearchEQMaintainByUnitName()
			{
				dalUPSearchEQMaintainByUnitName = DalFactory.Get<DalUPSearchEQMaintainByUnitName>();
			}

			[OperationContract]
			[WebInvoke(BodyStyle = WebMessageBodyStyle.Wrapped)]
			public IList<UPSearchEQMaintainByUnitName> Invoke(UPSearchEQMaintainByUnitNameParameter parameter)
			{
									var result = dalUPSearchEQMaintainByUnitName.Invoke(parameter);
					return result;
							}
		}
	}

	