# 工作联系单审批

## 基本信息
- **子菜单名**: 工作联系单审批
- **路由**: ~/ModuleXZ/WorkOrder/MainSubmitIndex
- **模块名**: ModuleXZ
- **视图名**: WorkOrder
- **功能描述**: 用于审批工作联系单，管理工作任务的审核流程

## 页面逻辑分析

### 主要Controller方法
**WorkOrderController.cs**

1. **MainSubmitIndex(ModelWorkOrderIndex model)** - 工作联系单审批主页
   - 功能：显示待审批的工作联系单列表
   - 逻辑：返回审批主页视图

2. **DealIndex(ModelWorkOrderDealIndex model)** - 工作联系单审核查询
   - 功能：显示当前用户需要审核的工作联系单
   - 逻辑：调用model.RetriveData()获取待审核数据

3. **DealCreate(ModelWorkOrderDealCreate model)** - 工作联系单审核
   - 功能：审核工作联系单
   - 逻辑：调用model.RetriveData()初始化审核数据

4. **DealCreate(POST)** - 提交审核结果
   - 功能：保存审核意见和结果
   - 逻辑：
     - 调用model.Submit()提交审核结果
     - 返回对话框关闭脚本

5. **NoPassIndex(ModelWorkOrderNoPassIndex model)** - 审核不通过列表
   - 功能：显示审核不通过的工作联系单
   - 逻辑：调用model.RetriveData()获取不通过的数据

### 主要Model类
**ModelWorkOrder.cs**

1. **ModelWorkOrderDealIndex** - 工作联系单审核列表模型
   - 属性：包含审核状态、审核人等搜索条件
   - 方法：RetriveData()获取待审核工作联系单列表

2. **ModelWorkOrderDealCreate** - 工作联系单审核模型
   - 属性：
     - UTXZWorkOrderEntity：工作联系单实体
     - UTBaseProcessOpDetialEnt：审核操作详情实体
   - 方法：
     - RetriveData()：初始化审核数据
     - Submit()：提交审核结果

3. **ModelWorkOrderNoPassIndex** - 审核不通过列表模型
   - 功能：显示审核不通过的工作联系单

## 数据库使用情况

### 主要数据表
1. **UTXZWorkOrder** - 工作联系单主表
   - 审批相关字段：
     - Status：当前状态
     - CurrentApprovalUser：当前审批人
     - ProcessCode：流程代码
   - 操作：状态更新、审批人设置

2. **UTBaseProcessOpDetial** - 审批操作详情表
   - 字段：
     - ProcessCode：流程代码
     - OperateStepCode：操作步骤代码
     - OperateInfo：操作说明
     - ApprovalResult：审批结果
     - OperateUser：操作用户
   - 操作：记录审批历史

3. **UTUnitWorkFlowDetailNew** - 工作流详情表
   - 字段：流程步骤、审批人员配置等
   - 操作：流程控制、下一步审批人确定

### 主要Service接口
1. **IServiceUTXZWorkOrder** - 工作联系单服务
   - 审批相关方法：
     - SubmitInfo()：提交审批信息
     - ApprovalOrderPass()：审批通过
     - ApprovalOrderNoPass()：审批不通过

2. **IServiceUTUnitWorkFlowDetailNew** - 工作流服务
   - 方法：流程控制、审批流转

## 业务流程
1. **提交审批**：工作联系单提交后进入审批流程
2. **审批分配**：系统根据流程配置分配审批人
3. **审批处理**：
   - 审批人查看工作联系单详情
   - 填写审批意见
   - 选择审批结果（通过/不通过）
4. **流程流转**：
   - 通过：流转到下一审批节点或完成
   - 不通过：退回申请人修改
5. **状态更新**：更新工作联系单状态

## 审批权限控制
- **审批人权限**：只有指定的审批人才能审批
- **状态控制**：只有特定状态的工作联系单才能审批
- **流程控制**：按照预定义的审批流程执行

## 审批结果类型
- **通过**：审批通过，流转到下一节点
- **不通过**：审批不通过，退回申请人
- **转办**：转给其他人审批

## 相关枚举
- **EnumApprovalResult**：审批结果枚举
- **EnumProcessCode**：流程代码枚举
- **EnumXZStatus**：工作联系单状态枚举
