	using System.Collections.Generic; 
	using System.Text; 
	using System;
	using System.ServiceModel;
	using System.ServiceModel.Web;
	using NHibernate;
	using NHibernate.Linq;
	using Project.Common;
	using Project.Dal.Base;
	using Project.Biz.Base;
	using PropertySys_ZGHQ.ProcedureEntity;
	using PropertySys_ZGHQ.ProcedureDal;

	namespace PropertySys_ZGHQ.ProcedureBiz
	{
		public class  BizUPSearchFireFaultMonthStatics : BaseSPBiz
		{
			private DalUPSearchFireFaultMonthStatics dalUPSearchFireFaultMonthStatics;
		
			private  BizUPSearchFireFaultMonthStatics()
			{
				dalUPSearchFireFaultMonthStatics = DalFactory.Get<DalUPSearchFireFaultMonthStatics>();
			}

			[OperationContract]
			[WebInvoke(BodyStyle = WebMessageBodyStyle.Wrapped)]
			public IList<UPSearchFireFaultMonthStatics> Invoke(UPSearchFireFaultMonthStaticsParameter parameter)
			{
									var result = dalUPSearchFireFaultMonthStatics.Invoke(parameter);
					return result;
							}
		}
	}

	