# 物料管理模块UML类图

## 类结构说明

### 1. 核心业务类

#### WLItemService (物料管理服务类)
- **职责**：处理物料信息的增删改查和业务逻辑
- **主要方法**：
  - `CreateItem()`: 创建物料信息
  - `UpdateItem()`: 更新物料信息
  - `DeleteItem()`: 删除物料信息
  - `GetItemById()`: 根据ID获取物料详情
  - `GetItemsByClass()`: 按分类查询物料列表
  - `SearchItems()`: 物料模糊搜索
  - `GenerateItemCode()`: 生成物料编码
  - `CheckAlarmQuantity()`: 检查库存报警
  - `ImportItems()`: 批量导入物料

#### WLItemClassService (物料分类服务类)
- **职责**：管理物料分类的树形结构和相关业务
- **主要方法**：
  - `CreateClass()`: 创建物料分类
  - `UpdateClass()`: 更新分类信息
  - `DeleteClass()`: 删除分类（检查是否有关联物料）
  - `GetClassTree()`: 获取分类树结构
  - `GetClassPath()`: 获取分类完整路径
  - `GenerateClassCode()`: 生成分类编码
  - `CheckIsLeafClass()`: 检查是否为叶子节点
  - `SortClassOrder()`: 分类排序管理

#### WLStockService (库存管理服务类)
- **职责**：处理物料库存的增减和查询统计
- **主要方法**：
  - `AddStock()`: 增加库存（入库）
  - `ReduceStock()`: 减少库存（出库）
  - `TransferStock()`: 库存调拨
  - `GetStockByItem()`: 查询物料库存
  - `GetStockByWarehouse()`: 查询仓库库存
  - `CheckLowStock()`: 检查低库存
  - `CalculateAvgPrice()`: 计算平均价格
  - `GetStockHistory()`: 获取库存历史

#### WLWareHouseService (仓库管理服务类)
- **职责**：管理仓库基础信息和相关业务
- **主要方法**：
  - `CreateWareHouse()`: 创建仓库
  - `UpdateWareHouse()`: 更新仓库信息
  - `DeleteWareHouse()`: 删除仓库（检查是否有库存）
  - `GetWareHousesByProject()`: 按项目查询仓库
  - `GenerateWHCode()`: 生成仓库编码
  - `CheckWareHouseCapacity()`: 检查仓库容量

### 2. 实体类

#### WLItem (物料实体)
- **属性**：物料编号、名称、规格、品牌、分类、价格、单位、报警值等
- **关系**：多对一关联物料分类，一对多关联库存记录

#### WLItemClass (物料分类实体)
- **属性**：分类编码、名称、父分类、完整路径、层级等
- **关系**：树形结构，一对多关联物料和子分类

#### WLStock (库存实体)
- **属性**：库存数量、单价、单位、更新时间等
- **关系**：多对一关联物料和仓库

#### WLWareHouse (仓库实体)
- **属性**：仓库编码、名称、描述、项目等
- **关系**：一对多关联库存记录

### 3. 工具类

#### ItemCodeGenerator (物料编码生成器)
- **职责**：生成唯一的物料编码
- **主要方法**：
  - `GenerateCode()`: 生成物料编码
  - `ValidateCode()`: 验证编码唯一性
  - `GetNextSequence()`: 获取下一个序号

#### StockAlarmManager (库存报警管理器)
- **职责**：处理库存报警相关逻辑
- **主要方法**：
  - `CheckAlarmConditions()`: 检查报警条件
  - `SendAlarmNotification()`: 发送报警通知
  - `GetAlarmItems()`: 获取报警物料列表
  - `UpdateAlarmSettings()`: 更新报警设置

#### DataImportExporter (数据导入导出器)
- **职责**：处理物料数据的批量导入导出
- **主要方法**：
  - `ImportFromExcel()`: 从Excel导入数据
  - `ExportToExcel()`: 导出到Excel
  - `ValidateImportData()`: 验证导入数据
  - `GenerateImportReport()`: 生成导入报告

## UML类图

```mermaid
classDiagram
    %% 服务层类
    class WLItemService {
        +CreateItem(item: WLItem) : Result
        +UpdateItem(item: WLItem) : Result
        +DeleteItem(itemId: string) : Result
        +GetItemById(itemId: string) : WLItem
        +GetItemsByClass(classId: string) : List~WLItem~
        +SearchItems(keyword: string, projectId: string) : List~WLItem~
        +GenerateItemCode(classId: string, projectId: string) : string
        +CheckAlarmQuantity(itemId: string) : AlarmInfo
        +ImportItems(excelFile: FileData, projectId: string) : ImportResult
        +UpdateItemPrice(itemId: string, newPrice: decimal) : Result
    }
    
    class WLItemClassService {
        +CreateClass(itemClass: WLItemClass) : Result
        +UpdateClass(itemClass: WLItemClass) : Result
        +DeleteClass(classId: string) : Result
        +GetClassTree(projectId: string) : TreeNode~WLItemClass~
        +GetClassPath(classId: string) : string
        +GenerateClassCode(parentId: string) : string
        +CheckIsLeafClass(classId: string) : boolean
        +SortClassOrder(classIds: List~string~) : Result
        +GetSubClasses(parentId: string) : List~WLItemClass~
    }
    
    class WLStockService {
        +AddStock(stockOperation: StockOperation) : Result
        +ReduceStock(stockOperation: StockOperation) : Result
        +TransferStock(transferInfo: StockTransfer) : Result
        +GetStockByItem(itemId: string) : List~WLStock~
        +GetStockByWarehouse(warehouseId: string) : List~WLStock~
        +CheckLowStock(projectId: string) : List~LowStockAlert~
        +CalculateAvgPrice(itemId: string, warehouseId: string) : decimal
        +GetStockHistory(itemId: string, startDate: DateTime, endDate: DateTime) : List~StockRecord~
        +UpdateStockPrice(stockId: string, price: decimal) : Result
    }
    
    class WLWareHouseService {
        +CreateWareHouse(warehouse: WLWareHouse) : Result
        +UpdateWareHouse(warehouse: WLWareHouse) : Result
        +DeleteWareHouse(warehouseId: string) : Result
        +GetWareHousesByProject(projectId: string) : List~WLWareHouse~
        +GenerateWHCode(projectId: string) : string
        +CheckWareHouseCapacity(warehouseId: string) : CapacityInfo
        +GetWareHouseStatistics(warehouseId: string) : WHStatistics
    }
    
    %% 实体类
    class WLItem {
        +Id: string
        +UnitID: string
        +ItemCode: string
        +ItemName: string
        +ItemModel: string
        +Brand: string
        +ItemClassID: string
        +ItemClass: string
        +Price: decimal
        +Unit: string
        +AlarmQty: decimal
        +MaxAlarmQty: decimal
        +Remark: string
        +OldItemCode: string
        +IsDelete: int
        +CreateDate: DateTime
        +CreateUser: long
        +UpdateDate: DateTime
        +UpdateUser: long
    }
    
    class WLItemClass {
        +Id: string
        +ClassCode: string
        +ClassName: string
        +PClassID: string
        +PClassCode: string
        +AllClassName: string
        +IsLast: int
        +OrderIndex: int
        +IsDelete: int
        +Remark: string
        +Children: List~WLItemClass~
        +Items: List~WLItem~
    }
    
    class WLStock {
        +Id: string
        +WHID: string
        +ItemID: string
        +TotalQty: decimal
        +Price: decimal
        +Unit: string
        +ItemCode: string
        +ItemName: string
        +ItemModel: string
        +Brand: string
        +WHCode: string
        +WHName: string
        +UnitID: string
        +LastUpdateDate: DateTime
    }
    
    class WLWareHouse {
        +Id: string
        +UnitID: string
        +WHCode: string
        +WHName: string
        +WHDesc: string
        +IsDelete: int
        +StockList: List~WLStock~
    }
    
    %% 工具类
    class ItemCodeGenerator {
        +GenerateCode(classCode: string, projectCode: string) : string
        +ValidateCode(itemCode: string, projectId: string) : boolean
        +GetNextSequence(prefix: string, projectId: string) : int
        +ParseCodeComponents(itemCode: string) : CodeComponents
    }
    
    class StockAlarmManager {
        +CheckAlarmConditions(projectId: string) : List~AlarmInfo~
        +SendAlarmNotification(alarmInfo: AlarmInfo) : Result
        +GetAlarmItems(projectId: string, alarmType: AlarmType) : List~WLItem~
        +UpdateAlarmSettings(itemId: string, minQty: decimal, maxQty: decimal) : Result
        +GenerateAlarmReport(projectId: string, period: TimePeriod) : AlarmReport
    }
    
    class DataImportExporter {
        +ImportFromExcel(filePath: string, projectId: string) : ImportResult
        +ExportToExcel(items: List~WLItem~, filePath: string) : ExportResult
        +ValidateImportData(importData: List~ImportItemData~) : ValidationResult
        +GenerateImportReport(importResult: ImportResult) : ReportData
        +CreateImportTemplate() : ExcelTemplate
    }
    
    %% 枚举类
    class AlarmType {
        <<enumeration>>
        +LowStock
        +HighStock
        +ZeroStock
        +ExpiredAlert
    }
    
    class OperationType {
        <<enumeration>>
        +StockIn
        +StockOut
        +Transfer
        +Adjustment
    }
    
    %% 关系定义
    WLItemClass ||--o{ WLItemClass : "父子分类"
    WLItemClass ||--o{ WLItem : "分类包含物料"
    WLItem ||--o{ WLStock : "物料库存"
    WLWareHouse ||--o{ WLStock : "仓库库存"
    
    WLItemService ..> WLItem : "操作"
    WLItemService ..> ItemCodeGenerator : "使用"
    WLItemService ..> DataImportExporter : "使用"
    WLItemClassService ..> WLItemClass : "操作"
    WLStockService ..> WLStock : "操作"
    WLStockService ..> StockAlarmManager : "使用"
    WLWareHouseService ..> WLWareHouse : "操作"
    
    StockAlarmManager ..> AlarmType : "使用"
    WLStockService ..> OperationType : "使用"
```

## 业务逻辑设计

### 1. 物料管理流程

```mermaid
sequenceDiagram
    participant User as 用户
    participant ItemService as WLItemService
    participant ClassService as WLItemClassService
    participant CodeGen as ItemCodeGenerator
    participant DB as 数据库
    
    User->>ClassService: 创建物料分类
    ClassService->>DB: 保存分类信息
    ClassService-->>User: 返回分类ID
    
    User->>ItemService: 创建物料
    ItemService->>ClassService: 验证分类是否为叶子节点
    ClassService-->>ItemService: 返回验证结果
    ItemService->>CodeGen: 生成物料编码
    CodeGen-->>ItemService: 返回唯一编码
    ItemService->>DB: 保存物料信息
    ItemService-->>User: 返回创建结果
    
    User->>ItemService: 查询物料
    ItemService->>DB: 执行查询
    DB-->>ItemService: 返回物料列表
    ItemService-->>User: 返回查询结果
```

### 2. 库存管理流程

```mermaid
flowchart TD
    A[库存操作请求] --> B{操作类型}
    B -->|入库| C[增加库存]
    B -->|出库| D[减少库存]
    B -->|调拨| E[库存转移]
    
    C --> F[更新库存数量]
    D --> G{库存是否充足}
    G -->|是| F
    G -->|否| H[返回库存不足错误]
    
    E --> I[减少源仓库库存]
    I --> J[增加目标仓库库存]
    J --> F
    
    F --> K[计算平均价格]
    K --> L[更新最后更新时间]
    L --> M[检查报警条件]
    M --> N{是否触发报警}
    N -->|是| O[发送报警通知]
    N -->|否| P[操作完成]
    O --> P
```

### 3. 分类管理流程

```mermaid
graph TB
    A[分类管理请求] --> B{操作类型}
    B -->|创建| C[新增分类]
    B -->|修改| D[更新分类]
    B -->|删除| E[删除分类]
    
    C --> F[生成分类编码]
    F --> G[设置父子关系]
    G --> H[更新完整路径]
    H --> I[保存分类信息]
    
    D --> J[验证分类存在]
    J --> K[更新分类信息]
    K --> L[同步更新物料表]
    L --> M[重新计算完整路径]
    
    E --> N[检查是否有子分类]
    N --> O{有子分类?}
    O -->|是| P[禁止删除]
    O -->|否| Q[检查是否有关联物料]
    Q --> R{有关联物料?}
    R -->|是| P
    R -->|否| S[执行删除]
    
    I --> T[操作完成]
    M --> T
    S --> T
    P --> U[返回错误信息]
```

## 设计模式应用

### 1. 工厂模式 - 服务对象创建
```java
interface WLServiceFactory {
    WLItemService createItemService();
    WLStockService createStockService();
    WLItemClassService createClassService();
}

class DefaultWLServiceFactory implements WLServiceFactory {
    public WLItemService createItemService() {
        return new WLItemServiceImpl();
    }
    // 其他服务创建方法...
}
```

### 2. 策略模式 - 库存操作处理
```java
interface StockOperationStrategy {
    Result executeOperation(StockOperation operation);
}

class StockInStrategy implements StockOperationStrategy {
    // 入库操作逻辑
}

class StockOutStrategy implements StockOperationStrategy {
    // 出库操作逻辑
}

class StockTransferStrategy implements StockOperationStrategy {
    // 调拨操作逻辑
}
```

### 3. 观察者模式 - 库存报警通知
```java
interface StockAlarmObserver {
    void onAlarmTriggered(AlarmInfo alarmInfo);
}

class EmailNotificationObserver implements StockAlarmObserver {
    // 邮件通知实现
}

class SMSNotificationObserver implements StockAlarmObserver {
    // 短信通知实现
}

class SystemNotificationObserver implements StockAlarmObserver {
    // 系统内通知实现
}
```

### 4. 模板方法模式 - 数据导入处理
```java
abstract class DataImportTemplate {
    public final ImportResult importData(String filePath) {
        validateFile(filePath);
        List<RawData> rawData = readFile(filePath);
        List<ValidatedData> validatedData = validateData(rawData);
        ImportResult result = processData(validatedData);
        generateReport(result);
        return result;
    }
    
    protected abstract List<RawData> readFile(String filePath);
    protected abstract List<ValidatedData> validateData(List<RawData> rawData);
    protected abstract ImportResult processData(List<ValidatedData> data);
}
```

## 扩展性设计

### 1. 插件化分类管理
- 支持自定义分类属性
- 可扩展的分类规则配置
- 支持第三方分类标准集成

### 2. 多样化库存策略
- 可配置的库存计算方法
- 支持不同的价格策略（FIFO、LIFO、加权平均）
- 灵活的报警规则设置

### 3. 智能化物料管理
- 基于历史数据的需求预测
- 智能物料推荐
- 自动化采购建议

### 4. 移动端支持
- 移动端物料查询和录入
- 二维码/条形码扫描支持
- 离线数据同步功能

## 关键算法设计

### 1. 物料编码生成算法
```
编码格式：项目编码-分类编码-序号
示例：GZ-A010-0001
算法：获取项目和分类编码 + 查询最大序号 + 递增生成
```

### 2. 库存平均价格计算算法
```
加权平均价格 = (原库存数量 × 原单价 + 新增数量 × 新单价) / (原库存数量 + 新增数量)
考虑因素：历史成本、当前市价、批次差异
```

### 3. 分类路径维护算法
```
完整路径 = 逐级向上查找父分类名称并拼接
格式：一级分类/二级分类/三级分类/...
自动维护：分类变更时触发路径重计算
```

### 4. 库存报警检查算法
```
报警条件：
- 低库存：当前库存 ≤ 下限报警值
- 高库存：当前库存 ≥ 上限报警值
- 零库存：当前库存 = 0
检查频率：实时检查 + 定时批量检查
```

## 性能优化设计

### 1. 查询优化
- 分类树缓存机制
- 库存信息索引优化
- 分页查询支持

### 2. 数据同步优化
- 冗余字段减少查询关联
- 批量更新操作
- 异步数据同步

### 3. 报警优化
- 报警条件缓存
- 批量报警处理
- 防重复报警机制