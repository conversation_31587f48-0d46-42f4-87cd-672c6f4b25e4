	using System.Collections.Generic; 
	using System.Text; 
	using System;
	using System.ServiceModel;
	using System.ServiceModel.Web;
	using NHibernate;
	using NHibernate.Linq;
	using Project.Common;
	using Project.Dal.Base;
	using Project.Biz.Base;
	using PropertySys_ZGHQ.ProcedureEntity;
	using PropertySys_ZGHQ.ProcedureDal;

	namespace PropertySys_ZGHQ.ProcedureBiz
	{
		public class  BizUPSearchFireUnitAlarmByAlarmTimes : BaseSPBiz
		{
			private DalUPSearchFireUnitAlarmByAlarmTimes dalUPSearchFireUnitAlarmByAlarmTimes;
		
			private  BizUPSearchFireUnitAlarmByAlarmTimes()
			{
				dalUPSearchFireUnitAlarmByAlarmTimes = DalFactory.Get<DalUPSearchFireUnitAlarmByAlarmTimes>();
			}

			[OperationContract]
			[WebInvoke(BodyStyle = WebMessageBodyStyle.Wrapped)]
			public IList<UPSearchFireUnitAlarmByAlarmTimes> Invoke(UPSearchFireUnitAlarmByAlarmTimesParameter parameter)
			{
									var result = dalUPSearchFireUnitAlarmByAlarmTimes.Invoke(parameter);
					return result;
							}
		}
	}

	