# 集团管理模块ER图

## 表结构说明

### 核心实体表

#### UTBaseUnitManage (集团项目架构表)
- **主键**: ID (Guid)
- **外键**: PID (父单位ID, Guid?)
- **核心字段**: UnitCode, UnitName, UnitLevel, UnitClass, CompanyName

#### UTSYSOrganization (组织机构部门表)  
- **主键**: OrgID (Int64)
- **外键**: OrgParentID (父级部门ID, Int64?), UnitID (所属单位ID, Guid?)
- **核心字段**: OrgName, OrgType, OrgNumber, Level

#### UTWLItemClass (物料分类表)
- **主键**: ID (Guid)
- **外键**: PClassID (父分类ID, Guid?)
- **核心字段**: ClassCode, ClassName, IsLast, OrderIndex

#### UTBaseEQClass (设备分类表)
- **主键**: ID (Guid)  
- **外键**: PClassID (上级分类, Guid?), EQSystemID (所属系统, Guid?)
- **核心字段**: ClassCode, ClassName, ClassLevel, IsLast

#### UTBaseCheckInfo (设备各类检查项表)
- **主键**: ID (Guid)
- **外键**: UnitID (单位ID, Guid?), EQClassID (分类ID, Guid?)
- **核心字段**: CheckItem, CheckInfo, CheckMethod, IsDayCheck

#### UTBaseBadCode (故障代码表)
- **主键**: ID (Guid)
- **外键**: PBadClassID (父类ID, Guid?)
- **核心字段**: BadCode, BadName, BadDesc, BadLevel

## ER关系图

```mermaid
erDiagram
    UTBaseUnitManage ||--o{ UTSYSOrganization : "单位包含部门"
    UTBaseUnitManage ||--|| UTBaseUnitManage : "父子单位关系"
    UTBaseUnitManage ||--o{ UTBaseCheckInfo : "单位定义检查项"
    UTSYSOrganization ||--|| UTSYSOrganization : "部门层级关系"
    UTWLItemClass ||--|| UTWLItemClass : "分类层级关系"
    UTBaseEQClass ||--|| UTBaseEQClass : "设备分类层级"
    UTBaseEQClass ||--o{ UTBaseCheckInfo : "设备分类关联检查项"
    UTBaseBadCode ||--|| UTBaseBadCode : "故障代码层级"
    
    UTBaseUnitManage {
        Guid ID PK
        Guid PID FK "父单位ID"
        string UnitCode "单位编码"
        string UnitName "单位名称" 
        int UnitLevel "单位等级(0集团1分公司2项目)"
        int UnitClass "项目类型"
        string CompanyName "所属公司名称"
        string UnitAddress "地址"
        decimal UnitArea "占地面积"
        decimal BuildArea "建筑面积"
        int64 AdminUserID "管理员ID"
        DateTime CreateDate "创建时间"
    }
    
    UTSYSOrganization {
        int64 OrgID PK
        string OrgName "部门名称"
        int OrgType "部门类型"
        string OrgNumber "部门编号"
        int64 OrgParentID FK "父级部门ID"
        string OrgPhone "部门电话"
        string OrgAddress "机构地址"
        string ChargePerson "负责人"
        int Level "层级数"
        Guid UnitID FK "所属单位ID"
        int IsLast "是否最终级别"
        int MaintenanceNY "维修组标识"
    }
    
    UTWLItemClass {
        Guid ID PK
        string ClassCode "分类编码"
        string ClassName "分类名称"
        Guid PClassID FK "父分类ID"
        string AllClassName "全名"
        int IsLast "是否最终类型"
        string Remark "备注"
        int OrderIndex "排序"
    }
    
    UTBaseEQClass {
        Guid ID PK
        Guid EQSystemID FK "所属系统"
        int ClassLevel "分类级别"
        Guid PClassID FK "上级分类"
        string ClassCode "分类编号"
        string ClassName "分类名称"
        int IsLast "是否最终类型"
        int IsDayCheck "是否日检"
        int IsWeekCheck "是否周检"
        int IsMonthCheck "是否月检"
        int IsQuarterCheck "是否季检"
        int IsYearCheck "是否年检"
    }
    
    UTBaseCheckInfo {
        Guid ID PK
        Guid UnitID FK "单位ID(空为集团级)"
        Guid EQClassID FK "分类ID"
        string CheckItem "检查项"
        string CheckInfo "检查项详情"
        string CheckMethod "检查方法"
        int IsDayCheck "是否日检"
        int IsWeekCheck "是否周检"
        int IsMonthCheck "是否月检"
        int IsQuarterCheck "是否季检"
        int IsYearCheck "是否年检"
        string CommonFault "常见问题"
        DateTime CreateDate "创建时间"
    }
    
    UTBaseBadCode {
        Guid ID PK
        Guid PBadClassID FK "父类ID"
        string PBadClassCode "父类编号"
        string BadCode "故障编号"
        string BadName "故障名称"
        string BadDesc "故障说明"
        int BadLevel "故障等级"
        DateTime CreateDate "创建时间"
        int OrderIndex "排序"
    }
```

## 业务关系说明

1. **集团架构管理**: UTBaseUnitManage表通过PID字段实现树形结构，支持集团-分公司-项目的三级管理
2. **组织架构管理**: UTSYSOrganization表通过OrgParentID实现部门层级，UnitID关联到具体单位
3. **物料分类管理**: UTWLItemClass表通过PClassID实现多级分类体系
4. **设备分类管理**: UTBaseEQClass表支持多级分类，关联检查项配置
5. **巡检项管理**: UTBaseCheckInfo表关联设备分类，支持集团级和项目级配置
6. **故障代码管理**: UTBaseBadCode表支持分级故障代码体系 