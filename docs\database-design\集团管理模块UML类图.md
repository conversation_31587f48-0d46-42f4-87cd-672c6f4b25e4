# 集团管理模块UML类图

## 类结构说明

### 实体类层级
- **TableEntity**: 数据表实体类 (对应数据库表结构)
- **TableBiz**: 业务逻辑类 (处理具体业务逻辑)
- **TableDal**: 数据访问类 (处理数据库访问)

### 核心业务实体

#### UTBaseUnitManage (集团项目架构)
- 负责集团、分公司、项目的层级管理
- 支持树形结构的单位组织架构
- 管理项目基本信息和配置参数

#### UTSYSOrganization (组织机构)
- 管理部门层级结构
- 支持维修组等特殊部门标识
- 关联具体单位的部门架构

#### UTBaseEQClass (设备分类)
- 管理设备的分类体系
- 配置各类设备的检查周期
- 支持多级分类结构

#### UTBaseCheckInfo (巡检项)
- 定义设备检查项目
- 支持集团级和项目级配置
- 关联设备分类的检查要求

## UML类关系图

```mermaid
classDiagram
    class UTBaseUnitManage {
        +Guid ID
        +Guid? PID
        +String UnitCode
        +String UnitName
        +Int32? UnitLevel
        +Int32? UnitClass
        +String CompanyName
        +String UnitAddress
        +Decimal? UnitArea
        +Decimal? BuildArea
        +Int64? AdminUserID
        +DateTime? CreateDate
        +GetParentUnit() UTBaseUnitManage
        +GetChildUnits() List~UTBaseUnitManage~
        +IsProject() Boolean
        +IsCompany() Boolean
        +IsGroup() Boolean
    }
    
    class UTSYSOrganization {
        +Int64 OrgID
        +String OrgName
        +Int32? OrgType
        +String OrgNumber
        +Int64? OrgParentID
        +String OrgPhone
        +String ChargePerson
        +Int32? Level
        +Guid? UnitID
        +Int32? IsLast
        +Int32? MaintenanceNY
        +GetParentOrg() UTSYSOrganization
        +GetChildOrgs() List~UTSYSOrganization~
        +IsMaintenanceOrg() Boolean
    }
    
    class UTWLItemClass {
        +Guid ID
        +String ClassCode
        +String ClassName
        +Guid? PClassID
        +String AllClassName
        +Int32? IsLast
        +Int32? OrderIndex
        +String Remark
        +GetParentClass() UTWLItemClass
        +GetChildClasses() List~UTWLItemClass~
        +GetFullPath() String
    }
    
    class UTBaseEQClass {
        +Guid ID
        +Guid? EQSystemID
        +Int32? ClassLevel
        +Guid? PClassID
        +String ClassCode
        +String ClassName
        +Int32? IsLast
        +Int32? IsDayCheck
        +Int32? IsWeekCheck
        +Int32? IsMonthCheck
        +Int32? IsQuarterCheck
        +Int32? IsYearCheck
        +GetParentClass() UTBaseEQClass
        +GetChildClasses() List~UTBaseEQClass~
        +GetCheckPeriods() List~String~
    }
    
    class UTBaseCheckInfo {
        +Guid ID
        +Guid? UnitID
        +Guid? EQClassID
        +String CheckItem
        +String CheckInfo
        +String CheckMethod
        +Int32? IsDayCheck
        +Int32? IsWeekCheck
        +Int32? IsMonthCheck
        +String CommonFault
        +DateTime? CreateDate
        +IsApplicableForPeriod(String period) Boolean
        +GetCheckPeriods() List~String~
    }
    
    class UTBaseBadCode {
        +Guid ID
        +Guid? PBadClassID
        +String BadCode
        +String BadName
        +String BadDesc
        +Int32? BadLevel
        +DateTime? CreateDate
        +Int32? OrderIndex
        +GetParentCode() UTBaseBadCode
        +GetChildCodes() List~UTBaseBadCode~
        +GetLevelDescription() String
    }
    
    class BizUTBaseUnitManage {
        <<Service>>
        +GetByID(Guid id) UTBaseUnitManage
        +GetProjectList() List~UTBaseUnitManage~
        +GetCompanyList() List~UTBaseUnitManage~
        +GetUnitTree() List~UTBaseUnitManage~
        +SaveOrUpdate(UTBaseUnitManage entity) Guid
        +ValidateUnitHierarchy(UTBaseUnitManage entity) Boolean
    }
    
    class BizUTSYSOrganization {
        <<Service>>
        +GetByUnitID(Guid unitId) List~UTSYSOrganization~
        +GetOrgTree(Guid unitId) List~UTSYSOrganization~
        +GetMaintenanceOrgs(Guid unitId) List~UTSYSOrganization~
        +SaveOrUpdate(UTSYSOrganization entity) Int64
        +ValidateOrgHierarchy(UTSYSOrganization entity) Boolean
    }
    
    class BizUTBaseEQClass {
        <<Service>>
        +GetEQClassTree() List~UTBaseEQClass~
        +GetBySystemType(Int32 systemType) List~UTBaseEQClass~
        +GetCheckableClasses() List~UTBaseEQClass~
        +SaveOrUpdate(UTBaseEQClass entity) Guid
        +ValidateClassHierarchy(UTBaseEQClass entity) Boolean
    }
    
    class BizUTBaseCheckInfo {
        <<Service>>
        +GetByEQClassID(Guid classId) List~UTBaseCheckInfo~
        +GetByUnitID(Guid unitId) List~UTBaseCheckInfo~
        +GetGroupLevelChecks() List~UTBaseCheckInfo~
        +GetProjectLevelChecks(Guid unitId) List~UTBaseCheckInfo~
        +SaveOrUpdate(UTBaseCheckInfo entity) Guid
    }

    %% 聚合关系
    UTBaseUnitManage ||--o{ UTSYSOrganization : "包含部门"
    UTBaseUnitManage ||--o{ UTBaseCheckInfo : "定义检查项"
    UTBaseEQClass ||--o{ UTBaseCheckInfo : "关联检查项"
    
    %% 自关联关系
    UTBaseUnitManage ||--o{ UTBaseUnitManage : "父子单位"
    UTSYSOrganization ||--o{ UTSYSOrganization : "部门层级"
    UTWLItemClass ||--o{ UTWLItemClass : "分类层级"
    UTBaseEQClass ||--o{ UTBaseEQClass : "分类层级"
    UTBaseBadCode ||--o{ UTBaseBadCode : "代码层级"
    
    %% 服务依赖关系
    BizUTBaseUnitManage --> UTBaseUnitManage : "管理"
    BizUTSYSOrganization --> UTSYSOrganization : "管理"
    BizUTBaseEQClass --> UTBaseEQClass : "管理"
    BizUTBaseCheckInfo --> UTBaseCheckInfo : "管理"
    BizUTBaseCheckInfo --> UTBaseEQClass : "依赖"
    BizUTSYSOrganization --> UTBaseUnitManage : "依赖"
    BizUTBaseCheckInfo --> UTBaseUnitManage : "依赖"
```

## 类职责说明

### 实体类职责
1. **UTBaseUnitManage**: 集团架构管理，支持三级组织结构
2. **UTSYSOrganization**: 部门组织管理，支持层级结构和维修组标识
3. **UTWLItemClass**: 物料分类管理，支持多级分类体系
4. **UTBaseEQClass**: 设备分类管理，配置检查周期
5. **UTBaseCheckInfo**: 巡检项配置，支持集团级和项目级
6. **UTBaseBadCode**: 故障代码管理，支持分级代码体系

### 业务服务类职责
1. **BizUTBaseUnitManage**: 提供集团架构的CRUD操作和业务逻辑
2. **BizUTSYSOrganization**: 提供组织架构的管理功能
3. **BizUTBaseEQClass**: 提供设备分类的管理功能
4. **BizUTBaseCheckInfo**: 提供巡检项的配置和查询功能

### 设计模式
- **Repository模式**: 数据访问层封装
- **Service模式**: 业务逻辑层封装
- **Composite模式**: 支持树形结构的层级管理 