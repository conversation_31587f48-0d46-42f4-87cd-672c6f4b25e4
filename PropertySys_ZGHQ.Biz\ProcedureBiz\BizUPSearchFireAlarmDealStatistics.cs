	using System.Collections.Generic; 
	using System.Text; 
	using System;
	using System.ServiceModel;
	using System.ServiceModel.Web;
	using NHibernate;
	using NHibernate.Linq;
	using Project.Common;
	using Project.Dal.Base;
	using Project.Biz.Base;
	using PropertySys_ZGHQ.ProcedureEntity;
	using PropertySys_ZGHQ.ProcedureDal;

	namespace PropertySys_ZGHQ.ProcedureBiz
	{
		public class  BizUPSearchFireAlarmDealStatistics : BaseSPBiz
		{
			private DalUPSearchFireAlarmDealStatistics dalUPSearchFireAlarmDealStatistics;
		
			private  BizUPSearchFireAlarmDealStatistics()
			{
				dalUPSearchFireAlarmDealStatistics = DalFactory.Get<DalUPSearchFireAlarmDealStatistics>();
			}

			[OperationContract]
			[WebInvoke(BodyStyle = WebMessageBodyStyle.Wrapped)]
			public IList<UPSearchFireAlarmDealStatistics> Invoke(UPSearchFireAlarmDealStatisticsParameter parameter)
			{
									var result = dalUPSearchFireAlarmDealStatistics.Invoke(parameter);
					return result;
							}
		}
	}

	