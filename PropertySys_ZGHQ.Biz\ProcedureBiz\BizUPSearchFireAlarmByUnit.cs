	using System.Collections.Generic; 
	using System.Text; 
	using System;
	using System.ServiceModel;
	using System.ServiceModel.Web;
	using NHibernate;
	using NHibernate.Linq;
	using Project.Common;
	using Project.Dal.Base;
	using Project.Biz.Base;
	using PropertySys_ZGHQ.ProcedureEntity;
	using PropertySys_ZGHQ.ProcedureDal;

	namespace PropertySys_ZGHQ.ProcedureBiz
	{
		public class  BizUPSearchFireAlarmByUnit : BaseSPBiz
		{
			private DalUPSearchFireAlarmByUnit dalUPSearchFireAlarmByUnit;
		
			private  BizUPSearchFireAlarmByUnit()
			{
				dalUPSearchFireAlarmByUnit = DalFactory.Get<DalUPSearchFireAlarmByUnit>();
			}

			[OperationContract]
			[WebInvoke(BodyStyle = WebMessageBodyStyle.Wrapped)]
			public IList<UPSearchFireAlarmByUnit> Invoke(UPSearchFireAlarmByUnitParameter parameter)
			{
									var result = dalUPSearchFireAlarmByUnit.Invoke(parameter);
					return result;
							}
		}
	}

	