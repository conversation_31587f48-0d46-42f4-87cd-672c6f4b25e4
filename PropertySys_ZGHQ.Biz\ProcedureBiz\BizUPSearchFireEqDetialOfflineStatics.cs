	using System.Collections.Generic; 
	using System.Text; 
	using System;
	using System.ServiceModel;
	using System.ServiceModel.Web;
	using NHibernate;
	using NHibernate.Linq;
	using Project.Common;
	using Project.Dal.Base;
	using Project.Biz.Base;
	using PropertySys_ZGHQ.ProcedureEntity;
	using PropertySys_ZGHQ.ProcedureDal;

	namespace PropertySys_ZGHQ.ProcedureBiz
	{
		public class  BizUPSearchFireEqDetialOfflineStatics : BaseSPBiz
		{
			private DalUPSearchFireEqDetialOfflineStatics dalUPSearchFireEqDetialOfflineStatics;
		
			private  BizUPSearchFireEqDetialOfflineStatics()
			{
				dalUPSearchFireEqDetialOfflineStatics = DalFactory.Get<DalUPSearchFireEqDetialOfflineStatics>();
			}

			[OperationContract]
			[WebInvoke(BodyStyle = WebMessageBodyStyle.Wrapped)]
			public IList<UPSearchFireEqDetialOfflineStatics> Invoke(UPSearchFireEqDetialOfflineStaticsParameter parameter)
			{
									var result = dalUPSearchFireEqDetialOfflineStatics.Invoke(parameter);
					return result;
							}
		}
	}

	