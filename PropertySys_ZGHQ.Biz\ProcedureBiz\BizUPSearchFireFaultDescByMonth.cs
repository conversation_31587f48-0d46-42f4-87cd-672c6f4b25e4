	using System.Collections.Generic; 
	using System.Text; 
	using System;
	using System.ServiceModel;
	using System.ServiceModel.Web;
	using NHibernate;
	using NHibernate.Linq;
	using Project.Common;
	using Project.Dal.Base;
	using Project.Biz.Base;
	using PropertySys_ZGHQ.ProcedureEntity;
	using PropertySys_ZGHQ.ProcedureDal;

	namespace PropertySys_ZGHQ.ProcedureBiz
	{
		public class  BizUPSearchFireFaultDescByMonth : BaseSPBiz
		{
			private DalUPSearchFireFaultDescByMonth dalUPSearchFireFaultDescByMonth;
		
			private  BizUPSearchFireFaultDescByMonth()
			{
				dalUPSearchFireFaultDescByMonth = DalFactory.Get<DalUPSearchFireFaultDescByMonth>();
			}

			[OperationContract]
			[WebInvoke(BodyStyle = WebMessageBodyStyle.Wrapped)]
			public IList<UPSearchFireFaultDescByMonth> Invoke(UPSearchFireFaultDescByMonthParameter parameter)
			{
									var result = dalUPSearchFireFaultDescByMonth.Invoke(parameter);
					return result;
							}
		}
	}

	