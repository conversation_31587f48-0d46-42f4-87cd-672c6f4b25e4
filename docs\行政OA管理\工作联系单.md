# 工作联系单

## 基本信息
- **子菜单名**: 工作联系单
- **路由**: ~/ModuleXZ/WorkOrder/LevelIndex
- **模块名**: ModuleXZ
- **视图名**: WorkOrder
- **功能描述**: 用于创建、管理和查看工作联系单，支持工作任务的分配和跟踪

## 页面逻辑分析

### 主要Controller方法
**WorkOrderController.cs**

1. **Index(ModelWorkOrderIndex model)** - 工作联系单查询
   - 功能：显示工作联系单列表
   - 逻辑：调用model.RetriveData()获取数据并返回视图

2. **Create(ModelWorkOrderCreate model)** - 工作联系单编辑
   - 功能：创建或编辑工作联系单
   - 逻辑：调用model.RetriveData()初始化数据

3. **Create(POST)** - 保存工作联系单
   - 功能：保存工作联系单数据
   - 逻辑：
     - 调用model.Save()保存数据
     - 如果issubmit=1则提交申请
     - 返回对话框关闭脚本或重定向到编辑页面

4. **DetailIndex(ModelWorkOrderDetailIndex model)** - 联系单详情
   - 功能：显示工作联系单详细信息
   - 逻辑：调用model.RetriveData()获取详情数据

5. **DealIndex(ModelWorkOrderDealIndex model)** - 工作联系单审核查询
   - 功能：显示待审核的工作联系单列表
   - 逻辑：调用model.RetriveData()获取待审核数据

6. **CancelWorkOrder(Guid id)** - 撤销工作联系单
   - 功能：撤销已提交的工作联系单
   - 逻辑：调用serviceUTXZWorkOrder.CancelOrder()撤销订单

### 主要Model类
**ModelWorkOrder.cs**

1. **ModelWorkOrderIndex** - 工作联系单列表模型
   - 属性：包含搜索条件、分页信息等
   - 方法：RetriveData()获取工作联系单列表数据

2. **ModelWorkOrderCreate** - 工作联系单创建/编辑模型
   - 属性：UTXZWorkOrderEntity工作联系单实体
   - 方法：
     - RetriveData()：初始化数据
     - Save()：保存工作联系单数据
     - RetriveDataPrint()：获取打印数据

3. **ModelWorkOrderDetailIndex** - 工作联系单详情模型
   - 功能：显示工作联系单的详细信息

## 数据库使用情况

### 主要数据表
1. **UTXZWorkOrder** - 工作联系单主表
   - 字段：ID、工作内容、申请人、状态、创建时间等
   - 操作：增删改查、状态更新

2. **UTXZWorkOrderUser** - 工作联系单用户关联表
   - 字段：工作联系单ID、用户ID、角色等
   - 操作：用户分配、权限管理

3. **UTXZWorkOrderFile** - 工作联系单附件表
   - 字段：工作联系单ID、文件名、文件路径等
   - 操作：附件上传、下载

### 主要Service接口
1. **IServiceUTXZWorkOrder** - 工作联系单服务
   - 方法：
     - SaveOrUpdate()：保存或更新
     - CancelOrder()：撤销订单
     - GetByID()：根据ID获取

2. **IServiceUTXZWorkOrderUser** - 工作联系单用户服务
   - 方法：用户关联操作

3. **IServiceUTXZWorkOrderFile** - 工作联系单附件服务
   - 方法：附件管理操作

## 业务流程
1. **创建工作联系单**：用户填写工作内容、指定执行人员
2. **提交审核**：提交后进入审核流程
3. **审核处理**：相关人员进行审核
4. **执行跟踪**：跟踪工作执行进度
5. **完成确认**：确认工作完成

## 权限控制
- 基于用户角色的访问控制
- 不同状态下的操作权限限制
- 审核流程中的权限验证

## 相关枚举
- **EnumXZStatus**：工作联系单状态枚举
- **EnumPageState**：页面状态枚举
