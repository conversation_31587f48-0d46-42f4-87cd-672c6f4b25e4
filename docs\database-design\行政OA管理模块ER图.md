# PropertySys行政OA管理模块ER图

## 模块概述

行政OA管理模块是PropertySys智能物业管理系统的重要组成部分，主要负责企业内部的行政办公自动化管理。该模块涵盖了日常办公中的各类申请、审批和管理流程，为物业公司提供完整的行政管理解决方案。

## 核心业务功能

### 1. 报销管理
- **报销申请**：员工提交各类费用报销申请
- **报销审核**：多级审核流程，确保费用合规性
- **费用分类**：支持多种费用类型和项目分摊
- **附件管理**：支持报销凭证和环境照片上传

### 2. 出差管理
- **出差申请**：员工申请出差，包含目的地、时间、费用预算等
- **出差审批**：按出差天数和用户类型进行分级审批
- **出差记录**：记录实际出差情况和工作内容
- **关联报销**：出差单可直接关联后续的报销申请

### 3. 预支管理
- **预支申请**：员工申请备用金、预付款等
- **预支审核**：按金额大小进行分级审批
- **预支发放**：记录实际发放情况
- **预支归还**：支持分次归还和多种归还方式

### 4. 用车管理
- **车辆登记**：公司车辆基本信息管理
- **用车申请**：员工申请使用公司车辆
- **用车审批**：车辆管理员和上级审批
- **用车记录**：记录用车前后状态和里程

## 核心数据表结构

### 报销管理相关表

#### UTUnitFeeApply（费用报销申请）
| 字段名 | 类型 | 说明 | 约束 |
|--------|------|------|------|
| ID | uniqueidentifier | 主键 | PK |
| UnitID | uniqueidentifier | 单位ID | FK → UTBaseUnitManage |
| FeeNo | nvarchar(50) | 费用报销单号 | - |
| FeeTitle | nvarchar(50) | 标题 | NOT NULL |
| FeeStatus | int | 报销单状态 | - |
| FeeType | int | 费用类型 | NOT NULL |
| Amount | decimal(18,2) | 报销总金额 | - |
| ApplyUserID | bigint | 报销人 | FK → UTSYSUser |
| EvectionID | uniqueidentifier | 关联出差单号 | FK → UTUnitEvection |
| StartDate | datetime | 报销开始时间 | NOT NULL |
| EndDate | datetime | 报销结束时间 | NOT NULL |
| CurrentApprovalUser | bigint | 当前审批人 | FK → UTSYSUser |
| ProcessCode | int | 流程Code | - |
| CreateDate | datetime | 创建时间 | - |
| CreateUser | bigint | 创建人 | FK → UTSYSUser |

#### UTUnitFeeApplyDetail（费用报销明细）
| 字段名 | 类型 | 说明 | 约束 |
|--------|------|------|------|
| ID | uniqueidentifier | 主键 | PK |
| FeeID | uniqueidentifier | 关联报销单 | FK → UTUnitFeeApply |
| ProjectID | uniqueidentifier | 项目ID | FK → UTBaseUnitManage |
| FeeType | int | 费用类型 | NOT NULL |
| Amount | decimal(18,2) | 金额 | NOT NULL |
| FeeDesc | nvarchar(500) | 费用明细说明 | NOT NULL |
| AdvanceID | uniqueidentifier | 关联预支单ID | FK → UTCWAdvance |
| IsExcess | int | 是否超额 | - |
| CreateDate | datetime | 创建时间 | - |
| CreateUser | bigint | 创建人 | FK → UTSYSUser |

#### UTUnitFeeApplyDetailFile（项目费用报销明细图片）
| 字段名 | 类型 | 说明 | 约束 |
|--------|------|------|------|
| ID | uniqueidentifier | 主键 | PK |
| BaseID | uniqueidentifier | 报销明细ID | FK → UTUnitFeeApplyDetail |
| FileType | int | 图片类型(0环境照片 1凭证) | - |
| FileName | nvarchar(200) | 图片名称 | - |
| ShowName | nvarchar(200) | 显示名称 | - |
| UploadDate | datetime | 上传时间 | - |
| UploadUser | bigint | 上传人 | FK → UTSYSUser |

### 出差管理相关表

#### UTUnitEvection（出差申请）
| 字段名 | 类型 | 说明 | 约束 |
|--------|------|------|------|
| ID | uniqueidentifier | 主键 | PK |
| UnitID | uniqueidentifier | 项目ID | FK → UTBaseUnitManage |
| EvectionNo | nvarchar(50) | 出差单号 | - |
| EvectionAddress | nvarchar(500) | 目的地 | - |
| EvectionType | int | 类型(0售前 1售后 2实施 3其他) | NOT NULL |
| EvectionStart | datetime | 计划开始时间 | NOT NULL |
| EvectionEnd | datetime | 计划结束时间 | NOT NULL |
| EvectionDesc | ntext | 描述 | NOT NULL |
| TogetherUser | nvarchar(500) | 同行人 | - |
| IsBuyTicket | int | 是否需要购买车票 | NOT NULL |
| EvectionProvice | uniqueidentifier | 出差目的地省份 | NOT NULL |
| EvectionCity | uniqueidentifier | 出差目的地城市 | NOT NULL |
| EvectionRegionID | uniqueidentifier | 出差区域ID | NOT NULL |
| Status | int | 状态 | - |
| ApplyUserID | bigint | 申请人 | FK → UTSYSUser |
| CurrentApprovalUser | bigint | 当前审批人 | FK → UTSYSUser |
| ProcessCode | int | 流程编号 | - |
| CreateDate | datetime | 创建时间 | - |
| CreateUser | bigint | 创建人 | FK → UTSYSUser |

#### UTUnitEvectionDetail（出差记录明细）
| 字段名 | 类型 | 说明 | 约束 |
|--------|------|------|------|
| ID | uniqueidentifier | 主键 | PK |
| EvectionID | uniqueidentifier | 出差ID | FK → UTUnitEvection |
| EvectionDate | datetime | 出差记录日期 | NOT NULL |
| EvectionDesc | nvarchar(500) | 出差描述 | NOT NULL |
| CreateDate | datetime | 创建日期 | - |
| CreateUser | bigint | 创建人 | FK → UTSYSUser |

### 预支管理相关表

#### UTCWAdvance（预支单）
| 字段名 | 类型 | 说明 | 约束 |
|--------|------|------|------|
| ID | uniqueidentifier | 主键 | PK |
| UnitID | uniqueidentifier | 单位ID | FK → UTBaseUnitManage |
| AdvanceTitle | nvarchar(500) | 标题 | - |
| AdvanceNo | nvarchar(50) | 预支单号 | - |
| AdvanceDate | datetime | 预支日期 | NOT NULL |
| AdvanceType | int | 预支类型(0微信 1支付宝 2银行卡 3现金) | NOT NULL |
| AdvanceAmount | decimal(18,2) | 预支金额 | NOT NULL |
| PlanReturnDate | datetime | 计划归还日期 | NOT NULL |
| Status | int | 状态 | - |
| ApplyUserID | bigint | 申请人 | FK → UTSYSUser |
| CurrentApprovalUser | bigint | 当前审批人 | FK → UTSYSUser |
| ProcessCode | int | 审批流程编号 | - |
| PayDate | datetime | 付款时间 | - |
| PayUser | bigint | 付款人 | FK → UTSYSUser |
| PayAmount | decimal(18,2) | 付款金额 | - |
| CreateDate | datetime | 创建时间 | - |
| CreateUser | bigint | 创建人 | FK → UTSYSUser |

#### UTCWAdvanceDetail（预支单归还记录）
| 字段名 | 类型 | 说明 | 约束 |
|--------|------|------|------|
| ID | uniqueidentifier | 主键 | PK |
| AdvanceID | uniqueidentifier | 预支单号ID | FK → UTCWAdvance |
| ReturnDate | datetime | 归还日期 | - |
| ReturnAmount | decimal(18,2) | 归还金额 | - |
| ReturnType | int | 归还方式(0微信 1支付宝 2银行卡 3现金) | - |
| Status | int | 状态(0未确认 1已确认) | - |
| Remark | nvarchar(500) | 备注 | - |
| CreateUser | bigint | 创建人 | FK → UTSYSUser |
| CreateDate | datetime | 创建时间 | - |

#### UTCWAdvanceFile（预支单附件）
| 字段名 | 类型 | 说明 | 约束 |
|--------|------|------|------|
| ID | uniqueidentifier | 主键 | PK |
| BaseID | uniqueidentifier | 预支单ID | FK → UTCWAdvance |
| FileName | nvarchar(150) | 文件名 | - |
| ShowName | nvarchar(150) | 显示名称 | - |
| CreateUser | bigint | 创建人 | FK → UTSYSUser |
| CreateDate | datetime | 创建时间 | - |

### 用车管理相关表

#### UTUnitCompanyCar（车辆管理）
| 字段名 | 类型 | 说明 | 约束 |
|--------|------|------|------|
| ID | uniqueidentifier | 主键 | PK |
| UnitID | uniqueidentifier | 单位ID | FK → UTBaseUnitManage |
| CarNo | nvarchar(20) | 车号 | NOT NULL, 车牌号正则验证 |
| CarStatus | int | 车辆状态(0空闲 1使用中) | - |
| ManagerUser | bigint | 管理人 | NOT NULL, FK → UTSYSUser |
| BuyDate | datetime | 购买日期 | - |
| CreateDate | datetime | 创建时间 | - |
| CreateUser | bigint | 创建人 | FK → UTSYSUser |
| UpdateDate | datetime | 更新时间 | - |
| UpdateUser | bigint | 更新人 | FK → UTSYSUser |

#### UTUnitCompanyCarApply（车辆申请）
| 字段名 | 类型 | 说明 | 约束 |
|--------|------|------|------|
| ID | uniqueidentifier | 主键 | PK |
| UnitID | uniqueidentifier | 单位ID | FK → UTBaseUnitManage |
| ApplyNo | nvarchar(50) | 申请单号 | - |
| ApplyUser | bigint | 申请人ID | NOT NULL, FK → UTSYSUser |
| ApplyPhone | nvarchar(50) | 申请人手机 | NOT NULL |
| CarID | uniqueidentifier | 车辆ID | NOT NULL, FK → UTUnitCompanyCar |
| CarDriver | bigint | 车辆司机 | NOT NULL, FK → UTSYSUser |
| CarSite | nvarchar(50) | 车辆目的地 | NOT NULL |
| ApplyDesc | nvarchar(500) | 申请描述 | NOT NULL |
| StartDate | datetime | 开始时间 | NOT NULL |
| Enddate | datetime | 结束时间 | NOT NULL |
| Status | int | 申请单状态 | - |
| CurrentDealUser | bigint | 当前处理人 | FK → UTSYSUser |
| StartCarStatus | int | 用车前车辆完好程度 | - |
| StartCarMileage | int | 用车前里程 | - |
| EndCarStatus | int | 用车后车辆完好程度 | - |
| EndCarMileage | int | 用车后里程数 | - |
| CreateDate | datetime | 创建时间 | - |
| CreateUser | bigint | 创建人 | FK → UTSYSUser |

## 数据库ER图

```mermaid
erDiagram
    UTBaseUnitManage ||--o{ UTUnitFeeApply : "单位管理"
    UTBaseUnitManage ||--o{ UTUnitEvection : "单位管理"
    UTBaseUnitManage ||--o{ UTCWAdvance : "单位管理"
    UTBaseUnitManage ||--o{ UTUnitCompanyCar : "单位管理"
    UTBaseUnitManage ||--o{ UTUnitCompanyCarApply : "单位管理"
    
    UTSYSUser ||--o{ UTUnitFeeApply : "申请人"
    UTSYSUser ||--o{ UTUnitFeeApply : "审批人"
    UTSYSUser ||--o{ UTUnitEvection : "申请人"
    UTSYSUser ||--o{ UTUnitEvection : "审批人"
    UTSYSUser ||--o{ UTCWAdvance : "申请人"
    UTSYSUser ||--o{ UTCWAdvance : "审批人"
    UTSYSUser ||--o{ UTUnitCompanyCar : "管理人"
    UTSYSUser ||--o{ UTUnitCompanyCarApply : "申请人"
    
    UTUnitFeeApply ||--o{ UTUnitFeeApplyDetail : "报销明细"
    UTUnitFeeApplyDetail ||--o{ UTUnitFeeApplyDetailFile : "明细附件"
    UTUnitEvection ||--o{ UTUnitEvectionDetail : "出差明细"
    UTUnitEvection ||--o{ UTUnitFeeApply : "关联报销"
    
    UTCWAdvance ||--o{ UTCWAdvanceDetail : "归还记录"
    UTCWAdvance ||--o{ UTCWAdvanceFile : "预支附件"
    UTCWAdvance ||--o{ UTUnitFeeApplyDetail : "关联报销明细"
    
    UTUnitCompanyCar ||--o{ UTUnitCompanyCarApply : "车辆申请"
    
    UTBaseUnitManage {
        uniqueidentifier ID PK
        nvarchar UnitName
        int UnitType
        datetime CreateDate
        bigint CreateUser
    }
    
    UTSYSUser {
        bigint UserID PK
        nvarchar UserName
        nvarchar UserAccount
        nvarchar MobilePhone
        int UserType
        uniqueidentifier UnitID
    }
    
    UTUnitFeeApply {
        uniqueidentifier ID PK
        uniqueidentifier UnitID FK
        nvarchar FeeNo
        nvarchar FeeTitle "NOT NULL"
        int FeeStatus
        int FeeType "NOT NULL"
        decimal Amount
        bigint ApplyUserID FK
        uniqueidentifier EvectionID FK
        datetime StartDate "NOT NULL"
        datetime EndDate "NOT NULL"
        bigint CurrentApprovalUser FK
        int ProcessCode
        datetime CreateDate
        bigint CreateUser FK
    }
    
    UTUnitFeeApplyDetail {
        uniqueidentifier ID PK
        uniqueidentifier FeeID FK
        uniqueidentifier ProjectID FK
        int FeeType "NOT NULL"
        decimal Amount "NOT NULL"
        nvarchar FeeDesc "NOT NULL"
        uniqueidentifier AdvanceID FK
        int IsExcess
        datetime CreateDate
        bigint CreateUser FK
    }
    
    UTUnitFeeApplyDetailFile {
        uniqueidentifier ID PK
        uniqueidentifier BaseID FK
        int FileType
        nvarchar FileName
        nvarchar ShowName
        datetime UploadDate
        bigint UploadUser FK
    }
    
    UTUnitEvection {
        uniqueidentifier ID PK
        uniqueidentifier UnitID FK
        nvarchar EvectionNo
        nvarchar EvectionAddress
        int EvectionType "NOT NULL"
        datetime EvectionStart "NOT NULL"
        datetime EvectionEnd "NOT NULL"
        ntext EvectionDesc "NOT NULL"
        nvarchar TogetherUser
        int IsBuyTicket "NOT NULL"
        uniqueidentifier EvectionProvice "NOT NULL"
        uniqueidentifier EvectionCity "NOT NULL"
        uniqueidentifier EvectionRegionID "NOT NULL"
        int Status
        bigint ApplyUserID FK
        bigint CurrentApprovalUser FK
        int ProcessCode
        datetime CreateDate
        bigint CreateUser FK
    }
    
    UTUnitEvectionDetail {
        uniqueidentifier ID PK
        uniqueidentifier EvectionID FK
        datetime EvectionDate "NOT NULL"
        nvarchar EvectionDesc "NOT NULL"
        datetime CreateDate
        bigint CreateUser FK
    }
    
    UTCWAdvance {
        uniqueidentifier ID PK
        uniqueidentifier UnitID FK
        nvarchar AdvanceTitle
        nvarchar AdvanceNo
        datetime AdvanceDate "NOT NULL"
        int AdvanceType "NOT NULL"
        decimal AdvanceAmount "NOT NULL"
        datetime PlanReturnDate "NOT NULL"
        int Status
        bigint ApplyUserID FK
        bigint CurrentApprovalUser FK
        int ProcessCode
        datetime PayDate
        bigint PayUser FK
        decimal PayAmount
        datetime CreateDate
        bigint CreateUser FK
    }
    
    UTCWAdvanceDetail {
        uniqueidentifier ID PK
        uniqueidentifier AdvanceID FK
        datetime ReturnDate
        decimal ReturnAmount
        int ReturnType
        int Status
        nvarchar Remark
        bigint CreateUser FK
        datetime CreateDate
    }
    
    UTCWAdvanceFile {
        uniqueidentifier ID PK
        uniqueidentifier BaseID FK
        nvarchar FileName
        nvarchar ShowName
        bigint CreateUser FK
        datetime CreateDate
    }
    
    UTUnitCompanyCar {
        uniqueidentifier ID PK
        uniqueidentifier UnitID FK
        nvarchar CarNo "NOT NULL"
        int CarStatus
        bigint ManagerUser "NOT NULL, FK"
        datetime BuyDate
        datetime CreateDate
        bigint CreateUser FK
        datetime UpdateDate
        bigint UpdateUser FK
    }
    
    UTUnitCompanyCarApply {
        uniqueidentifier ID PK
        uniqueidentifier UnitID FK
        nvarchar ApplyNo
        bigint ApplyUser "NOT NULL, FK"
        nvarchar ApplyPhone "NOT NULL"
        uniqueidentifier CarID "NOT NULL, FK"
        bigint CarDriver "NOT NULL, FK"
        nvarchar CarSite "NOT NULL"
        nvarchar ApplyDesc "NOT NULL"
        datetime StartDate "NOT NULL"
        datetime Enddate "NOT NULL"
        int Status
        bigint CurrentDealUser FK
        int StartCarStatus
        int StartCarMileage
        int EndCarStatus
        int EndCarMileage
        datetime CreateDate
        bigint CreateUser FK
    }
```

## 业务流程设计

### 1. 报销管理流程
```
员工申请 → 部门审核 → 财务审核 → 总经理审核 → 财务确认报销
     ↓         ↓         ↓          ↓            ↓
   保存草稿   审核通过   审核通过    审核通过    确认完成
             或退回     或退回      或退回
```

### 2. 出差管理流程
```
员工申请 → 部门审核 → 项目负责人审核 → 出差执行 → 回程确认 → 关联报销
     ↓         ↓           ↓         ↓        ↓        ↓
   保存草稿   审核通过     审核通过    记录明细  确认回程   费用报销
             或退回       或退回
```

### 3. 预支管理流程
```
员工申请 → 部门审核 → 财务审核 → 预支发放 → 分次归还 → 归还完成
     ↓         ↓         ↓        ↓        ↓        ↓
   保存草稿   审核通过   审核通过   记录发放   记录归还   状态更新
             或退回     或退回
```

### 4. 用车管理流程
```
员工申请 → 车辆管理员审核 → 领车确认 → 用车执行 → 还车确认 → 用车完成
     ↓           ↓          ↓        ↓        ↓        ↓
   保存草稿     审核通过     记录状态   记录里程   记录状态   状态更新
               或退回
```

## 数据关系分析

### 1. 核心关联关系
- **单位维度**：所有业务数据都与UTBaseUnitManage关联，支持多项目管理
- **用户维度**：通过UTSYSUser建立申请人、审批人、处理人关系
- **业务关联**：出差单可直接关联报销单，预支单可关联报销明细

### 2. 审批流程关系
- **ProcessCode**：统一的流程编号，关联工作流引擎
- **CurrentApprovalUser**：当前待审批人，支持动态审批流转
- **LastStepCode**：最后审批步骤，便于流程跟踪

### 3. 文件附件关系
- **分类存储**：不同业务模块的附件分表存储
- **类型区分**：支持不同类型文件的分类管理
- **权限控制**：通过创建人控制文件访问权限

## 扩展性设计

### 1. 状态管理
- 统一的状态枚举设计，便于状态流转控制
- 支持自定义状态扩展，适应不同业务需求

### 2. 流程配置
- 基于ProcessCode的流程配置，支持灵活的审批流程
- 可配置的审批层级，适应不同组织架构

### 3. 数据分区
- 基于UnitID的数据隔离，支持多租户架构
- 便于数据备份和性能优化

### 4. 业务扩展
- 预留扩展字段，支持个性化需求
- 标准化的实体设计，便于新增业务模块

## 技术特性

### 1. 数据完整性
- 外键约束确保数据一致性
- 非空约束确保关键数据完整性
- 数据类型约束确保数据格式正确

### 2. 性能优化
- 合理的索引设计，提升查询性能
- 分表设计减少单表数据量
- 视图封装复杂查询逻辑

### 3. 安全性
- 用户权限控制，确保数据安全
- 审计日志记录，便于问题追踪
- 数据加密存储敏感信息

## 总结

行政OA管理模块通过科学的数据库设计，实现了完整的企业行政管理功能。模块设计充分考虑了业务的复杂性和扩展性，采用标准化的数据结构和统一的业务流程，为企业提供了高效、可靠的行政管理解决方案。

该模块与PropertySys系统的其他模块紧密集成，共享用户体系和权限管理，形成了完整的企业级物业管理生态系统。 