# 工资单

## 基本信息
- **子菜单名**: 工资单
- **路由**: ~/ModuleXZ/Payroll/Index
- **模块名**: ModuleXZ
- **视图名**: Payroll
- **功能描述**: 用于管理员工工资单的生成、发放和查询，包括工资计算、扣除项目等

## 页面逻辑分析

### 主要Controller方法
**PayrollController.cs**

1. **Index(ModelPayrollIndex model)** - 工资单列表
   - 功能：显示工资单列表
   - 逻辑：调用model.RetriveData()获取工资单数据

2. **Create(ModelPayrollCreate model)** - 创建/编辑工资单
   - 功能：创建或编辑工资单
   - 逻辑：调用model.RetriveData()初始化数据

3. **Create(POST)** - 保存工资单
   - 功能：保存工资单数据
   - 逻辑：
     - 调用model.Save()保存数据
     - 如果issubmit=1则提交工资单
     - 返回对话框关闭脚本或重定向

4. **Delete(Guid id)** - 删除工资单
   - 功能：删除工资单记录
   - 逻辑：调用serviceUTXZPayroll.DeleteByID()删除

5. **GeneratePayroll(string datetime)** - 生成工资单
   - 功能：批量生成指定月份的工资单
   - 逻辑：调用serviceUTXZPayroll.GeneratePayroll()生成

6. **ImportPayrollCreate(ModelPayrollImport model)** - 导入工资数据
   - 功能：导入其他费用或工资数据
   - 逻辑：返回导入页面视图

7. **PayrollReport(ModelPayrollReport model)** - 工资报表
   - 功能：生成工资统计报表
   - 逻辑：调用model.RetriveData()获取报表数据

### 主要Model类
**ModelPayroll.cs**

1. **ModelPayrollIndex** - 工资单列表模型
   - 属性：
     - SearchEntity：搜索条件
     - GridDataSources：工资单列表数据
     - Count：总记录数
   - 方法：RetriveData()获取工资单列表

2. **ModelPayrollCreate** - 工资单创建/编辑模型
   - 属性：
     - UTXZPayrollEntity：工资单实体
     - UTSYSUserEnt：员工信息实体
     - issubmit：是否提交标志
   - 方法：
     - RetriveData()：初始化数据
     - Save()：保存工资单数据

## 数据库使用情况

### 主要数据表
1. **UTXZPayroll** - 工资单主表
   - 主要字段：
     - ID：主键
     - UserID：员工ID
     - UnitID：单位ID
     - PayrollDate：工资月份
     - BasicSalary：基本工资
     - Allowance：津贴
     - Bonus：奖金
     - Overtime：加班费
     - Deduction：扣除项
     - SocialInsurance：社保
     - Tax：个人所得税
     - NetSalary：实发工资
     - Status：状态
     - CreateDate：创建时间
     - CreateUser：创建人
   - 操作：增删改查、工资计算

2. **UTXZPayrollDetail** - 工资明细表
   - 字段：
     - PayrollID：工资单ID
     - ItemType：项目类型（收入、扣除等）
     - ItemName：项目名称
     - Amount：金额
     - Remark：备注
   - 操作：明细管理

3. **UTUnitSchedualShiftsReport** - 考勤报表（关联表）
   - 用于：
     - 获取考勤数据
     - 计算加班时间
     - 计算出勤天数

4. **UTUnitLeavePay** - 请假工资（关联表）
   - 用于：
     - 计算请假扣款
     - 处理病假工资
     - 年假工资计算

### 主要Service接口
1. **IServiceUTXZPayroll** - 工资单服务
   - 主要方法：
     - SaveOrUpdate()：保存或更新工资单
     - GeneratePayroll()：批量生成工资单
     - DeleteByID()：删除工资单
     - CalculateSalary()：计算工资

2. **IServiceUVSysUserHR** - 员工人事服务
   - 方法：获取员工人事信息

3. **IServiceUTUnitSchedualShiftsReport** - 考勤报表服务
   - 方法：获取考勤数据

## 业务流程
1. **工资计算**：
   - 获取员工基本工资信息
   - 计算考勤相关工资
   - 计算加班费
   - 计算各种津贴和奖金
   - 计算扣除项目

2. **工资单生成**：
   - 批量生成月度工资单
   - 自动计算各项工资组成
   - 生成工资明细
   - 计算实发工资

3. **审核确认**：
   - 人事部门审核工资单
   - 财务部门确认
   - 管理层审批

4. **工资发放**：
   - 确认工资单无误
   - 安排工资发放
   - 更新发放状态

5. **查询统计**：
   - 员工查询个人工资
   - 管理层查看工资统计
   - 生成工资报表

## 工资组成
### 收入项目
- **基本工资**：员工基本工资
- **岗位津贴**：岗位相关津贴
- **绩效奖金**：绩效考核奖金
- **加班费**：加班工资
- **其他收入**：其他各种收入

### 扣除项目
- **社会保险**：养老、医疗、失业保险
- **住房公积金**：住房公积金
- **个人所得税**：个人所得税
- **请假扣款**：请假相关扣款
- **其他扣除**：其他各种扣除

## 功能特点
1. **自动计算**：自动计算各项工资组成
2. **批量生成**：支持批量生成工资单
3. **明细管理**：详细的工资明细管理
4. **导入功能**：支持导入外部工资数据
5. **报表统计**：丰富的工资统计报表

## 计算规则
### 基本工资计算
- 按月薪或日薪计算
- 考虑出勤天数
- 处理请假扣款

### 加班费计算
- 平时加班：1.5倍工资
- 周末加班：2倍工资
- 节假日加班：3倍工资

### 税收计算
- 按照个人所得税法计算
- 考虑专项扣除
- 累计预扣预缴

## 权限控制
- **创建权限**：人事和财务人员可创建工资单
- **查看权限**：员工只能查看自己的工资单
- **管理权限**：管理人员可查看下属工资单
- **审批权限**：高级管理层有审批权限

## 状态管理
- **草稿**：工资单草稿状态
- **待审核**：等待审核
- **已审核**：审核通过
- **已发放**：工资已发放
- **已归档**：工资单已归档

## 报表功能
1. **工资汇总表**：部门和公司工资汇总
2. **工资明细表**：详细的工资明细
3. **成本分析表**：人工成本分析
4. **同比分析表**：工资同比分析

## 集成功能
1. **考勤集成**：与考勤系统集成
2. **人事集成**：与人事系统集成
3. **财务集成**：与财务系统集成
4. **银行集成**：与银行系统集成发放工资

## 相关枚举
- **EnumPayrollStatus**：工资单状态枚举
- **EnumPayrollItemType**：工资项目类型枚举
- **EnumSalaryType**：工资类型枚举
