# 身份证登录功能分析

## 概述

本系统提供多种登录方式，其中包括使用身份证号进行登录。本文档将详细分析身份证登录功能的实现逻辑。

## 用户界面

在登录页面 (`login.cshtml`)，用户可以通过一个单选按钮组来选择登录方式。其中一个选项是“身份证号”。

```html
<label for="isneedinspection2">
    @Html.RadioButtonFor(x => x.AcountType, 2, new { id = "isneedinspection2", onclick = "changeinspect(this)" })
    <span style="height: 45px; line-height: 45px;">身份证号</span>
</label>
```

当用户选择“身份证号”时，会触发 `changeinspect(this)` JavaScript 函数。

## 客户端逻辑

`changeinspect()` 函数会根据用户的选择来调整登录表单的显示。当选择身份证登录时，会执行以下操作：

1.  **更新输入框提示**: 将用户名字段的 `placeholder` 属性更改为“请输入身份证号”。
    ```javascript
    if ($("#isneedinspection2").prop("checked")) {
        $('#username').attr('placeholder', '请输入身份证号');
        $('#login_container').hide();
        $('.pclogin').show();
        draw(show_num);
        $("#canvas").on('click', function () {
            draw(show_num);
        });
    }
    ```
2.  **显示/隐藏元素**: 隐藏微信登录容器 (`#login_container`) 并显示传统的PC登录表单 (`.pclogin`)。
3.  **本地存储**: 用户的选择会通过 `localStorage.setItem("loginType", val)` 被记录在本地存储中，以便在下次访问页面时记住用户的选择。

## 表单提交

当用户填写完身份证号、密码和验证码后，点击“登录”按钮会触发 `loginConfirm()` 函数。该函数会进行以下操作：

1.  **输入验证**: 检查用户名（此时为身份证号）和密码是否为空。
2.  **验证码校验**: 如果开启了验证码功能，会校验用户输入的验证码是否正确。
3.  **密码加密**: 如果开启了密码加密功能，会使用 `JSEncrypt` 库对密码进行加密。
4.  **表单提交**: 如果所有验证都通过，表单将被提交到服务器进行处理。

## 后端处理

虽然前端代码没有直接显示后端处理逻辑，但可以推断出后端会根据 `AcountType` 的值来区分不同的登录方式。当 `AcountType` 为 `2` 时，后端会将 `User.Account` 字段作为身份证号进行验证。

后端的核心验证逻辑可能包括：

1.  **查询用户**: 根据身份证号查询 `UTSYSUser` 或 `UTSYSEmployee` 表，找到对应的用户。
2.  **验证密码**: 将用户输入的加密密码与数据库中存储的密码进行比对。
3.  **处理登录**: 如果验证成功，则创建用户会话并重定向到系统主页。

## 安全注意事项

- **密码加密**: 前端对密码进行了加密，这是一个很好的安全实践，可以防止密码在传输过程中被窃取。
- **登录锁定**: 系统实现了登录错误次数限制和锁定机制，可以有效防止暴力破解攻击。
- **验证码**: 验证码功能可以防止机器人自动登录。
