# 人事管理模块ER图设计

## 概述

人事管理模块是PropertySys智能物业管理系统的核心业务模块之一，负责管理企业内部人力资源的全生命周期，包括员工档案管理、入职流程、转正审批、异动管理、离职处理、员工关怀等功能。本模块采用工作流驱动的设计理念，支持完整的审批流程管理。

## 核心业务功能

1. **员工档案管理** - 完整的员工基本信息管理
2. **入职流程管理** - 员工入职全流程管理
3. **转正审批流程** - 员工试用期转正管理
4. **异动管理** - 员工岗位、薪资等变动管理  
5. **离职处理** - 员工离职申请和审批流程
6. **员工关怀** - 生日关怀、年假管理
7. **员工推荐** - 员工推荐奖励管理
8. **保险管理** - 雇主责任险管理
9. **合同管理** - 员工合同到期提醒
10. **查询统计** - 各类人事数据查询分析

## 数据库表结构

### 1. 员工基础信息表 (UTSYSEmployee)

员工详细资料表，存储员工的基本信息、联系方式、证件信息等。

**字段说明**:
- `ID` (Int64): 员工ID，主键
- `OrgID` (Int64): 所属组织ID
- `Name` (String): 员工姓名 [必填]
- `Sex` (Int32): 性别 (1=男, 2=女)
- `Birthday` (DateTime): 出生日期
- `Email` (String): 邮箱地址
- `MobilePhone` (String): 手机号码 [必填]
- `OfficePhone` (String): 办公电话
- `HomePhone` (String): 家庭电话
- `Address` (String): 住址
- `EntryDate` (DateTime): 入职日期
- `AnnualLeave` (Decimal): 年假天数
- `OffDays` (Decimal): 调休假天数
- `UserID` (Int64): 关联用户ID
- `UnitID` (Guid): 项目单位ID
- `IDCard` (String): 身份证号
- `IDCardName` (String): 身份证姓名
- `IDCardAddress` (String): 身份证地址
- `IDCardType` (Int32): 证件有效期类型
- `IDCardValidDate` (DateTime): 证件有效期
- `NationID` (Guid): 民族ID
- `MaritalStatus` (Int32): 婚姻状况
- `FirstWorkDate` (DateTime): 首次工作时间
- `PoliticalOutlook` (Int32): 政治面貌
- `SocialNumber` (String): 个人社保账号
- `BirdthMonth` (Int32): 出生月份
- `EducationType` (Int32): 学历
- `GraduationSchool` (String): 毕业院校
- `GraduationDate` (DateTime): 毕业时间
- `Major` (String): 所学专业
- `OpenBank` (String): 开户行
- `BankCard` (String): 银行卡号
- `JJContacts1` (String): 紧急联系人1姓名
- `JJContactsTel1` (String): 联系人1电话
- `JJContactsRelation1` (Int32): 联系人1关系
- `JJContacts2` (String): 紧急联系人2姓名
- `JJContactsTel2` (String): 联系人2电话
- `JJContactsRelation2` (Int32): 联系人2关系
- `IDCardBeforeFile` (String): 身份证人像面文件
- `IDCardAfterFile` (String): 身份证国徽面文件
- `EducationFile` (String): 学历证书文件
- `AcademicDegreeFile` (String): 学位证书文件
- `LastLeaveCertificate` (String): 前公司离职证明
- `BankFile` (String): 银行卡文件
- `SocialInsuranceFile` (String): 社保卡文件
- `InsuranceNo` (String): 雇主责任险保单号
- `InsuranceStartDate` (DateTime): 雇主责任险参保时间
- `InsuranceEndDate` (DateTime): 雇主责任险停保时间
- `InsuranceType` (Int32): 雇主责任险参保工种
- `SocialInsuranceNo` (String): 社保账号
- `SocialInsuranceStartDate` (DateTime): 社保参保时间
- `SocialInsuranceEndDate` (DateTime): 社保停保时间
- `IsDeparture` (Boolean): 是否离职
- `DepartureType` (Int32): 离职状态(1=辞职, 2=辞退)
- `DepartureDate` (DateTime): 离职时间
- `DepartureReason` (String): 离职原因
- `DepartureRemark` (String): 离职备注

### 2. 用户表 (UTSYSUser)

系统用户基础信息表，与员工表关联。

**字段说明**:
- `UserID` (Int64): 用户ID，主键
- `UserName` (String): 用户名称 [必填]
- `Account` (String): 账号
- `Password` (String): 密码
- `UserType` (Int32): 用户类型
- `UnitID` (Guid): 项目单位ID
- `OpenID` (String): 微信ID
- `UnionID` (String): 微信UnionID
- `MiniOpenID` (String): 小程序OpenID
- `IsDeleted` (Boolean): 删除标记

### 3. 入职管理表 (UTSYSUserEntryManage)

员工入职信息管理表，详细记录入职相关信息。

**字段说明**:
- `ID` (Guid): 主键ID
- `UserID` (Int64): 关联用户ID
- `UnitID` (Guid): 项目单位ID
- `EntryDate` (DateTime): 入职时间
- `JobType` (Int32): 员工类型 (1=全职, 2=兼职, 3=实习, 4=劳务派遣, 5=退休返聘)
- `UserStatus` (Int32): 员工状态 (1=试用, 2=正式)
- `Probation` (Int32): 试用期
- `ConfirmationDate` (DateTime): 转正日期
- `ConfirmationDatePlan` (DateTime): 计划转正日期
- `ProbationSalary` (Decimal): 试用期工资
- `ConfirmationSalary` (Decimal): 转正后工资
- `JobApplicationForm` (String): 职位申请表

### 4. 转正申请表 (UTXZRegularApply)

员工转正申请流程管理表。

**字段说明**:
- `ID` (Guid): 主键ID
- `ProcessCode` (Int32): 流程代码
- `ApplyCode` (String): 申请编号
- `ApplyUserID` (Int64): 申请人ID [必填]
- `RegularDate` (DateTime): 转正日期 [必填]
- `ApplyInfo` (String): 申请说明 [必填]
- `ApplyDate` (DateTime): 申请日期
- `Status` (Int32): 当前审批状态
- `CurrentApprovalUser` (Int64): 本次审批人
- `LastApprovalUser` (Int64): 最后审批人
- `LastApprovalDate` (DateTime): 最后审批时间
- `LastStepCode` (Int32): 最后审批步骤
- `LastApprovalResult` (Int32): 最后审批结果
- `IsOver` (Int32): 是否审批结束
- `CreateDate` (DateTime): 申请时间
- `CreateUser` (Int64): 申请人
- `ConfirmationSalary` (Decimal): 转正后工资

### 5. 转正申请附件表 (UTXZRegularApplyFile)

转正申请相关附件管理。

**字段说明**:
- `ID` (Guid): 主键ID
- `BaseID` (Guid): 转正申请ID
- `FileName` (String): 文件名
- `ShowName` (String): 显示名称
- `SordIndex` (Int32): 排序索引
- `CreateDate` (DateTime): 创建时间

### 6. 离职申请表 (UTXZDepartureApply)

员工离职申请流程管理表。

**字段说明**:
- `ID` (Guid): 主键ID
- `ProcessCode` (Int32): 流程代码
- `ApplyCode` (String): 申请编号
- `ApplyUserID` (Int64): 申请人ID [必填]
- `DepartureDate` (DateTime): 离职日期 [必填]
- `ApplyInfo` (String): 申请说明 [必填]
- `ApplyDate` (DateTime): 申请日期
- `Status` (Int32): 当前审批状态
- `CurrentApprovalUser` (Int64): 本次审批人
- `LastApprovalUser` (Int64): 最后审批人
- `LastApprovalDate` (DateTime): 最后审批时间
- `LastStepCode` (Int32): 最后审批步骤
- `LastApprovalResult` (Int32): 最后审批结果
- `IsOver` (Int32): 是否审批结束
- `CreateDate` (DateTime): 申请时间
- `CreateUser` (Int64): 申请人
- `FileName` (String): 员工文档
- `OrgID` (Int64): 申请部门
- `IsWorkTransfer` (Int32): 工作移交
- `IsRoom` (Int32): 宿舍
- `IsOfficeMaterial` (Int32): 办公用品移交
- `IsEquipment` (Int32): 设施设备移交
- `IsKey` (Int32): 钥匙移交
- `IsCommunicationTools` (Int32): 通讯工具移交
- `IsBook` (Int32): 书籍移交
- `IsNumberPlate` (Int32): 工号牌移交
- `IsAssets` (Int32): 资产移交
- `WorkStart` (DateTime): 工作开始时间
- `WorkEnd` (DateTime): 工作结束时间
- `WorkAttendance` (Int32): 工作出勤
- `LateDay` (Decimal): 迟到天数
- `Compassionatleave` (Decimal): 事假天数
- `SickLeave` (Decimal): 病假天数
- `Absenteeism` (Decimal): 旷工天数
- `WorkOvertime` (Decimal): 加班时间
- `SocialSecurity` (Int32): 社保情况
- `ClothingFee` (Decimal): 服装费
- `ClothingTime` (Int32): 服装时间
- `SelfClothingFee` (Decimal): 个人服装费
- `CompanyClothingFee` (Decimal): 公司服装费
- `IsUseMoney` (Int32): 是否使用费用
- `UseMoneyAmount` (Decimal): 使用费用金额
- `IsConfirm` (Int32): 是否确认

### 7. 异动申请表 (UTXZUserChangeApply)

员工异动申请流程管理表。

**字段说明**:
- `ID` (Guid): 主键ID
- `ProcessCode` (Int32): 流程代码
- `ApplyCode` (String): 申请编号
- `ApplyUserID` (Int64): 申请人ID [必填]
- `UseDate` (DateTime): 使用日期 [必填]
- `ApplyInfo` (String): 申请说明 [必填]
- `ApplyDate` (DateTime): 申请日期
- `Status` (Int32): 当前审批状态
- `CurrentApprovalUser` (Int64): 本次审批人
- `LastApprovalUser` (Int64): 最后审批人
- `LastApprovalDate` (DateTime): 最后审批时间
- `LastStepCode` (Int32): 最后审批步骤
- `LastApprovalResult` (Int32): 最后审批结果
- `IsOver` (Int32): 是否审批结束
- `CreateDate` (DateTime): 申请时间
- `CreateUser` (Int64): 申请人
- `FileName` (String): 员工文档
- `OrgID` (Int64): 申请部门
- `ChangeType` (Int32): 异动类型 (晋升/调薪/降级/升级/降薪/调岗) [必填]
- `UseType` (Int32): 当前岗位
- `ChangeUseType` (Int32): 异动后岗位
- `Salary` (Decimal): 当前薪资
- `ChangeSalary` (Decimal): 异动后薪资

### 8. 组织机构表 (UTSYSOrganization)

组织架构管理表。

**字段说明**:
- `OrgID` (Int64): 组织ID，主键
- `OrgName` (String): 组织名称
- `ParentOrgID` (Int64): 父级组织ID
- `UnitID` (Guid): 项目单位ID
- `IsDeleted` (Boolean): 删除标记

### 9. 项目管理表 (UTBaseUnitManage)

项目基本信息管理表。

**字段说明**:
- `UnitID` (Guid): 项目ID，主键
- `UnitName` (String): 项目名称
- `ParentUnitID` (Guid): 父级项目ID
- `UnitType` (Int32): 项目类型
- `IsDeleted` (Boolean): 删除标记

## ER图关系

```mermaid
erDiagram
    UTSYSUser ||--|| UTSYSEmployee : "用户-员工关联"
    UTSYSEmployee ||--o{ UTXZRegularApply : "员工转正申请"
    UTSYSEmployee ||--o{ UTXZDepartureApply : "员工离职申请"
    UTSYSEmployee ||--o{ UTXZUserChangeApply : "员工异动申请"
    UTSYSEmployee ||--|| UTSYSUserEntryManage : "员工入职信息"
    UTXZRegularApply ||--o{ UTXZRegularApplyFile : "转正申请附件"
    UTSYSEmployee }o--|| UTSYSOrganization : "员工所属组织"
    UTSYSOrganization }o--|| UTBaseUnitManage : "组织所属项目"
    UTSYSUser }o--|| UTBaseUnitManage : "用户所属项目"

    UTSYSUser {
        Int64 UserID PK "用户ID"
        String UserName "用户名称"
        String Account "账号"
        String Password "密码"
        Int32 UserType "用户类型"
        Guid UnitID FK "项目单位ID"
        String OpenID "微信ID"
        Boolean IsDeleted "删除标记"
    }

    UTSYSEmployee {
        Int64 ID PK "员工ID"
        Int64 OrgID FK "所属组织ID"
        String Name "员工姓名"
        Int32 Sex "性别"
        DateTime Birthday "出生日期"
        String Email "邮箱"
        String MobilePhone "手机号码"
        String Address "住址"
        DateTime EntryDate "入职日期"
        Decimal AnnualLeave "年假天数"
        Int64 UserID FK "关联用户ID"
        Guid UnitID FK "项目单位ID"
        String IDCard "身份证号"
        String IDCardName "身份证姓名"
        Int32 MaritalStatus "婚姻状况"
        DateTime FirstWorkDate "首次工作时间"
        String SocialNumber "个人社保账号"
        Int32 EducationType "学历"
        String GraduationSchool "毕业院校"
        String Major "所学专业"
        String BankCard "银行卡号"
        String JJContacts1 "紧急联系人1"
        String JJContactsTel1 "联系人1电话"
        String InsuranceNo "雇主责任险保单号"
        DateTime InsuranceStartDate "雇主责任险参保时间"
        DateTime InsuranceEndDate "雇主责任险停保时间"
        String SocialInsuranceNo "社保账号"
        DateTime SocialInsuranceStartDate "社保参保时间"
        DateTime SocialInsuranceEndDate "社保停保时间"
        Boolean IsDeparture "是否离职"
        DateTime DepartureDate "离职时间"
        String DepartureReason "离职原因"
    }

    UTSYSUserEntryManage {
        Guid ID PK "主键ID"
        Int64 UserID FK "关联用户ID"
        Guid UnitID FK "项目单位ID"
        DateTime EntryDate "入职时间"
        Int32 JobType "员工类型"
        Int32 UserStatus "员工状态"
        Int32 Probation "试用期"
        DateTime ConfirmationDate "转正日期"
        DateTime ConfirmationDatePlan "计划转正日期"
        Decimal ProbationSalary "试用期工资"
        Decimal ConfirmationSalary "转正后工资"
        String JobApplicationForm "职位申请表"
    }

    UTXZRegularApply {
        Guid ID PK "主键ID"
        Int32 ProcessCode "流程代码"
        String ApplyCode "申请编号"
        Int64 ApplyUserID FK "申请人ID"
        DateTime RegularDate "转正日期"
        String ApplyInfo "申请说明"
        DateTime ApplyDate "申请日期"
        Int32 Status "当前审批状态"
        Int64 CurrentApprovalUser "本次审批人"
        Int64 LastApprovalUser "最后审批人"
        DateTime LastApprovalDate "最后审批时间"
        Int32 LastStepCode "最后审批步骤"
        Int32 LastApprovalResult "最后审批结果"
        Int32 IsOver "是否审批结束"
        DateTime CreateDate "申请时间"
        Int64 CreateUser "申请人"
        Decimal ConfirmationSalary "转正后工资"
    }

    UTXZRegularApplyFile {
        Guid ID PK "主键ID"
        Guid BaseID FK "转正申请ID"
        String FileName "文件名"
        String ShowName "显示名称"
        Int32 SordIndex "排序索引"
        DateTime CreateDate "创建时间"
    }

    UTXZDepartureApply {
        Guid ID PK "主键ID"
        Int32 ProcessCode "流程代码"
        String ApplyCode "申请编号"
        Int64 ApplyUserID FK "申请人ID"
        DateTime DepartureDate "离职日期"
        String ApplyInfo "申请说明"
        DateTime ApplyDate "申请日期"
        Int32 Status "当前审批状态"
        Int64 CurrentApprovalUser "本次审批人"
        Int64 LastApprovalUser "最后审批人"
        DateTime LastApprovalDate "最后审批时间"
        Int32 IsOver "是否审批结束"
        DateTime CreateDate "申请时间"
        Int64 CreateUser "申请人"
        String FileName "员工文档"
        Int64 OrgID "申请部门"
        Int32 IsWorkTransfer "工作移交"
        Int32 IsRoom "宿舍"
        Int32 IsOfficeMaterial "办公用品移交"
        Int32 IsEquipment "设施设备移交"
        Decimal LateDay "迟到天数"
        Decimal SickLeave "病假天数"
        Int32 SocialSecurity "社保情况"
        Int32 IsConfirm "是否确认"
    }

    UTXZUserChangeApply {
        Guid ID PK "主键ID"
        Int32 ProcessCode "流程代码"
        String ApplyCode "申请编号"
        Int64 ApplyUserID FK "申请人ID"
        DateTime UseDate "使用日期"
        String ApplyInfo "申请说明"
        DateTime ApplyDate "申请日期"
        Int32 Status "当前审批状态"
        Int64 CurrentApprovalUser "本次审批人"
        Int64 LastApprovalUser "最后审批人"
        DateTime LastApprovalDate "最后审批时间"
        Int32 IsOver "是否审批结束"
        DateTime CreateDate "申请时间"
        Int64 CreateUser "申请人"
        String FileName "员工文档"
        Int64 OrgID "申请部门"
        Int32 ChangeType "异动类型"
        Int32 UseType "当前岗位"
        Int32 ChangeUseType "异动后岗位"
        Decimal Salary "当前薪资"
        Decimal ChangeSalary "异动后薪资"
    }

    UTSYSOrganization {
        Int64 OrgID PK "组织ID"
        String OrgName "组织名称"
        Int64 ParentOrgID "父级组织ID"
        Guid UnitID FK "项目单位ID"
        Boolean IsDeleted "删除标记"
    }

    UTBaseUnitManage {
        Guid UnitID PK "项目ID"
        String UnitName "项目名称"
        Guid ParentUnitID "父级项目ID"
        Int32 UnitType "项目类型"
        Boolean IsDeleted "删除标记"
    }
```

## 业务流程设计

### 1. 员工入职流程
1. **录入基本信息** → UTSYSEmployee + UTSYSUser
2. **完善入职信息** → UTSYSUserEntryManage
3. **上传相关证件** → 各类文件字段
4. **设置试用期信息** → Probation, ProbationSalary
5. **激活账号** → IsDeleted = false

### 2. 转正申请流程
1. **提交转正申请** → UTXZRegularApply (Status = 保存)
2. **逐级审批** → CurrentApprovalUser, LastApprovalUser
3. **审批完成** → IsOver = 1, Status = 审批完成
4. **更新员工状态** → UTSYSUserEntryManage.UserStatus = 正式
5. **附件管理** → UTXZRegularApplyFile

### 3. 异动申请流程
1. **提交异动申请** → UTXZUserChangeApply
2. **指定异动类型** → ChangeType (晋升/调薪/调岗等)
3. **设置变更信息** → ChangeUseType, ChangeSalary
4. **工作流审批** → CurrentApprovalUser流转
5. **生效执行** → 更新员工相关信息

### 4. 离职申请流程
1. **提交离职申请** → UTXZDepartureApply
2. **工作移交确认** → IsWorkTransfer, IsOfficeMaterial等
3. **考勤结算** → WorkAttendance, LateDay等
4. **费用结算** → ClothingFee, UseMoneyAmount等
5. **审批完成** → 更新UTSYSEmployee.IsDeparture = true

### 5. 员工关怀功能
- **生日提醒**: 基于UTSYSEmployee.Birthday, BirdthMonth
- **年假管理**: UTSYSEmployee.AnnualLeave, OffDays
- **合同到期提醒**: 基于合同相关字段
- **保险到期提醒**: UTSYSEmployee.InsuranceEndDate

## 设计特点

### 1. 工作流集成
- 所有审批类表都包含完整的工作流字段
- 支持多级审批和流程回退
- 审批状态和步骤跟踪

### 2. 多项目支持
- 所有表都包含UnitID字段
- 支持集团-项目多租户架构
- 数据隔离和权限控制

### 3. 完整性约束
- 外键关联保证数据一致性
- 必填字段确保核心信息完整
- 删除标记实现软删除

### 4. 扩展性设计
- 文件字段支持多媒体附件
- 枚举类型支持业务扩展
- 预留字段支持功能升级

### 5. 审计追踪
- 创建时间和创建人记录
- 审批过程完整跟踪
- 操作日志和状态变更

本设计支持完整的人事管理业务流程，具备良好的扩展性和可维护性，能够满足企业级物业管理系统的人力资源管理需求。 