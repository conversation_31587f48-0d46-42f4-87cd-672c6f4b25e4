# 客户管理模块ER图

## 表结构说明

### 核心实体表

#### UTUnitCustomer (客户基本信息表)
- **主键**: ID (Guid)
- **外键**: UnitID (所属单位ID, Guid?)
- **核心字段**: CustomerName, CustomerType, MobilePhone, Email, CustomerAttribute

#### UTKFCustomerService (客户服务表)
- **主键**: ID (Guid)
- **外键**: UnitID (单位ID, Guid?), CustomerID (客户ID, Guid?)
- **核心字段**: ServiceCode, ServiceType, SendUserName, SendUserTel, ServiceInfo, Status

#### UTKFCustomerServiceDetial (客户服务详情表)
- **主键**: ID (Guid)
- **外键**: ServiceID (服务ID, Guid?)
- **核心字段**: OperateType, OperateInfo, OperateUserID, OperateTime

#### UTEQMaintain (维修单表)
- **主键**: ID (Guid)
- **外键**: UnitID (园区ID, Guid?), CustomerUserID (客户人员ID, Guid?), CustomerID (客户ID, Guid?)
- **核心字段**: MaintainCode, FromType, IsUrgent, BadDesc, SendUser, Status

#### UTEQMaintainDetial (维修单处理详情表)
- **主键**: ID (Guid)
- **外键**: MaintainID (维修单ID, Guid?)
- **核心字段**: DetialType, BeginDeal, EndDeal, DealTime, DealUserID

#### UTUnitSatisfaction (满意度调查表)
- **主键**: ID (Guid)
- **外键**: UnitID (单位ID, Guid?)
- **核心字段**: SatisfactionName, CustomerType, SendName, UseType

#### UTUnitSatisfactionDetail (满意度调查详情表)
- **主键**: ID (Guid)
- **外键**: SatisfactionID (主表ID, Guid?), CustomerID (客户ID, Guid?)
- **核心字段**: CustomerName, SendName, CustomerType, CustomerJob, IsOK, Score

#### UTUnitSatisfactionClass (满意度调查分类表)
- **主键**: ID (Guid)
- **外键**: UnitID (项目ID, Guid?), PClassID (父分类ID, Guid?)
- **核心字段**: ClassName, ClassLevel, IsLast, OrderIndex

#### UTUnitSatisfactionQuestion (满意度调查问题表)
- **主键**: ID (Guid)
- **外键**: SatisfactionID (调查ID, Guid?)
- **核心字段**: QestionType, Question, AnswerType

#### UTBaseSysNotice (系统通知公告表)
- **主键**: ID (Guid)
- **核心字段**: NoticeTitle, NoticeContent, StartDate, EndDate, CreateUser

## ER关系图

```mermaid
erDiagram
    UTUnitCustomer ||--o{ UTKFCustomerService : "客户发起服务"
    UTUnitCustomer ||--o{ UTEQMaintain : "客户报修"
    UTUnitCustomer ||--o{ UTUnitSatisfactionDetail : "参与满意度调查"
    UTKFCustomerService ||--o{ UTKFCustomerServiceDetial : "服务处理记录"
    UTEQMaintain ||--o{ UTEQMaintainDetial : "维修处理记录"
    UTUnitSatisfaction ||--o{ UTUnitSatisfactionDetail : "调查实例"
    UTUnitSatisfaction ||--o{ UTUnitSatisfactionQuestion : "调查问题"
    UTUnitSatisfactionClass ||--|| UTUnitSatisfactionClass : "分类层级关系"
    
    UTUnitCustomer {
        Guid ID PK
        Guid UnitID FK "所属单位ID"
        string CustomerName "姓名"
        int CustomerType "用户类型(0租户1业主)"
        int Sex "性别"
        DateTime Birthday "生日"
        string Email "邮箱"
        string MobilePhone "手机号"
        string Tel "电话号码"
        int CustomerAttribute "客户属性(0个人1企业)"
        string UnitName "单位名称"
        string OpenID "微信OpenID"
        int Status "状态(0当前1历史)"
        DateTime UpdateDate "更新时间"
    }
    
    UTKFCustomerService {
        Guid ID PK
        string ServiceCode "服务单号"
        Guid UnitID FK "单位ID"
        Guid CustomerID FK "客户ID"
        int ServiceType "服务类型(0报修1投诉2咨询)"
        string SendUserName "发送人姓名"
        string SendUserTel "电话"
        DateTime SendTime "发送时间"
        string ServiceInfo "服务内容"
        int Status "状态"
        DateTime CreateDate "创建时间"
        int64 ManagerUser "负责人"
        string DealDesc "处理说明"
        int DealResult "处理结果"
        DateTime DealTime "处理时间"
        int64 HFUser "回访人"
        DateTime HFTime "回访时间"
        int HFLevel "回访满意度"
        int HFResult "回访结果"
    }
    
    UTKFCustomerServiceDetial {
        Guid ID PK
        Guid ServiceID FK "服务ID"
        int OperateType "操作类型"
        string OperateInfo "操作内容"
        int64 OperateUserID "操作人"
        DateTime OperateTime "操作时间"
    }
    
    UTEQMaintain {
        Guid ID PK
        Guid UnitID FK "园区ID"
        Guid CustomerUserID FK "客户人员ID"
        Guid CustomerID FK "客户ID"
        string MaintainCode "维修单编号"
        int FromType "维修单来源"
        int BaseType "报修类型"
        int IsUrgent "是否紧急"
        string BadDesc "问题描述"
        string BadSite "问题位置"
        string SendUser "发送人"
        string SendUserTel "发送电话"
        DateTime SendTime "发送时间"
        DateTime ExpectTimeS "期望开始时间"
        DateTime ExpectTimeE "期望结束时间"
        int64 MaintainUserID "维修人"
        DateTime MaintainDate "接单时间"
        int Status "状态"
        DateTime CreateDate "创建时间"
        string MaintainDesc "维修描述"
        DateTime MaintainStart "维修开始时间"
        DateTime MaintainEnd "维修结束时间"
        int Score "评分"
        string ScoreDesc "评分说明"
    }
    
    UTEQMaintainDetial {
        Guid ID PK
        Guid MaintainID FK "维修单ID"
        int DetialType "明细类型"
        string BeginDeal "处理前说明"
        string EndDeal "处理后说明"
        DateTime DealTime "处理时间"
        int64 DealUserID "处理人"
        string DealUserName "处理人姓名"
        string Remark "备注"
    }
    
    UTUnitSatisfaction {
        Guid ID PK
        Guid UnitID FK "单位ID"
        string SatisfactionName "调查名称"
        DateTime CreateDate "创建时间"
        int64 CreateUser "创建人"
        string Attachment "附件"
        int CustomerType "客户类型"
        string SendName "发送人名称"
        string CustomerJob "客户职位"
        int IsScore "是否评分"
        int UseType "使用类型"
    }
    
    UTUnitSatisfactionDetail {
        Guid ID PK
        Guid SatisfactionID FK "主表ID"
        Guid CustomerID FK "客户ID"
        string CustomerName "客户名称"
        string SendName "发送客户"
        string DownloadAttachment "下载调查表"
        string UpAttachment "上传调查表"
        int CustomerType "客户类型(1重点2大众)"
        string CustomerJob "职位"
        int IsSend "是否发送"
        DateTime CreateDate "创建时间"
        int64 CreateUser "创建人"
        int IsOK "是否完成(1完成2未完成)"
        int Score "得分"
        string Telephone "电话"
        string OpenID "微信OpenID"
    }
    
    UTUnitSatisfactionClass {
        Guid ID PK
        Guid UnitID FK "项目ID"
        Guid PClassID FK "父分类ID"
        string ClassName "类名"
        int ClassLevel "分类级别"
        int IsLast "是否最终级别"
        int OrderIndex "排序"
        string Remark "备注"
        DateTime CreateDate "创建时间"
        int64 CreateUser "创建人"
    }
    
    UTUnitSatisfactionQuestion {
        Guid ID PK
        Guid SatisfactionID FK "调查ID"
        int QestionType "问题类型(1单选2多选3简答)"
        string Question "问题内容"
        DateTime Lastupdate "最后更新"
        string Remark "备注"
        DateTime CreateDate "创建时间"
        int64 CreateUser "创建人"
        int AnswerType "答案类型(1十分制2五档制)"
    }
    
    UTBaseSysNotice {
        Guid ID PK
        string NoticeTitle "标题"
        string NoticeContent "内容"
        string NoticeAttachment "附件"
        int64 CreateUser "创建人"
        DateTime CreateDate "创建时间"
        DateTime StartDate "开始时间"
        DateTime EndDate "结束时间"
        int IsDelete "是否删除"
    }
```

## 业务关系说明

1. **客户基础管理**: UTUnitCustomer表存储客户基本信息，支持个人用户和企业用户，关联微信OpenID
2. **客户服务管理**: UTKFCustomerService表管理投诉、咨询等服务，通过UTKFCustomerServiceDetial记录处理过程
3. **报事报修管理**: UTEQMaintain表管理维修单，UTEQMaintainDetial记录维修处理详情和状态变更
4. **满意度调查**: UTUnitSatisfaction为调查主表，UTUnitSatisfactionDetail记录具体参与情况，UTUnitSatisfactionQuestion定义问题内容
5. **调查分类管理**: UTUnitSatisfactionClass实现分类层级管理，支持树形结构
6. **公示公告管理**: UTBaseSysNotice管理系统通知和公告信息，支持时间范围控制 