# PropertySys数据库设计文档总览

**最后更新时间：** 2025-06-24 23:30:07 +08:00

## 📋 文档统计概览

| 模块类型 | ER图文档 | UML类图文档 | 核心表数量 | 文档大小 |
|---------|---------|------------|----------|----------|
| 集团管理模块 | ✅ | ✅ | 6张表 | 12KB |
| 片区管理模块 | ✅ | ✅ | 4张表 | 15KB |
| 设备设施管理模块 | ✅ | ✅ | 10张表 | 20KB |
| 巡检管理模块 | ✅ | ✅ | 6张表 | 21KB |
| 物料管理模块 | ✅ | ✅ | 4张表 | 25KB |
| 采购管理模块 | ✅ | ✅ | 5张表 | 23KB |
| 客户管理模块 | ✅ | ✅ | 10张表 | 20KB |
| 行政OA管理模块 | ✅ | ✅ | 11张表 | 28KB |
| 人事管理模块 | ✅ | ✅ | 9张表 | 30KB |
| 考勤管理模块 | ✅ | ✅ | 9张表 | 35KB |
| 系统权限管理模块 | ✅ | ✅ | 13张表 | 32KB |
| **总计** | **11个模块** | **22个文档** | **67张不重复表** | **261KB** |

## 📊 模块核心表结构详细统计

### 1. 集团管理模块 (6张表)

| 表名 | 表用途 | 主要功能 |
|------|--------|----------|
| UTBaseUnitManage | 集团项目架构表 | 管理集团-分公司-项目三级架构 |
| UTSYSOrganization | 组织机构部门表 | 部门层级管理和权限控制 |
| UTWLItemClass | 物料分类表 | 集团级物料分类标准化 |
| UTBaseEQClass | 设备分类表 | 集团级设备分类体系 |
| UTBaseCheckInfo | 设备各类检查项表 | 集团级巡检标准配置 |
| UTBaseBadCode | 故障代码表 | 统一的故障代码管理 |

**文档链接**: [ER图](集团管理模块ER图.md) | [UML类图](集团管理模块UML类图.md)

### 2. 片区管理模块 (4张表)

| 表名 | 表用途 | 主要功能 |
|------|--------|----------|
| UTBaseUnitManage | 项目基本信息表 | 项目基础信息和联系方式 |
| UTSYSOrganization | 组织机构部门表 | 项目级组织架构管理 |
| UTUnitBuild | 建筑物表 | 项目内建筑物和区域管理 |
| UTEQEquipment | 设备信息表 | 项目设备台账管理 |

**文档链接**: [ER图](片区管理模块ER图.md) | [UML类图](片区管理模块UML类图.md)

### 3. 设备设施管理模块 (10张表)

| 表名 | 表用途 | 主要功能 |
|------|--------|----------|
| UTEQEquipment | 设备信息表 | 设备台账和基本信息 |
| UTBaseEQClass | 设备分类表 | 设备多级分类管理 |
| UTBaseEQSystem | 设备系统表 | 设备系统分类管理 |
| UTEQInspectTaskDetial | 巡检任务详情表 | 巡检任务执行记录 |
| UTEQInspectTaskDetialInfo | 巡检任务详情内容表 | 巡检检查项执行详情 |
| UTEQMaintain | 维修单表 | 设备维修申请管理 |
| UTEQMaintainDetial | 维修单处理表 | 维修处理过程记录 |
| UTBaseCheckInfo | 设备检查项表 | 设备检查标准配置 |
| UTBaseUnitManage | 项目管理表 | 项目基础信息关联 |
| UTUnitBuild | 建筑物信息表 | 设备安装位置管理 |

**文档链接**: [ER图](设备设施管理模块ER图.md) | [UML类图](设备设施管理模块UML类图.md)

### 4. 巡检管理模块 (6张表)

| 表名 | 表用途 | 主要功能 |
|------|--------|----------|
| UTEQInspectPlan | 巡检计划表 | 巡检计划制定和管理 |
| UTEQInspectTaskDetial | 巡检任务详情表 | 具体巡检任务安排 |
| UTEQInspectTaskDetialInfo | 巡检任务详情内容表 | 巡检执行结果记录 |
| UTBaseCheckInfo | 设备检查项表 | 巡检标准和检查项 |
| UTEQEquipment | 设备信息表 | 巡检对象设备信息 |
| UTBaseEQClass | 设备分类表 | 设备分类关联巡检 |

**文档链接**: [ER图](巡检管理模块ER图.md) | [UML类图](巡检管理模块UML类图.md)

### 5. 物料管理模块 (4张表)

| 表名 | 表用途 | 主要功能 |
|------|--------|----------|
| UTWLItem | 物料信息表 | 物料基础信息和编码 |
| UTWLItemClass | 物料分类表 | 物料树形分类管理 |
| UTWLStock | 库存表 | 物料实时库存管理 |
| UTWLWareHouse | 仓库表 | 仓库基础信息管理 |

**文档链接**: [ER图](物料管理模块ER图.md) | [UML类图](物料管理模块UML类图.md)

### 6. 采购管理模块 (5张表)

| 表名 | 表用途 | 主要功能 |
|------|--------|----------|
| UTWLItemApply | 采购申请单表 | 采购申请流程管理 |
| UTWLItemApplyDetial | 采购申请明细表 | 采购物料明细信息 |
| UTWLItem | 物料信息表 | 采购物料基础信息 |
| UTWLItemClass | 物料分类表 | 采购物料分类关联 |
| UTBaseSupplier | 供应商管理表 | 供应商信息和资质 |

**文档链接**: [ER图](采购管理模块ER图.md) | [UML类图](采购管理模块UML类图.md)

### 7. 客户管理模块 (10张表)

| 表名 | 表用途 | 主要功能 |
|------|--------|----------|
| UTUnitCustomer | 客户基本信息表 | 客户档案和联系方式 |
| UTKFCustomerService | 客户服务表 | 投诉咨询服务管理 |
| UTKFCustomerServiceDetial | 客户服务详情表 | 服务处理过程记录 |
| UTEQMaintain | 维修单表 | 客户报修单管理 |
| UTEQMaintainDetial | 维修单处理详情表 | 报修处理过程记录 |
| UTUnitSatisfaction | 满意度调查表 | 满意度调查计划 |
| UTUnitSatisfactionDetail | 满意度调查详情表 | 调查参与和结果 |
| UTUnitSatisfactionClass | 满意度调查分类表 | 调查分类管理 |
| UTUnitSatisfactionQuestion | 满意度调查问题表 | 调查问题设计 |
| UTBaseSysNotice | 系统通知公告表 | 公示公告发布 |

**文档链接**: [ER图](客户管理模块ER图.md) | [UML类图](客户管理模块UML类图.md)

### 8. 行政OA管理模块 (11张表)

| 表名 | 表用途 | 主要功能 |
|------|--------|----------|
| UTUnitFeeApply | 费用报销申请表 | 报销申请流程管理 |
| UTUnitFeeApplyDetail | 费用报销明细表 | 报销明细和项目分摊 |
| UTUnitFeeApplyDetailFile | 报销明细图片表 | 报销凭证和照片 |
| UTUnitEvection | 出差申请表 | 出差申请和审批 |
| UTUnitEvectionDetail | 出差记录明细表 | 出差工作记录 |
| UTCWAdvance | 预支单表 | 预支申请和管理 |
| UTCWAdvanceDetail | 预支归还记录表 | 预支归还流水 |
| UTCWAdvanceFile | 预支单附件表 | 预支相关附件 |
| UTUnitCompanyCar | 车辆管理表 | 公司车辆信息 |
| UTUnitCompanyCarApply | 车辆申请表 | 用车申请和审批 |
| UTSYSUser | 用户表 | 申请人和审批人 |

**文档链接**: [ER图](行政OA管理模块ER图.md) | [UML类图](行政OA管理模块UML类图.md)

### 9. 人事管理模块 (9张表)

| 表名 | 表用途 | 主要功能 |
|------|--------|----------|
| UTSYSEmployee | 员工基础信息表 | 员工档案和详细信息 |
| UTSYSUser | 用户表 | 系统用户账号管理 |
| UTSYSUserEntryManage | 入职管理表 | 员工入职流程管理 |
| UTXZRegularApply | 转正申请表 | 员工转正申请流程 |
| UTXZRegularApplyFile | 转正申请附件表 | 转正相关附件 |
| UTXZDepartureApply | 离职申请表 | 员工离职申请流程 |
| UTXZUserChangeApply | 异动申请表 | 员工岗位异动申请 |
| UTSYSOrganization | 组织机构表 | 部门和岗位管理 |
| UTBaseUnitManage | 项目管理表 | 项目信息关联 |

**文档链接**: [ER图](人事管理模块ER图.md) | [UML类图](人事管理模块UML类图.md)

### 10. 考勤管理模块 (9张表)

| 表名 | 表用途 | 主要功能 |
|------|--------|----------|
| UTBaseAttPoint | 考勤打卡点表 | 考勤地点和GPS配置 |
| UTUnitAttRecord | 考勤记录表 | 员工考勤打卡记录 |
| UTUnitShiftsType | 班次类型表 | 班次时间和规则配置 |
| UTUnitSchedualShifts | 排班管理表 | 员工排班计划 |
| UTUnitLeave | 请假管理表 | 请假申请和审批 |
| UTUnitOvertime | 加班管理表 | 加班申请和工时统计 |
| UTUnitSchedualCardApply | 补卡管理表 | 补卡申请和处理 |
| UTDingDingAttendanceRecordPush | 钉钉考勤集成表 | 钉钉考勤数据同步 |
| UTSYSUserYearHoliday | 年假管理表 | 员工年假额度管理 |

**文档链接**: [ER图](考勤管理模块ER图.md) | [UML类图](考勤管理模块UML类图.md)

### 11. 系统权限管理模块 (13张表)

| 表名 | 表用途 | 主要功能 |
|------|--------|----------|
| UTSYSUser | 系统用户表 | 用户账户管理，支持多平台登录 |
| UTSYSRole | 系统角色表 | 角色定义和权限管理 |
| UTSYSRight | 系统权限表 | 菜单权限树形结构管理 |
| UTSYSRoleRight | 角色权限关系表 | 角色与权限的多对多关联 |
| UTSYSUserRole | 用户角色关系表 | 用户与角色的多对多关联 |
| UTSYSSystem | 子系统信息表 | 子系统管理和权限隔离 |
| UTBasePostAuth | 岗位权限表 | 基于岗位的操作权限管理 |
| UTBasePostAuthDetail | 岗位权限详情表 | 岗位权限的具体操作明细 |
| UTBaseCodeGroup | 代码组表 | 数据字典分组管理 |
| UTBaseCode | 代码表 | 数据字典详细配置管理 |
| UTBaseWechatAdvertise | 微信广告表 | 微信生态广告内容管理 |
| UTBaseWechatAdvertiseDetial | 微信广告详情表 | 广告详细内容和配置管理 |
| UTBaseWechatAdvertiseDetialFile | 微信广告文件表 | 广告附件和多媒体文件管理 |

**文档链接**: [ER图](系统权限管理模块ER图.md) | [UML类图](系统权限管理模块UML类图.md)

## 📈 数据库表去重统计分析

### 表结构去重统计

经过对所有模块表结构的详细分析，**去重后系统共使用67张不重复的核心表**：

#### 基础管理类表 (4张)
- `UTBaseUnitManage` - 集团项目架构管理
- `UTSYSOrganization` - 组织机构管理  
- `UTSYSUser` - 系统用户管理
- `UTSYSEmployee` - 员工基础信息

#### 设备设施类表 (10张)
- `UTEQEquipment` - 设备信息管理
- `UTBaseEQClass` - 设备分类体系
- `UTBaseEQSystem` - 设备系统分类
- `UTBaseCheckInfo` - 设备检查标准
- `UTEQInspectPlan` - 巡检计划管理
- `UTEQInspectTaskDetial` - 巡检任务详情
- `UTEQInspectTaskDetialInfo` - 巡检执行记录
- `UTEQMaintain` - 维修单管理
- `UTEQMaintainDetial` - 维修处理记录
- `UTBaseBadCode` - 故障代码管理

#### 物料采购类表 (7张)
- `UTWLItem` - 物料信息管理
- `UTWLItemClass` - 物料分类体系
- `UTWLStock` - 库存管理
- `UTWLWareHouse` - 仓库管理
- `UTWLItemApply` - 采购申请管理
- `UTWLItemApplyDetial` - 采购明细管理
- `UTBaseSupplier` - 供应商管理

#### 客户服务类表 (7张)
- `UTUnitCustomer` - 客户信息管理
- `UTKFCustomerService` - 客户服务管理
- `UTKFCustomerServiceDetial` - 服务处理记录
- `UTUnitSatisfaction` - 满意度调查
- `UTUnitSatisfactionDetail` - 调查详情记录
- `UTUnitSatisfactionClass` - 调查分类管理
- `UTUnitSatisfactionQuestion` - 调查问题设计
- `UTBaseSysNotice` - 系统通知公告

#### 建筑管理类表 (1张)
- `UTUnitBuild` - 建筑物信息管理

#### 行政OA类表 (10张)
- `UTUnitFeeApply` - 报销申请管理
- `UTUnitFeeApplyDetail` - 报销明细管理
- `UTUnitFeeApplyDetailFile` - 报销附件管理
- `UTUnitEvection` - 出差申请管理
- `UTUnitEvectionDetail` - 出差记录管理
- `UTCWAdvance` - 预支单管理
- `UTCWAdvanceDetail` - 预支归还记录
- `UTCWAdvanceFile` - 预支附件管理
- `UTUnitCompanyCar` - 车辆信息管理
- `UTUnitCompanyCarApply` - 用车申请管理

#### 人事管理类表 (6张)
- `UTSYSUserEntryManage` - 入职流程管理
- `UTXZRegularApply` - 转正申请管理
- `UTXZRegularApplyFile` - 转正附件管理
- `UTXZDepartureApply` - 离职申请管理
- `UTXZUserChangeApply` - 异动申请管理

#### 考勤管理类表 (9张)
- `UTBaseAttPoint` - 考勤打卡点管理
- `UTUnitAttRecord` - 考勤记录管理
- `UTUnitShiftsType` - 班次类型管理
- `UTUnitSchedualShifts` - 排班计划管理
- `UTUnitLeave` - 请假申请管理
- `UTUnitOvertime` - 加班申请管理
- `UTUnitSchedualCardApply` - 补卡申请管理
- `UTDingDingAttendanceRecordPush` - 钉钉考勤集成
- `UTSYSUserYearHoliday` - 年假额度管理

#### 系统权限管理类表 (13张)
- `UTSYSUser` - 系统用户账户管理
- `UTSYSRole` - 系统角色定义管理
- `UTSYSRight` - 系统权限菜单管理
- `UTSYSRoleRight` - 角色权限关联管理
- `UTSYSUserRole` - 用户角色关联管理
- `UTSYSSystem` - 子系统信息管理
- `UTBasePostAuth` - 岗位权限管理
- `UTBasePostAuthDetail` - 岗位权限详情管理
- `UTBaseCodeGroup` - 数据字典分组管理
- `UTBaseCode` - 数据字典条目管理
- `UTBaseWechatAdvertise` - 微信广告内容管理
- `UTBaseWechatAdvertiseDetial` - 微信广告详情管理
- `UTBaseWechatAdvertiseDetialFile` - 微信广告文件管理

### 表重用统计分析

以下是在多个模块中被重复使用的核心表：

| 表名 | 使用模块数 | 使用模块 |
|------|-----------|----------|
| UTBaseUnitManage | 8个模块 | 集团管理、片区管理、设备设施、巡检、客户、行政OA、人事、考勤 |
| UTSYSOrganization | 3个模块 | 集团管理、片区管理、人事管理 |
| UTSYSUser | 2个模块 | 人事管理、行政OA管理 |
| UTEQEquipment | 3个模块 | 片区管理、设备设施、巡检管理 |
| UTBaseEQClass | 3个模块 | 集团管理、设备设施、巡检管理 |
| UTBaseCheckInfo | 3个模块 | 集团管理、设备设施、巡检管理 |
| UTWLItem | 2个模块 | 物料管理、采购管理 |
| UTWLItemClass | 3个模块 | 集团管理、物料管理、采购管理 |
| UTEQMaintain | 2个模块 | 设备设施、客户管理 |
| UTEQMaintainDetial | 2个模块 | 设备设施、客户管理 |

## 🎯 系统概述

PropertySys智能物业管理系统是一套企业级的物业管理解决方案，采用.NET Framework 4.8多层架构设计，涵盖物业管理的全业务流程。系统支持多租户架构，集成工作流引擎，提供完整的移动端支持和实时监控功能。

### 核心特点

- **🏢 企业级架构**: .NET Framework 4.8 多层架构设计 (Entity→Dal→Biz→Web→Server)
- **👥 多租户支持**: 集团-分公司-项目三级管理体系，数据隔离
- **🔄 工作流集成**: 集成工作流引擎，支持复杂业务审批流程
- **📱 移动端支持**: 微信生态集成，支持移动办公和小程序
- **⚡ 实时监控**: 消防监控、设备状态实时监控告警
- **🔐 安全架构**: 基于组织架构的权限体系，多级数据权限控制

## 🗂️ 快速导航

### 基础管理模块
| 模块名称 | ER图文档 | UML类图文档 | 核心功能 |
|---------|---------|------------|----------|
| [集团管理模块](#1-集团管理模块) | [ER图](集团管理模块ER图.md) | [UML类图](集团管理模块UML类图.md) | 集团级配置、组织架构 |
| [片区管理模块](#2-片区管理模块) | [ER图](片区管理模块ER图.md) | [UML类图](片区管理模块UML类图.md) | 项目管理、建筑物管理 |

### 设备设施管理
| 模块名称 | ER图文档 | UML类图文档 | 核心功能 |
|---------|---------|------------|----------|
| [设备设施管理模块](#3-设备设施管理模块) | [ER图](设备设施管理模块ER图.md) | [UML类图](设备设施管理模块UML类图.md) | 设备台账、维修管理 |
| [巡检管理模块](#4-巡检管理模块) | [ER图](巡检管理模块ER图.md) | [UML类图](巡检管理模块UML类图.md) | 巡检计划、任务执行 |

### 物料供应链管理
| 模块名称 | ER图文档 | UML类图文档 | 核心功能 |
|---------|---------|------------|----------|
| [物料管理模块](#5-物料管理模块) | [ER图](物料管理模块ER图.md) | [UML类图](物料管理模块UML类图.md) | 库存管理、智能报警 |
| [采购管理模块](#6-采购管理模块) | [ER图](采购管理模块ER图.md) | [UML类图](采购管理模块UML类图.md) | 采购流程、供应商管理 |

### 客户服务管理
| 模块名称 | ER图文档 | UML类图文档 | 核心功能 |
|---------|---------|------------|----------|
| [客户管理模块](#7-客户管理模块) | [ER图](客户管理模块ER图.md) | [UML类图](客户管理模块UML类图.md) | 客户服务、满意度调查 |

### 企业内部管理
| 模块名称 | ER图文档 | UML类图文档 | 核心功能 |
|---------|---------|------------|----------|
| [行政OA管理模块](#8-行政oa管理模块) | [ER图](行政OA管理模块ER图.md) | [UML类图](行政OA管理模块UML类图.md) | 报销、出差、用车管理 |
| [人事管理模块](#9-人事管理模块) | [ER图](人事管理模块ER图.md) | [UML类图](人事管理模块UML类图.md) | 员工全生命周期管理 |
| [考勤管理模块](#10-考勤管理模块) | [ER图](考勤管理模块ER图.md) | [UML类图](考勤管理模块UML类图.md) | 智能考勤、钉钉集成 |

### 系统管理
| 模块名称 | ER图文档 | UML类图文档 | 核心功能 |
|---------|---------|------------|----------|
| [系统权限管理模块](#11-系统权限管理模块) | [ER图](系统权限管理模块ER图.md) | [UML类图](系统权限管理模块UML类图.md) | RBAC权限体系、用户角色管理 |

## 🏗️ 技术架构

### 系统架构层次
```
┌─────────────────────────────────────────┐
│              Web展示层 (PropertySys_ZGHQ.Web)              │
├─────────────────────────────────────────┤
│             业务逻辑层 (PropertySys_ZGHQ.Biz)              │
├─────────────────────────────────────────┤
│            数据访问层 (PropertySys_ZGHQ.Dal)               │
├─────────────────────────────────────────┤
│            实体数据层 (PropertySys_ZGHQ.Entity)            │
├─────────────────────────────────────────┤
│                SQL Server 数据库                 │
└─────────────────────────────────────────┘
```

### 核心技术栈
- **开发框架**: .NET Framework 4.8
- **数据库**: SQL Server + NHibernate ORM
- **架构模式**: Entity→Dal→Biz→Web→Server
- **工作流引擎**: 集成工作流引擎支持审批流程
- **移动端**: 微信生态集成 (公众号 + 小程序)
- **监控系统**: 实时告警和监控系统

### 数据关系特征
1. **🌳 层级结构**: 支持树形数据组织 (集团-分公司-项目)
2. **🏢 多租户**: 项目间数据隔离 (基于UnitManageID)
3. **📐 标准化**: 集团级配置可下沉到项目级
4. **🔐 权限控制**: 基于组织架构的多级权限体系
5. **🔄 工作流集成**: 支持复杂业务审批流程
6. **📊 实时监控**: 设备状态、消防系统实时监控

### 设计模式应用
- **Repository模式**: 数据访问层封装
- **Service模式**: 业务逻辑封装  
- **MVC模式**: 表现层架构
- **Composite模式**: 层级结构支持
- **Strategy模式**: 业务规则策略化
- **Observer模式**: 事件驱动通知
- **Factory模式**: 对象创建管理
- **Unit of Work模式**: 事务管理

## 📊 业务流程覆盖

### 1. 设备设施全生命周期
```mermaid
graph LR
    A[台账管理] --> B[巡检计划]
    B --> C[现场执行]
    C --> D[问题发现]
    D --> E[维修处理]
    E --> F[数据分析]
    F --> A
```

### 2. 物料采购供应链
```mermaid
graph LR
    A[需求申请] --> B[采购计划]
    B --> C[供应商管理]
    C --> D[入库管理]
    D --> E[库存监控]
    E --> F[出库使用]
    F --> A
```

### 3. 客户服务闭环
```mermaid
graph LR
    A[客户档案] --> B[服务受理]
    B --> C[问题处理]
    C --> D[满意度调查]
    D --> E[服务改进]
    E --> A
```

### 4. 人事管理全流程
```mermaid
graph LR
    A[员工入职] --> B[转正申请]
    B --> C[岗位异动]
    C --> D[离职处理]
    D --> E[员工关怀]
    E --> F[数据统计]
```

### 5. 考勤管理流程
```mermaid
graph LR
    A[基础设置] --> B[班次排班]
    B --> C[考勤打卡]
    C --> D[请假申请]
    D --> E[加班管理]
    E --> F[补卡申请]
    F --> G[统计分析]
```

## 📚 模块详细说明

### 1. 集团管理模块
🎯 **管理集团层级的核心配置和标准化设置**

**核心表结构 (6张表)**:
- `UTBaseUnitManage` - 集团项目架构
- `UTSYSOrganization` - 组织机构  
- `UTWLItemClass` - 物料分类
- `UTBaseEQClass` - 设备分类
- `UTBaseCheckInfo` - 巡检项配置
- `UTBaseBadCode` - 故障代码

**文档**:
- [集团管理模块ER图](集团管理模块ER图.md)
- [集团管理模块UML类图](集团管理模块UML类图.md)

### 2. 片区管理模块
🎯 **管理具体项目的日常运营和设施设备**

**核心表结构 (4张表)**:
- `UTBaseUnitManage` - 项目基本信息
- `UTSYSOrganization` - 项目组织架构
- `UTUnitBuild` - 建筑物管理
- `UTEQEquipment` - 设备设施

**文档**:
- [片区管理模块ER图](片区管理模块ER图.md)
- [片区管理模块UML类图](片区管理模块UML类图.md)

### 3. 设备设施管理模块
🎯 **设备台账管理、巡检维修、分析统计**

**核心表结构 (10张表)**:
- `UTEQEquipment` - 设备信息
- `UTBaseEQClass` - 设备分类
- `UTBaseEQSystem` - 设备系统
- `UTEQInspectTaskDetial` - 巡检任务详情
- `UTEQInspectTaskDetialInfo` - 巡检内容
- `UTEQMaintain` - 维修单
- `UTEQMaintainDetial` - 维修详情
- `UTBaseCheckInfo` - 检查项
- `UTBaseUnitManage` - 项目管理
- `UTUnitBuild` - 建筑信息

**业务功能**:
- 设备台账管理、多维度巡检分析、多维度报修分析、设备台数分析

**文档**:
- [设备设施管理模块ER图](设备设施管理模块ER图.md)
- [设备设施管理模块UML类图](设备设施管理模块UML类图.md)

### 4. 巡检管理模块
🎯 **全流程巡检计划-任务-执行-整改管理**

**核心表结构 (6张表)**:
- `UTEQInspectPlan` - 巡检计划
- `UTEQInspectTaskDetial` - 巡检任务详情
- `UTEQInspectTaskDetialInfo` - 巡检内容
- `UTBaseCheckInfo` - 检查项
- `UTEQEquipment` - 设备信息
- `UTBaseEQClass` - 设备分类

**业务功能**:
- 巡检计划制定、任务分配、现场执行、问题整改、数据统计

**文档**:
- [巡检管理模块ER图](巡检管理模块ER图.md)
- [巡检管理模块UML类图](巡检管理模块UML类图.md)

### 5. 物料管理模块
🎯 **智能物料库存管理、多仓库支持、自动报警**

**核心表结构 (4张表)**:
- `UTWLItem` - 物料信息
- `UTWLItemClass` - 物料分类
- `UTWLStock` - 库存管理
- `UTWLWareHouse` - 仓库管理

**业务功能**:
- 树形分类管理、多仓库库存、智能报警、自动编码生成

**文档**:
- [物料管理模块ER图](物料管理模块ER图.md)
- [物料管理模块UML类图](物料管理模块UML类图.md)

### 6. 采购管理模块
🎯 **完整采购流程管理、三种采购方式、审批流程**

**核心表结构 (5张表)**:
- `UTWLItemApply` - 物料申请
- `UTWLItemApplyDetial` - 申请详情
- `UTWLItem` - 物料信息
- `UTWLItemClass` - 物料分类
- `UTBaseSupplier` - 供应商

**业务功能**:
- 支持直接采购、申请采购、计划采购三种方式，完整审批流程

**文档**:
- [采购管理模块ER图](采购管理模块ER图.md)
- [采购管理模块UML类图](采购管理模块UML类图.md)

### 7. 客户管理模块
🎯 **客户服务全流程管理、满意度调查、公示公告**

**核心表结构 (10张表)**:
- `UTUnitCustomer` - 客户基本信息
- `UTKFCustomerService` - 客户投诉咨询
- `UTKFCustomerServiceDetial` - 服务详情
- `UTEQMaintain` - 维修单
- `UTEQMaintainDetial` - 维修详情
- `UTUnitSatisfaction` - 满意度调查
- `UTUnitSatisfactionDetail` - 调查详情
- `UTUnitSatisfactionClass` - 调查分类
- `UTUnitSatisfactionQuestion` - 调查问题
- `UTBaseSysNotice` - 系统通知

**业务功能**:
- 客户信息管理、投诉咨询处理、报事报修、满意度调查、公示公告

**文档**:
- [客户管理模块ER图](客户管理模块ER图.md)
- [客户管理模块UML类图](客户管理模块UML类图.md)

### 8. 行政OA管理模块
🎯 **企业内部行政事务管理、审批流程、资源管理**

**核心表结构 (11张表)**:
- `UTUnitFeeApply` - 报销申请
- `UTUnitFeeApplyDetail` - 报销详情
- `UTUnitFeeApplyDetailFile` - 报销附件
- `UTUnitEvection` - 出差申请
- `UTUnitEvectionDetail` - 出差明细
- `UTCWAdvance` - 预支单
- `UTCWAdvanceDetail` - 预支归还记录
- `UTCWAdvanceFile` - 预支附件
- `UTUnitCompanyCar` - 车辆管理
- `UTUnitCompanyCarApply` - 用车申请
- 等...

**业务功能**:
- 报销管理、出差管理、预支管理、用车管理，集成工作流引擎实现完整审批流程

**文档**:
- [行政OA管理模块ER图](行政OA管理模块ER图.md)
- [行政OA管理模块UML类图](行政OA管理模块UML类图.md)

### 9. 人事管理模块 🆕
🎯 **员工全生命周期管理、工作流审批、人力资源统计**

**核心表结构 (9张表)**:
- `UTSYSEmployee` - 员工基础信息
- `UTSYSUser` - 用户账户
- `UTSYSUserEntryManage` - 入职管理
- `UTXZRegularApply` - 转正申请
- `UTXZRegularApplyFile` - 转正附件
- `UTXZDepartureApply` - 离职申请
- `UTXZUserChangeApply` - 异动申请
- 等...

**业务功能**:
- 入职管理、转正管理、异动管理、离职管理、员工关怀、推荐管理，支持完整的人事审批流程

**文档**:
- [人事管理模块ER图](人事管理模块ER图.md)
- [人事管理模块UML类图](人事管理模块UML类图.md)

### 10. 考勤管理模块 🆕
🎯 **智能考勤管理、班次排班、请假加班、钉钉集成**

**核心表结构 (9张表)**:
- `UTBaseAttPoint` - 考勤打卡点
- `UTUnitAttRecord` - 考勤记录
- `UTUnitShiftsType` - 班次类型
- `UTUnitSchedualShifts` - 排班管理
- `UTUnitLeave` - 请假申请
- `UTUnitOvertime` - 加班申请
- `UTUnitSchedualCardApply` - 补卡申请
- `UTDingDingAttendanceRecordPush` - 钉钉考勤推送
- `UTSYSUserYearHoliday` - 年假管理

**业务功能**:
- 考勤打卡管理、班次配置排班、请假加班审批、补卡异常处理、钉钉生态集成、年假额度管理，支持完整的智能考勤体系

**文档**:
- [考勤管理模块ER图](考勤管理模块ER图.md)
- [考勤管理模块UML类图](考勤管理模块UML类图.md)

### 11. 系统权限管理模块 🆕
🎯 **RBAC权限体系、用户角色管理、数据字典、微信生态集成**

**核心表结构 (13张表)**:
- `UTSYSUser` - 系统用户表
- `UTSYSRole` - 系统角色表
- `UTSYSRight` - 系统权限表
- `UTSYSRoleRight` - 角色权限关系表
- `UTSYSUserRole` - 用户角色关系表
- `UTSYSSystem` - 子系统信息表
- `UTBasePostAuth` - 岗位权限表
- `UTBasePostAuthDetail` - 岗位权限详情表
- `UTBaseCodeGroup` - 代码组表
- `UTBaseCode` - 代码表
- `UTBaseWechatAdvertise` - 微信广告表
- `UTBaseWechatAdvertiseDetial` - 微信广告详情表
- `UTBaseWechatAdvertiseDetialFile` - 微信广告文件表

**业务功能**:
- 基于角色的访问控制(RBAC)权限体系、用户账户管理(支持微信/钉钉集成)、角色定义和权限分配、菜单权限树形管理、岗位操作权限控制、数据字典配置管理、微信生态广告管理，实现完整的企业级权限管理和系统配置体系

**文档**:
- [系统权限管理模块ER图](系统权限管理模块ER图.md)
- [系统权限管理模块UML类图](系统权限管理模块UML类图.md)

## 📈 系统架构特点

### 多层级管理体系
- **🏢 集团级**: 制定标准和规范，统一配置管理
- **🏬 分公司级**: 区域管理和协调，承上启下
- **🏗️ 项目级**: 具体执行和操作，业务落地

### 数据架构设计原则
1. **🌳 层级结构**: 支持树形数据组织，灵活的组织架构
2. **🏢 多租户**: 项目间数据隔离(基于UnitManageID)，数据安全
3. **📐 标准化**: 集团级配置可下沉到项目，统一标准
4. **🔐 权限控制**: 基于组织架构的权限体系，精细化权限
5. **🔄 工作流集成**: 支持复杂业务审批流程，灵活配置
6. **📊 实时监控**: 设备状态、消防系统实时监控，预警机制

## 🚀 快速开始

### 环境要求
- **开发工具**: Visual Studio 2019 或更高版本
- **数据库**: SQL Server 2012 或更高版本
- **框架**: .NET Framework 4.8
- **ORM**: NHibernate

### 部署步骤
1. **克隆项目**: 获取 PropertySys_ZGHQ 项目源码
2. **创建数据库**: 在 SQL Server 中创建 `PropertySys_ZG` 数据库
3. **配置连接**: 修改 `web.config` 中的数据库连接字符串
4. **设置启动**: 将 `PropertySys_ZGHQ.Web` 设为启动项目
5. **运行项目**: F5 启动调试或发布到 IIS

### 主要特色功能
- **⚡ 实时监控告警**: 消防系统、设备状态实时监控
- **📱 移动办公**: 微信生态集成，支持移动端操作
- **💳 微信支付集成**: 支持微信支付，便民缴费
- **🔄 工作流管理**: 可视化工作流配置，灵活的审批流程
- **🏢 多租户架构**: 支持集团化管理，数据隔离安全

## 📞 技术支持

如有任何技术问题或建议，请通过以下方式联系：

- **技术文档**: 参考各模块的 ER图 和 UML类图 文档
- **数据库设计**: 所有表结构和关系都在对应的 ER图 文档中详细说明
- **业务逻辑**: UML类图 文档包含完整的业务类设计

---

**文档维护**: 该文档会随着系统版本更新而持续维护，请以最新版本为准。 