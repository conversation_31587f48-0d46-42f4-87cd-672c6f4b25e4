# 采购管理模块UML类图

## 类结构说明

### 1. 核心业务类

#### WLItemApplyService (采购申请服务类)
- **职责**：处理采购申请单的业务逻辑
- **主要方法**：
  - `CreateApply()`: 创建采购申请
  - `SubmitApply()`: 提交申请审核
  - `ApproveApply()`: 审核采购申请
  - `RejectApply()`: 拒绝采购申请
  - `GetApplyList()`: 查询申请单列表
  - `GetApplyDetails()`: 获取申请详情

#### WLItemService (物料管理服务类)
- **职责**：管理物料信息和分类
- **主要方法**：
  - `CreateItem()`: 创建物料
  - `UpdateItem()`: 更新物料信息
  - `GetItemsByClass()`: 按分类查询物料
  - `SearchItems()`: 物料搜索

#### SupplierService (供应商服务类)
- **职责**：供应商信息管理
- **主要方法**：
  - `RegisterSupplier()`: 供应商注册
  - `UpdateSupplier()`: 更新供应商信息
  - `GetQualifiedSuppliers()`: 获取合格供应商
  - `EvaluateSupplier()`: 供应商评估

### 2. 实体类

#### WLItemApply (采购申请单实体)
- **属性**：申请编号、申请类型、申请人、申请日期、状态等
- **关系**：一对多关联申请明细

#### WLItemApplyDetail (采购申请明细实体)
- **属性**：物料编号、数量、单价、总价、备注等
- **关系**：多对一关联申请单，多对一关联物料

#### WLItem (物料实体)
- **属性**：物料编号、名称、规格型号、单位、分类等
- **关系**：多对一关联物料分类

#### WLItemClass (物料分类实体)
- **属性**：分类编号、分类名称、父级分类、层级等
- **关系**：树形结构，一对多关联物料

#### Supplier (供应商实体)
- **属性**：供应商编号、名称、联系方式、资质等级等
- **关系**：一对多关联采购记录

### 3. 工具类

#### ApprovalWorkflow (审批工作流类)
- **职责**：处理采购审批流程
- **主要方法**：
  - `StartWorkflow()`: 启动审批流程
  - `ProcessApproval()`: 处理审批节点
  - `CompleteWorkflow()`: 完成审批流程

#### PurchaseCalculator (采购计算器类)
- **职责**：采购金额和统计计算
- **主要方法**：
  - `CalculateTotal()`: 计算总金额
  - `CalculateTax()`: 计算税金
  - `GenerateStatistics()`: 生成统计报表

## UML类图

```mermaid
classDiagram
    %% 服务层类
    class WLItemApplyService {
        +CreateApply(apply: WLItemApply) : Result
        +SubmitApply(applyId: string) : Result
        +ApproveApply(applyId: string, approver: string) : Result
        +RejectApply(applyId: string, reason: string) : Result
        +GetApplyList(searchCondition: SearchCondition) : List~WLItemApply~
        +GetApplyDetails(applyId: string) : WLItemApply
        +UpdateApplyStatus(applyId: string, status: ApplyStatus) : Result
    }
    
    class WLItemService {
        +CreateItem(item: WLItem) : Result
        +UpdateItem(item: WLItem) : Result
        +DeleteItem(itemId: string) : Result
        +GetItemsByClass(classId: string) : List~WLItem~
        +SearchItems(keyword: string) : List~WLItem~
        +GetItemDetails(itemId: string) : WLItem
        +CheckItemStock(itemId: string) : StockInfo
    }
    
    class SupplierService {
        +RegisterSupplier(supplier: Supplier) : Result
        +UpdateSupplier(supplier: Supplier) : Result
        +GetQualifiedSuppliers(itemClass: string) : List~Supplier~
        +EvaluateSupplier(supplierId: string, evaluation: Evaluation) : Result
        +GetSupplierHistory(supplierId: string) : List~PurchaseRecord~
    }
    
    %% 实体类
    class WLItemApply {
        +Id: string
        +ApplyCode: string
        +ApplyType: ApplyType
        +ApplicantId: string
        +ApplicantName: string
        +ApplyDate: DateTime
        +RequiredDate: DateTime
        +Status: ApplyStatus
        +TotalAmount: decimal
        +Reason: string
        +UnitId: string
        +DepartmentId: string
        +ApprovalNote: string
        +CreateTime: DateTime
        +UpdateTime: DateTime
        +Details: List~WLItemApplyDetail~
    }
    
    class WLItemApplyDetail {
        +Id: string
        +ApplyId: string
        +ItemId: string
        +ItemCode: string
        +ItemName: string
        +Specification: string
        +Unit: string
        +Quantity: decimal
        +EstimatedPrice: decimal
        +TotalPrice: decimal
        +Urgency: UrgencyLevel
        +Note: string
        +SupplierId: string
        +CreateTime: DateTime
    }
    
    class WLItem {
        +Id: string
        +ItemCode: string
        +ItemName: string
        +Specification: string
        +Model: string
        +Unit: string
        +ClassId: string
        +Brand: string
        +Standard: string
        +SafetyStock: decimal
        +CurrentStock: decimal
        +UnitPrice: decimal
        +Status: ItemStatus
        +CreateTime: DateTime
        +UpdateTime: DateTime
    }
    
    class WLItemClass {
        +Id: string
        +ClassCode: string
        +ClassName: string
        +ParentId: string
        +Level: int
        +Path: string
        +IsLeaf: boolean
        +SortOrder: int
        +Status: ClassStatus
        +CreateTime: DateTime
        +Children: List~WLItemClass~
        +Items: List~WLItem~
    }
    
    class Supplier {
        +Id: string
        +SupplierCode: string
        +SupplierName: string
        +ContactPerson: string
        +Phone: string
        +Email: string
        +Address: string
        +BusinessLicense: string
        +QualificationLevel: QualificationLevel
        +CreditRating: CreditRating
        +Status: SupplierStatus
        +RegisterDate: DateTime
        +LastEvaluationDate: DateTime
        +EvaluationScore: decimal
    }
    
    %% 工具类
    class ApprovalWorkflow {
        +StartWorkflow(applyId: string, workflowType: WorkflowType) : WorkflowInstance
        +ProcessApproval(instanceId: string, approverId: string, decision: ApprovalDecision) : Result
        +CompleteWorkflow(instanceId: string) : Result
        +GetWorkflowStatus(instanceId: string) : WorkflowStatus
        +GetApprovalHistory(applyId: string) : List~ApprovalRecord~
    }
    
    class PurchaseCalculator {
        +CalculateTotal(details: List~WLItemApplyDetail~) : decimal
        +CalculateTax(amount: decimal, taxRate: decimal) : decimal
        +GenerateStatistics(startDate: DateTime, endDate: DateTime) : StatisticsReport
        +CalculateSupplierPerformance(supplierId: string) : PerformanceMetrics
    }
    
    %% 枚举类
    class ApplyType {
        <<enumeration>>
        +SelfPurchase
        +CompanyPurchase
        +SupplierSupply
    }
    
    class ApplyStatus {
        <<enumeration>>
        +Draft
        +Submitted
        +Approved
        +Rejected
        +Completed
        +Cancelled
    }
    
    class UrgencyLevel {
        <<enumeration>>
        +Low
        +Normal
        +High
        +Emergency
    }
    
    %% 关系定义
    WLItemApply ||--o{ WLItemApplyDetail : "包含明细"
    WLItemApplyDetail }o--|| WLItem : "关联物料"
    WLItem }o--|| WLItemClass : "属于分类"
    WLItemClass ||--o{ WLItemClass : "父子分类"
    WLItemApplyDetail }o--|| Supplier : "指定供应商"
    
    WLItemApplyService ..> WLItemApply : "操作"
    WLItemApplyService ..> ApprovalWorkflow : "使用"
    WLItemApplyService ..> PurchaseCalculator : "使用"
    WLItemService ..> WLItem : "操作"
    WLItemService ..> WLItemClass : "操作"
    SupplierService ..> Supplier : "操作"
    
    WLItemApply ..> ApplyType : "使用"
    WLItemApply ..> ApplyStatus : "使用"
    WLItemApplyDetail ..> UrgencyLevel : "使用"
```

## 业务逻辑设计

### 1. 采购申请流程

```mermaid
sequenceDiagram
    participant User as 申请人
    participant ApplyService as WLItemApplyService
    participant WorkflowService as ApprovalWorkflow
    participant ItemService as WLItemService
    participant DB as 数据库
    
    User->>ApplyService: 创建采购申请
    ApplyService->>ItemService: 验证物料信息
    ItemService-->>ApplyService: 返回物料详情
    ApplyService->>DB: 保存申请单
    ApplyService->>User: 返回申请单号
    
    User->>ApplyService: 提交审核
    ApplyService->>WorkflowService: 启动审批流程
    WorkflowService->>DB: 创建工作流实例
    WorkflowService-->>ApplyService: 返回流程ID
    ApplyService->>DB: 更新申请状态
    
    loop 审批环节
        WorkflowService->>WorkflowService: 通知审批人
        WorkflowService->>ApplyService: 处理审批结果
        ApplyService->>DB: 更新审批状态
    end
    
    WorkflowService-->>User: 审批完成通知
```

### 2. 物料管理流程

```mermaid
flowchart TD
    A[物料分类管理] --> B[创建物料分类]
    A --> C[维护分类树结构]
    
    D[物料信息管理] --> E[新增物料]
    D --> F[更新物料信息]
    D --> G[物料状态管理]
    
    B --> H[物料录入]
    E --> H
    H --> I[物料验证]
    I --> J{验证通过?}
    J -->|是| K[保存物料]
    J -->|否| L[返回错误信息]
    
    F --> I
    G --> M[状态变更记录]
    K --> N[更新库存信息]
    M --> N
```

### 3. 供应商评估体系

```mermaid
graph TB
    A[供应商注册] --> B[资质审核]
    B --> C{审核通过?}
    C -->|是| D[激活供应商]
    C -->|否| E[拒绝注册]
    
    D --> F[供应商评估]
    F --> G[质量评估]
    F --> H[交期评估]
    F --> I[服务评估]
    F --> J[价格评估]
    
    G --> K[综合评分]
    H --> K
    I --> K
    J --> K
    
    K --> L{评分等级}
    L -->|优秀| M[A级供应商]
    L -->|良好| N[B级供应商]
    L -->|一般| O[C级供应商]
    L -->|较差| P[限制合作]
```

## 设计模式应用

### 1. 策略模式 - 采购类型处理
```java
interface PurchaseStrategy {
    Result processPurchase(WLItemApply apply);
}

class SelfPurchaseStrategy implements PurchaseStrategy {
    // 自行采购处理逻辑
}

class CompanyPurchaseStrategy implements PurchaseStrategy {
    // 公司采购处理逻辑
}

class SupplierSupplyStrategy implements PurchaseStrategy {
    // 供应商供货处理逻辑
}
```

### 2. 状态模式 - 申请单状态管理
```java
abstract class ApplyState {
    abstract Result submit(WLItemApply apply);
    abstract Result approve(WLItemApply apply);
    abstract Result reject(WLItemApply apply);
}

class DraftState extends ApplyState {
    // 草稿状态处理
}

class SubmittedState extends ApplyState {
    // 已提交状态处理
}

class ApprovedState extends ApplyState {
    // 已审核状态处理
}
```

### 3. 观察者模式 - 审批状态通知
```java
interface ApprovalObserver {
    void onStatusChanged(String applyId, ApplyStatus newStatus);
}

class EmailNotificationObserver implements ApprovalObserver {
    // 邮件通知实现
}

class SMSNotificationObserver implements ApprovalObserver {
    // 短信通知实现
}

class SystemNotificationObserver implements ApprovalObserver {
    // 系统内通知实现
}
```

## 扩展性设计

### 1. 插件化采购流程
- 支持自定义审批节点
- 可配置审批规则
- 支持外部系统集成

### 2. 多样化评估维度
- 可扩展的供应商评估指标
- 灵活的评分权重配置
- 历史数据对比分析

### 3. 智能推荐功能
- 基于历史数据的物料推荐
- 供应商智能匹配
- 价格预警和优化建议

## 关键算法设计

### 1. 采购成本优化算法
```
输入：采购需求列表、供应商报价、约束条件
输出：最优采购方案
算法：基于约束的多目标优化
```

### 2. 供应商评分算法
```
综合评分 = Σ(评估维度i × 权重i × 时间衰减因子i)
评估维度：质量、交期、服务、价格、合作历史
```

### 3. 库存预警算法
```
预警阈值 = 安全库存 + 预期消耗量 × 采购周期 × 安全系数
考虑因素：历史消耗、季节性变化、采购难度
``` 