# 系统角色类型分析

## 概述

本系统定义了多种角色类型，用于控制不同用户的访问权限和功能范围。这些角色类型在 `PropertySys_ZGHQ.Entity/Enums/EnumCommon.cs` 文件中的 `UserRoleType` 枚举中定义。

```csharp
public enum UserRoleType
{
    集团角色 = 0,
    公司角色 = 1,
    项目角色 = 2,
    宿管角色 = 3,
    管理角色 = 99
}
```

## 角色类型详解

### 1. 集团角色 (Group Role)

- **值**: `0`
- **目的**: 此角色拥有最高级别的权限，可以访问和管理系统中所有集团、公司和项目的数据。通常分配给集团总部的核心管理人员。

### 2. 公司角色 (Company Role)

- **值**: `1`
- **目的**: 此角色用于管理一个或多个特定的分公司。他们可以查看和管理其所管辖分公司下的所有项目数据，但不能访问其他分公司或集团级别的数据。

### 3. 项目角色 (Project Role)

- **值**: `2`
- **目的**: 这是最基础的业务角色，其权限被限制在单个项目内。项目角色的用户只能访问和操作其所在项目的数据，例如查看本项目的设备、报修单等。

### 4. 宿管角色 (Dormitory Management Role)

- **值**: `3`
- **目的**: 这是一个特殊的业务角色，专门用于管理宿舍相关的业务。从代码中可以看出，此角色主要用于“ModuleSpareManage”模块，可能拥有管理宿舍、床位、学生等权限。

### 5. 管理角色 (Admin Role)

- **值**: `99`
- **目的**: 这是一个特殊的管理角色，拥有系统级别的管理权限。从代码中可以看出，此角色通常与“集团角色”一起出现，拥有跨公司、跨项目的管理能力。与“集团角色”相比，“管理角色”可能更侧重于系统的技术性管理，例如系统配置、用户管理等。

## 权限控制逻辑

系统中的权限控制主要通过检查当前用户的 `UnitLevel` (单位级别) 来实现。`UnitLevel` 与 `UserRoleType` 枚举值相对应。例如，在一个控制器中，可能会有类似以下的逻辑：

```csharp
if (UTBaseUnitManageEnt.UnitLevel == (int)UserRoleType.公司角色)
{
    // 公司角色的用户可以看到的特定数据
}
else if (UTBaseUnitManageEnt.UnitLevel == (int)UserRoleType.项目角色)
{
    // 项目角色的用户可以看到的特定数据
}
```

这种方式可以根据用户的角色类型，在后端动态地过滤数据和控制功能访问，从而实现分层级的权限管理。
