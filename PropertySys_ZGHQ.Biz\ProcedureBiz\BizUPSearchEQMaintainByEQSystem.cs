	using System.Collections.Generic; 
	using System.Text; 
	using System;
	using System.ServiceModel;
	using System.ServiceModel.Web;
	using NHibernate;
	using NHibernate.Linq;
	using Project.Common;
	using Project.Dal.Base;
	using Project.Biz.Base;
	using PropertySys_ZGHQ.ProcedureEntity;
	using PropertySys_ZGHQ.ProcedureDal;

	namespace PropertySys_ZGHQ.ProcedureBiz
	{
		public class  BizUPSearchEQMaintainByEQSystem : BaseSPBiz
		{
			private DalUPSearchEQMaintainByEQSystem dalUPSearchEQMaintainByEQSystem;
		
			private  BizUPSearchEQMaintainByEQSystem()
			{
				dalUPSearchEQMaintainByEQSystem = DalFactory.Get<DalUPSearchEQMaintainByEQSystem>();
			}

			[OperationContract]
			[WebInvoke(BodyStyle = WebMessageBodyStyle.Wrapped)]
			public IList<UPSearchEQMaintainByEQSystem> Invoke(UPSearchEQMaintainByEQSystemParameter parameter)
			{
									var result = dalUPSearchEQMaintainByEQSystem.Invoke(parameter);
					return result;
							}
		}
	}

	