# 考勤管理模块UML类图设计

## 模块概述

考勤管理模块采用标准的四层架构设计（Entity→Dal→Biz→Web），实现了完整的考勤管理业务功能。模块支持考勤打卡、班次管理、排班管理、请假审批、加班管理、补卡申请等核心功能，并集成工作流引擎和钉钉生态，提供智能化的考勤管理解决方案。

## 系统架构设计

### 架构层次
1. **Entity层**: 数据实体定义和ORM映射
2. **Dal层**: 数据访问层，封装数据库操作
3. **Biz层**: 业务逻辑层，实现核心业务规则
4. **Web层**: 表现层，提供用户界面和API接口

### 设计模式应用
- **Repository模式**: 数据访问抽象
- **Service模式**: 业务逻辑封装
- **Workflow模式**: 审批流程管理
- **Observer模式**: 事件驱动通知
- **Strategy模式**: 考勤规则策略化

## 四层架构UML类图

\`\`\`mermaid
classDiagram
    %% Entity层 - 数据实体
    class UTBaseAttPoint {
        +Guid ID
        +String AttPoint
        +String AttAddress
        +Decimal Lon
        +Decimal Lat
        +Int32 Distance
        +String Remark
        +DateTime CreateDate
        +Int64 CreateUser
    }

    class UTUnitAttRecord {
        +Guid ID
        +Int64 UserID
        +Guid FaceUserID
        +DateTime AttDate
        +String TheDay
        +DateTime FirstRecordTime
        +DateTime LastRecordTime
        +Int32 Status
        +DateTime CreateDate
    }

    class UTUnitShiftsType {
        +Guid ID
        +Guid UnitID
        +String ShiftName
        +String ShortName
        +String StartTimeStr
        +String EndTimeStr
        +Decimal WordTime1
        +Int32 IsCrossDay
        +String ClassColor
        +Int32 ShiftsType
    }

    class UTUnitSchedualShifts {
        +Guid ID
        +Guid UnitID
        +Guid ShiftTypeID
        +String ShiftName
        +Int64 UserID
        +Int64 OrgID
        +DateTime ShiftDate
        +Int32 IsRest
        +Int32 Isdynamic
    }

    class UTUnitLeave {
        +Guid ID
        +Guid UnitID
        +String LeaveNo
        +DateTime LeaveStart
        +DateTime LeaveEnd
        +Int32 LeaveType
        +Decimal LeaveTime
        +String LeaveDesc
        +Int32 Status
        +Int64 CurrentApprovalUser
    }

    class UTUnitOvertime {
        +Guid ID
        +Guid UnitID
        +String OverTimeNo
        +DateTime OvertimeStart
        +DateTime OvertimeEnd
        +Int32 OvertimeReason
        +Int32 OvertimeType
        +Decimal OvertimeTime
        +Int32 Status
    }

    %% Dal层 - 数据访问
    class Repository~T~ {
        <<abstract>>
        +IQueryable~T~ GetAll()
        +T GetById(object id)
        +void Insert(T entity)
        +void Update(T entity)
        +void Delete(T entity)
        +IQueryable~T~ Where(Expression expression)
    }

    class DalUTBaseAttPoint {
        +IQueryable~UTBaseAttPoint~ GetAll()
        +UTBaseAttPoint GetById(Guid id)
        +void Insert(UTBaseAttPoint entity)
        +UTBaseAttPoint GetByName(string name)
        +List~UTBaseAttPoint~ GetByDistance(decimal lon, decimal lat)
    }

    class DalUTUnitAttRecord {
        +IQueryable~UTUnitAttRecord~ GetAll()
        +UTUnitAttRecord GetById(Guid id)
        +List~UTUnitAttRecord~ GetByUserAndDate(Int64 userId, DateTime date)
        +List~UTUnitAttRecord~ GetByDateRange(DateTime start, DateTime end)
        +void UpdateStatus(Guid id, Int32 status)
    }

    class DalUTUnitShiftsType {
        +IQueryable~UTUnitShiftsType~ GetAll()
        +UTUnitShiftsType GetById(Guid id)
        +List~UTUnitShiftsType~ GetByUnit(Guid unitId)
        +UTUnitShiftsType GetByName(string shiftName)
    }

    class DalUTUnitSchedualShifts {
        +IQueryable~UTUnitSchedualShifts~ GetAll()
        +List~UTUnitSchedualShifts~ GetByUserAndDate(Int64 userId, DateTime date)
        +List~UTUnitSchedualShifts~ GetByDateRange(DateTime start, DateTime end)
        +void BatchInsert(List~UTUnitSchedualShifts~ shifts)
    }

    class DalUTUnitLeave {
        +IQueryable~UTUnitLeave~ GetAll()
        +UTUnitLeave GetById(Guid id)
        +List~UTUnitLeave~ GetByApprovalUser(Int64 userId)
        +List~UTUnitLeave~ GetByStatus(Int32 status)
        +void UpdateApprovalStatus(Guid id, Int32 status)
    }

    class DalUTUnitOvertime {
        +IQueryable~UTUnitOvertime~ GetAll()
        +UTUnitOvertime GetById(Guid id)
        +List~UTUnitOvertime~ GetByApprovalUser(Int64 userId)
        +Decimal GetTotalOvertimeHours(Int64 userId, DateTime start, DateTime end)
    }

    %% Biz层 - 业务逻辑
    class BaseBiz~TEntity, TDal~ {
        <<abstract>>
        +TDal Dal
        +IQueryable~TEntity~ GetAll()
        +TEntity GetById(object id)
        +bool Insert(TEntity entity)
        +bool Update(TEntity entity)
        +bool Delete(object id)
    }

    class BizUTBaseAttPoint {
        +bool CreateAttPoint(UTBaseAttPoint point)
        +List~UTBaseAttPoint~ GetNearbyPoints(decimal lon, decimal lat, int distance)
        +bool ValidateLocation(decimal lon, decimal lat, int distance)
        +bool UpdateAttPointInfo(Guid id, string name, string address)
    }

    class BizUTUnitAttRecord {
        +bool CreateAttRecord(UTUnitAttRecord record)
        +AttendanceResult ProcessAttendance(Int64 userId, DateTime checkTime, Guid pointId)
        +List~UTUnitAttRecord~ GetUserAttendance(Int64 userId, DateTime start, DateTime end)
        +AttendanceStatistics GetAttendanceStatistics(Int64 userId, DateTime month)
        +bool UpdateAttendanceStatus(Guid id, Int32 status, string reason)
    }

    class BizUTUnitShiftsType {
        +bool CreateShiftType(UTUnitShiftsType shiftType)
        +List~UTUnitShiftsType~ GetUnitShiftTypes(Guid unitId)
        +bool ValidateShiftTime(string startTime, string endTime)
        +TimeSpan CalculateWorkDuration(UTUnitShiftsType shiftType)
        +bool UpdateShiftConfiguration(Guid id, UTUnitShiftsType updates)
    }

    class BizUTUnitSchedualShifts {
        +bool CreateSchedule(UTUnitSchedualShifts schedule)
        +List~UTUnitSchedualShifts~ GetUserSchedule(Int64 userId, DateTime start, DateTime end)
        +bool BatchCreateSchedule(List~UTUnitSchedualShifts~ schedules)
        +ScheduleConflictResult ValidateScheduleConflict(UTUnitSchedualShifts schedule)
        +bool UpdateSchedule(Guid id, UTUnitSchedualShifts updates)
    }

    class BizUTUnitLeave {
        +bool SubmitLeaveApplication(UTUnitLeave leave)
        +WorkflowResult ProcessLeaveApproval(Guid leaveId, Int64 approverId, Int32 result, string comment)
        +List~UTUnitLeave~ GetPendingApprovals(Int64 approverId)
        +LeaveStatistics GetUserLeaveStatistics(Int64 userId, int year)
        +bool CancelLeaveApplication(Guid leaveId, string reason)
        +bool HRConfirmLeave(Guid leaveId, UTUnitLeave confirmData)
    }

    class BizUTUnitOvertime {
        +bool SubmitOvertimeApplication(UTUnitOvertime overtime)
        +WorkflowResult ProcessOvertimeApproval(Guid overtimeId, Int64 approverId, Int32 result, string comment)
        +List~UTUnitOvertime~ GetPendingApprovals(Int64 approverId)
        +OvertimeStatistics GetUserOvertimeStatistics(Int64 userId, DateTime start, DateTime end)
        +Decimal CalculateOvertimePay(UTUnitOvertime overtime)
        +bool HRConfirmOvertime(Guid overtimeId, UTUnitOvertime confirmData)
    }

    %% Web层 - 控制器
    class BaseController {
        <<abstract>>
        +ActionResult Index()
        +ActionResult Create()
        +ActionResult Edit(object id)
        +ActionResult Delete(object id)
        +JsonResult GetJson()
    }

    class AttPointController {
        +ActionResult Index()
        +ActionResult Create(UTBaseAttPoint model)
        +ActionResult Edit(Guid id, UTBaseAttPoint model)
        +JsonResult GetNearbyPoints(decimal lon, decimal lat)
        +ActionResult Delete(Guid id)
    }

    class AttendanceRecordController {
        +ActionResult Index()
        +JsonResult ProcessAttendance(AttendanceModel model)
        +ActionResult AttendanceReport()
        +JsonResult GetUserAttendance(Int64 userId, DateTime start, DateTime end)
        +ActionResult ExportAttendance()
    }

    class ShiftsSetController {
        +ActionResult Index()
        +ActionResult Create(UTUnitShiftsType model)
        +ActionResult Edit(Guid id, UTUnitShiftsType model)
        +JsonResult ValidateShiftTime(string startTime, string endTime)
        +ActionResult Copy(Guid id)
    }

    class LeaveApplyController {
        +ActionResult Index()
        +ActionResult Create(UTUnitLeave model)
        +ActionResult Approval()
        +JsonResult ProcessApproval(ApprovalModel model)
        +ActionResult LeaveReport()
        +ActionResult HRConfirm()
    }

    class OverTimeController {
        +ActionResult Index()
        +ActionResult Create(UTUnitOvertime model)
        +ActionResult Approval()
        +JsonResult ProcessApproval(ApprovalModel model)
        +ActionResult OvertimeReport()
        +ActionResult HRConfirm()
    }

    %% 业务服务类
    class AttendanceService {
        +ProcessResult ProcessDailyAttendance(Int64 userId, DateTime date)
        +AttendanceReport GenerateMonthlyReport(Int64 userId, DateTime month)
        +List~AttendanceAnomaly~ DetectAnomalies(DateTime start, DateTime end)
        +bool SyncDingDingAttendance()
    }

    class WorkflowService {
        +WorkflowInstance StartWorkflow(string workflowType, Guid businessId)
        +WorkflowResult ProcessApproval(Guid workflowId, ApprovalDecision decision)
        +List~WorkflowTask~ GetPendingTasks(Int64 userId)
        +WorkflowHistory GetWorkflowHistory(Guid workflowId)
    }

    class ScheduleService {
        +ScheduleResult GenerateMonthlySchedule(Int64 userId, DateTime month)
        +List~ScheduleConflict~ ValidateScheduleConflicts(List~UTUnitSchedualShifts~ schedules)
        +bool BatchUpdateSchedule(List~UTUnitSchedualShifts~ schedules)
        +ScheduleStatistics GetScheduleStatistics(Guid unitId, DateTime month)
    }

    %% 数据传输对象
    class AttendanceModel {
        +Int64 UserID
        +DateTime CheckTime
        +Guid AttPointID
        +Decimal Longitude
        +Decimal Latitude
        +String DeviceInfo
    }

    class ApprovalModel {
        +Guid BusinessID
        +Int32 ApprovalResult
        +String ApprovalComment
        +Int64 ApproverID
        +DateTime ApprovalTime
    }

    class AttendanceStatistics {
        +Int32 WorkDays
        +Int32 ActualWorkDays
        +Int32 LateDays
        +Int32 EarlyLeaveDays
        +Decimal TotalWorkHours
        +Decimal OvertimeHours
    }

    %% 继承关系
    DalUTBaseAttPoint --|> Repository : extends
    DalUTUnitAttRecord --|> Repository : extends
    DalUTUnitShiftsType --|> Repository : extends
    DalUTUnitSchedualShifts --|> Repository : extends
    DalUTUnitLeave --|> Repository : extends
    DalUTUnitOvertime --|> Repository : extends

    BizUTBaseAttPoint --|> BaseBiz : extends
    BizUTUnitAttRecord --|> BaseBiz : extends
    BizUTUnitShiftsType --|> BaseBiz : extends
    BizUTUnitSchedualShifts --|> BaseBiz : extends
    BizUTUnitLeave --|> BaseBiz : extends
    BizUTUnitOvertime --|> BaseBiz : extends

    AttPointController --|> BaseController : extends
    AttendanceRecordController --|> BaseController : extends
    ShiftsSetController --|> BaseController : extends
    LeaveApplyController --|> BaseController : extends
    OverTimeController --|> BaseController : extends

    %% 依赖关系
    BizUTBaseAttPoint --> DalUTBaseAttPoint : uses
    BizUTUnitAttRecord --> DalUTUnitAttRecord : uses
    BizUTUnitShiftsType --> DalUTUnitShiftsType : uses
    BizUTUnitSchedualShifts --> DalUTUnitSchedualShifts : uses
    BizUTUnitLeave --> DalUTUnitLeave : uses
    BizUTUnitOvertime --> DalUTUnitOvertime : uses

    AttPointController --> BizUTBaseAttPoint : uses
    AttendanceRecordController --> BizUTUnitAttRecord : uses
    ShiftsSetController --> BizUTUnitShiftsType : uses
    LeaveApplyController --> BizUTUnitLeave : uses
    OverTimeController --> BizUTUnitOvertime : uses

    AttendanceRecordController --> AttendanceService : uses
    LeaveApplyController --> WorkflowService : uses
    OverTimeController --> WorkflowService : uses
    ShiftsSetController --> ScheduleService : uses
\`\`\`

## 业务流程类图

\`\`\`mermaid
classDiagram
    %% 考勤打卡流程
    class AttendanceProcess {
        +startAttendanceCheck(userId, location)
        +validateLocation(location, attPoint)
        +recordAttendance(userId, checkTime, pointId)
        +calculateAttendanceStatus(userId, checkTime, schedule)
        +notifyAttendanceResult(userId, result)
    }

    %% 请假申请流程
    class LeaveApplicationProcess {
        +submitLeaveApplication(leaveData)
        +validateLeaveQuota(userId, leaveType, days)
        +startWorkflowApproval(leaveId)
        +processApprovalStep(approvalData)
        +hrConfirmLeave(leaveId, confirmData)
        +updateAttendanceRecord(leaveId)
    }

    %% 加班申请流程
    class OvertimeApplicationProcess {
        +submitOvertimeApplication(overtimeData)
        +validateOvertimeRules(overtimeData)
        +startWorkflowApproval(overtimeId)
        +processApprovalStep(approvalData)
        +calculateOvertimePay(overtimeId)
        +hrConfirmOvertime(overtimeId, confirmData)
    }

    %% 排班管理流程
    class ScheduleManagementProcess {
        +createShiftType(shiftData)
        +generateMonthlySchedule(unitId, month)
        +assignUserToSchedule(userId, scheduleId)
        +validateScheduleConflict(scheduleData)
        +publishSchedule(scheduleId)
        +adjustSchedule(scheduleId, adjustData)
    }

    %% 工作流引擎
    class WorkflowEngine {
        +startWorkflow(workflowType, businessId)
        +getNextApprover(workflowId, currentStep)
        +processApprovalDecision(workflowId, decision)
        +completeWorkflow(workflowId)
        +terminateWorkflow(workflowId, reason)
    }

    %% 通知服务
    class NotificationService {
        +sendApprovalNotification(userId, workflowId)
        +sendAttendanceReminder(userId)
        +sendScheduleNotification(userId, scheduleId)
        +sendAnomalyAlert(managerId, anomalies)
    }

    %% 流程关系
    AttendanceProcess --> AttendanceService : uses
    LeaveApplicationProcess --> WorkflowEngine : uses
    OvertimeApplicationProcess --> WorkflowEngine : uses
    ScheduleManagementProcess --> ScheduleService : uses
    WorkflowEngine --> NotificationService : uses
\`\`\`

## 设计模式应用详解

### 1. Repository模式
```csharp
// 抽象数据访问接口
public abstract class Repository<T> where T : class
{
    protected ISession Session { get; set; }
    
    public virtual IQueryable<T> GetAll()
    public virtual T GetById(object id)
    public virtual void Insert(T entity)
    public virtual void Update(T entity)
    public virtual void Delete(T entity)
    public virtual IQueryable<T> Where(Expression<Func<T, bool>> expression)
}

// 具体实现
public class DalUTUnitAttRecord : Repository<UTUnitAttRecord>
{
    public List<UTUnitAttRecord> GetByUserAndDate(Int64 userId, DateTime date)
    {
        return Where(x => x.UserID == userId && x.AttDate.Date == date.Date).ToList();
    }
}
```

### 2. Service模式
```csharp
// 业务服务封装
public class AttendanceService
{
    private readonly BizUTUnitAttRecord _attRecordBiz;
    private readonly BizUTUnitSchedualShifts _scheduleBiz;
    
    public ProcessResult ProcessDailyAttendance(Int64 userId, DateTime date)
    {
        // 1. 获取用户排班信息
        var schedule = _scheduleBiz.GetUserSchedule(userId, date, date).FirstOrDefault();
        
        // 2. 获取打卡记录
        var records = _attRecordBiz.GetUserAttendance(userId, date, date);
        
        // 3. 分析考勤状态
        var status = CalculateAttendanceStatus(schedule, records);
        
        // 4. 更新考勤状态
        return UpdateAttendanceStatus(userId, date, status);
    }
}
```

### 3. Workflow模式
```csharp
// 工作流引擎
public class WorkflowEngine
{
    public WorkflowResult ProcessApproval(Guid workflowId, ApprovalDecision decision)
    {
        var workflow = GetWorkflowInstance(workflowId);
        var currentStep = workflow.CurrentStep;
        
        // 处理审批决策
        var result = ProcessStep(currentStep, decision);
        
        if (result.IsApproved)
        {
            // 移动到下一步
            var nextStep = GetNextStep(workflow, currentStep);
            if (nextStep != null)
            {
                workflow.CurrentStep = nextStep;
                NotifyNextApprover(nextStep.ApproverId);
            }
            else
            {
                // 工作流完成
                CompleteWorkflow(workflow);
            }
        }
        
        return result;
    }
}
```

### 4. Observer模式
```csharp
// 考勤事件观察者
public interface IAttendanceObserver
{
    void OnAttendanceRecorded(AttendanceEvent evt);
    void OnAttendanceAnomaly(AttendanceAnomalyEvent evt);
}

public class AttendanceNotificationObserver : IAttendanceObserver
{
    public void OnAttendanceRecorded(AttendanceEvent evt)
    {
        // 发送考勤通知
        if (evt.IsLate)
        {
            NotificationService.SendLateNotification(evt.UserId);
        }
    }
    
    public void OnAttendanceAnomaly(AttendanceAnomalyEvent evt)
    {
        // 发送异常告警
        NotificationService.SendAnomalyAlert(evt.ManagerId, evt.Anomalies);
    }
}
```

### 5. Strategy模式
```csharp
// 考勤规则策略
public interface IAttendanceRule
{
    AttendanceStatus CalculateStatus(UTUnitSchedualShifts schedule, List<UTUnitAttRecord> records);
}

public class StandardWorkdayRule : IAttendanceRule
{
    public AttendanceStatus CalculateStatus(UTUnitSchedualShifts schedule, List<UTUnitAttRecord> records)
    {
        // 标准工作日考勤规则
        var firstRecord = records.OrderBy(x => x.FirstRecordTime).FirstOrDefault();
        var lastRecord = records.OrderByDescending(x => x.LastRecordTime).FirstOrDefault();
        
        // 计算迟到早退状态
        return DetermineStatus(schedule, firstRecord, lastRecord);
    }
}

public class FlexibleWorkRule : IAttendanceRule
{
    public AttendanceStatus CalculateStatus(UTUnitSchedualShifts schedule, List<UTUnitAttRecord> records)
    {
        // 弹性工作时间考勤规则
        // 实现弹性工作时间的考勤状态计算逻辑
    }
}
```

## 核心业务功能详解

### 1. 考勤打卡处理
- **地理位置验证**: 基于GPS坐标和设定距离验证打卡地点
- **时间窗口验证**: 根据排班信息验证打卡时间有效性
- **重复打卡处理**: 防止短时间内重复打卡
- **异常状态标记**: 自动标记迟到、早退等异常状态

### 2. 班次排班管理
- **灵活班次配置**: 支持标准班次、分段班次、跨天班次
- **动态排班调整**: 支持临时调班和班次变更
- **冲突检测**: 自动检测排班冲突和时间重叠
- **批量操作**: 支持批量生成和调整排班

### 3. 请假审批流程
- **多级审批**: 支持部门经理、人事、总经理等多级审批
- **审批流转**: 基于工作流引擎的状态流转
- **额度控制**: 年假、调休假等额度自动扣减
- **人事确认**: 最终人事确认环节确保数据准确性

### 4. 加班工时管理
- **加班类型区分**: 平时加班、周末加班、节假日加班
- **工时倍数计算**: 不同类型加班的工时倍数自动计算
- **调休管理**: 加班工时转换为调休时间
- **薪资对接**: 为薪资系统提供准确的加班工时数据

### 5. 数据同步集成
- **钉钉集成**: 与钉钉考勤系统数据双向同步
- **人脸识别**: 集成人脸识别考勤设备
- **实时推送**: 考勤数据实时推送和处理
- **异常预警**: 异常考勤数据自动预警通知

## 技术架构优势

### 1. 分层解耦
- 清晰的分层架构便于维护和扩展
- 每层职责明确，减少代码耦合
- 支持单元测试和集成测试

### 2. 可扩展性
- 基于接口的设计支持功能扩展
- 插件化的规则引擎支持业务定制
- 标准化的API接口支持第三方集成

### 3. 高性能
- Repository模式优化数据访问
- 缓存机制减少数据库压力
- 异步处理提升系统响应速度

### 4. 可维护性
- 标准化的编码规范
- 完善的日志记录和异常处理
- 清晰的文档和注释 