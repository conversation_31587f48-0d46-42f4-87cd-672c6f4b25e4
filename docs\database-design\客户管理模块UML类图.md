# 客户管理模块UML类图

## 类结构说明

### 实体类层次结构

#### 客户管理核心类
- **UTUnitCustomer**: 客户基本信息管理，包含个人信息、联系方式、客户分类等
- **UTKFCustomerService**: 客户服务管理，处理投诉、咨询等业务
- **UTKFCustomerServiceDetial**: 客户服务处理详情，记录操作轨迹

#### 维修管理类
- **UTEQMaintain**: 维修单主体，管理报修流程和状态
- **UTEQMaintainDetial**: 维修处理详情，记录维修过程

#### 满意度调查类
- **UTUnitSatisfaction**: 满意度调查主表，定义调查基本信息
- **UTUnitSatisfactionDetail**: 调查参与详情，记录客户参与情况
- **UTUnitSatisfactionClass**: 调查分类管理，支持层级结构
- **UTUnitSatisfactionQuestion**: 调查问题定义，支持多种题型

#### 公告管理类
- **UTBaseSysNotice**: 系统通知公告，支持时间控制和附件

### 业务层类结构
- **Biz层**: 业务逻辑处理层，封装核心业务规则
- **Dal层**: 数据访问层，处理数据库操作
- **Model层**: 模型层，定义数据传输对象和验证规则

## UML类图

```mermaid
classDiagram
    class UTUnitCustomer {
        +Guid ID
        +Guid? UnitID
        +String CustomerName
        +Int32? CustomerType
        +Int32? Sex
        +DateTime? Birthday
        +String Email
        +String MobilePhone
        +String Tel
        +Int32? CustomerAttribute
        +String UnitName
        +String OpenID
        +Int32? Status
        +DateTime? UpdateDate
        +validate() Boolean
        +getDisplayName() String
    }

    class UTKFCustomerService {
        +Guid ID
        +String ServiceCode
        +Guid? UnitID
        +Guid? CustomerID
        +Int32? ServiceType
        +String SendUserName
        +String SendUserTel
        +DateTime? SendTime
        +String ServiceInfo
        +Int32? Status
        +DateTime? CreateDate
        +Int64? ManagerUser
        +String DealDesc
        +Int32? DealResult
        +DateTime? DealTime
        +Int64? HFUser
        +DateTime? HFTime
        +Int32? HFLevel
        +Int32? HFResult
        +generateServiceCode() String
        +updateStatus(Int32 status) Void
        +assignManager(Int64 userId) Void
    }

    class UTKFCustomerServiceDetial {
        +Guid ID
        +Guid? ServiceID
        +Int32? OperateType
        +String OperateInfo
        +Int64? OperateUserID
        +DateTime? OperateTime
        +addOperateRecord() Void
        +getOperateHistory() List
    }

    class UTEQMaintain {
        +Guid ID
        +Guid? UnitID
        +Guid? CustomerUserID
        +Guid? CustomerID
        +String MaintainCode
        +Int32? FromType
        +Int32? BaseType
        +Int32? IsUrgent
        +String BadDesc
        +String BadSite
        +String SendUser
        +String SendUserTel
        +DateTime? SendTime
        +DateTime? ExpectTimeS
        +DateTime? ExpectTimeE
        +Int64? MaintainUserID
        +DateTime? MaintainDate
        +Int32? Status
        +DateTime? CreateDate
        +String MaintainDesc
        +Int32? Score
        +generateMaintainCode() String
        +assignMaintainer(Int64 userId) Void
        +updateProgress(Int32 status) Void
        +calculateScore() Int32
    }

    class UTEQMaintainDetial {
        +Guid ID
        +Guid? MaintainID
        +Int32? DetialType
        +String BeginDeal
        +String EndDeal
        +DateTime? DealTime
        +Int64? DealUserID
        +String DealUserName
        +String Remark
        +addProcessRecord() Void
        +getProcessHistory() List
    }

    class UTUnitSatisfaction {
        +Guid ID
        +Guid? UnitID
        +String SatisfactionName
        +DateTime? CreateDate
        +Int64? CreateUser
        +String Attachment
        +Int32? CustomerType
        +String SendName
        +String CustomerJob
        +Int32? IsScore
        +Int32? UseType
        +createSurvey() Boolean
        +publishSurvey() Boolean
        +calculateResults() Object
    }

    class UTUnitSatisfactionDetail {
        +Guid ID
        +Guid? SatisfactionID
        +Guid? CustomerID
        +String CustomerName
        +String SendName
        +String DownloadAttachment
        +String UpAttachment
        +Int32? CustomerType
        +String CustomerJob
        +Int32? IsSend
        +DateTime? CreateDate
        +Int64? CreateUser
        +Int32? IsOK
        +Int32? Score
        +String Telephone
        +String OpenID
        +sendToCustomer() Boolean
        +submitResponse() Boolean
        +calculateScore() Int32
    }

    class UTUnitSatisfactionClass {
        +Guid ID
        +Guid? UnitID
        +Guid? PClassID
        +String ClassName
        +Int32? ClassLevel
        +Int32? IsLast
        +Int32? OrderIndex
        +String Remark
        +DateTime? CreateDate
        +Int64? CreateUser
        +getChildren() List
        +getParent() UTUnitSatisfactionClass
        +buildTree() Object
    }

    class UTUnitSatisfactionQuestion {
        +Guid ID
        +Guid? SatisfactionID
        +Int32? QestionType
        +String Question
        +DateTime? Lastupdate
        +String Remark
        +DateTime? CreateDate
        +Int64? CreateUser
        +Int32? AnswerType
        +validateAnswer(String answer) Boolean
        +getAnswerOptions() List
    }

    class UTBaseSysNotice {
        +Guid ID
        +String NoticeTitle
        +String NoticeContent
        +String NoticeAttachment
        +Int64? CreateUser
        +DateTime? CreateDate
        +DateTime? StartDate
        +DateTime? EndDate
        +Int32? IsDelete
        +isActive() Boolean
        +publish() Boolean
        +archive() Boolean
    }

    class BizUTUnitCustomer {
        -IServiceUTUnitCustomer service
        +Create(UTUnitCustomer entity) Result
        +Update(UTUnitCustomer entity) Result
        +Delete(Guid id) Result
        +GetByID(Guid id) UTUnitCustomer
        +List() List~UTUnitCustomer~
        +Search(SearchEntity search) List~UTUnitCustomer~
        +ValidateCustomer(UTUnitCustomer entity) Boolean
    }

    class BizUTKFCustomerService {
        -IServiceUTKFCustomerService service
        -IServiceUTKFCustomerServiceDetial detailService
        +Create(UTKFCustomerService entity) Result
        +Update(UTKFCustomerService entity) Result
        +ProcessService(Guid id, String operation) Result
        +GetServiceHistory(Guid customerId) List
        +GetPendingServices() List
        +AssignService(Guid serviceId, Int64 userId) Result
    }

    class BizUTEQMaintain {
        -IServiceUTEQMaintain service
        -IServiceUTEQMaintainDetial detailService
        +Create(UTEQMaintain entity) Result
        +Update(UTEQMaintain entity) Result
        +AssignMaintainer(Guid maintainId, Int64 userId) Result
        +UpdateProgress(Guid maintainId, Int32 status) Result
        +GetMaintainHistory(Guid customerId) List
        +GetStatistics(DateTime start, DateTime end) Object
    }

    class BizUTUnitSatisfaction {
        -IServiceUTUnitSatisfaction service
        -IServiceUTUnitSatisfactionDetail detailService
        -IServiceUTUnitSatisfactionQuestion questionService
        +CreateSurvey(UTUnitSatisfaction entity) Result
        +PublishSurvey(Guid surveyId) Result
        +AddParticipant(UTUnitSatisfactionDetail detail) Result
        +SubmitResponse(Guid detailId, Object answers) Result
        +GetSurveyResults(Guid surveyId) Object
        +GetStatistics(DateTime start, DateTime end) Object
    }

    %% 继承关系
    UTUnitCustomer --|> BaseEntity : 继承基础实体
    UTKFCustomerService --|> BaseEntity : 继承基础实体
    UTEQMaintain --|> BaseEntity : 继承基础实体
    UTUnitSatisfaction --|> BaseEntity : 继承基础实体
    UTBaseSysNotice --|> BaseEntity : 继承基础实体

    %% 组合关系
    UTKFCustomerService ||--o{ UTKFCustomerServiceDetial : 包含处理详情
    UTEQMaintain ||--o{ UTEQMaintainDetial : 包含处理详情
    UTUnitSatisfaction ||--o{ UTUnitSatisfactionDetail : 包含调查详情
    UTUnitSatisfaction ||--o{ UTUnitSatisfactionQuestion : 包含调查问题
    UTUnitSatisfactionClass ||--o{ UTUnitSatisfactionClass : 包含子分类

    %% 关联关系
    UTUnitCustomer ||--o{ UTKFCustomerService : 发起服务请求
    UTUnitCustomer ||--o{ UTEQMaintain : 提交报修
    UTUnitCustomer ||--o{ UTUnitSatisfactionDetail : 参与调查

    %% 业务层依赖关系
    BizUTUnitCustomer ..> UTUnitCustomer : 操作
    BizUTKFCustomerService ..> UTKFCustomerService : 操作
    BizUTKFCustomerService ..> UTKFCustomerServiceDetial : 操作
    BizUTEQMaintain ..> UTEQMaintain : 操作
    BizUTEQMaintain ..> UTEQMaintainDetial : 操作
    BizUTUnitSatisfaction ..> UTUnitSatisfaction : 操作
    BizUTUnitSatisfaction ..> UTUnitSatisfactionDetail : 操作
    BizUTUnitSatisfaction ..> UTUnitSatisfactionQuestion : 操作

    class BaseEntity {
        <<abstract>>
        +Guid ID
        +DateTime? CreateDate
        +Int64? CreateUser
        +DateTime? UpdateDate
        +Int64? UpdateUser
        +validate() Boolean
        +toString() String
    }

    class SearchEntity {
        +String _name
        +String _code
        +DateTime? _dateS
        +DateTime? _dateE
        +Int32? _type
        +Int32? _status
        +Guid? _unitId
        +buildCondition() ExpressionWrapper
    }

    class Result {
        +Boolean IsSuccess
        +String Message
        +Object Data
        +success(Object data) Result
        +error(String message) Result
    }
```

## 设计模式说明

### 1. 仓储模式 (Repository Pattern)
- **Dal层**: 实现数据访问仓储，封装数据库操作
- **Biz层**: 实现业务仓储，封装业务逻辑

### 2. 工厂模式 (Factory Pattern)  
- **服务工厂**: 统一创建和管理各种业务服务实例
- **实体工厂**: 负责实体对象的创建和初始化

### 3. 策略模式 (Strategy Pattern)
- **状态处理策略**: 不同业务状态的处理逻辑
- **评分计算策略**: 不同类型的评分算法

### 4. 观察者模式 (Observer Pattern)
- **状态变更通知**: 业务状态变更时的自动通知机制
- **事件处理**: 客户服务、维修等业务事件的处理

### 5. 建造者模式 (Builder Pattern)
- **查询条件构建**: 复杂查询条件的构建
- **报表生成**: 复杂报表的分步构建

## 核心业务流程

### 1. 客户服务流程
1. 客户提交服务请求 → UTKFCustomerService
2. 系统分配处理人员 → ManagerUser字段
3. 处理过程记录 → UTKFCustomerServiceDetial
4. 回访和评价 → HFUser, HFLevel字段

### 2. 报修流程
1. 客户提交报修 → UTEQMaintain
2. 派工处理 → MaintainUserID字段
3. 维修过程跟踪 → UTEQMaintainDetial
4. 完工评价 → Score字段

### 3. 满意度调查流程
1. 创建调查模板 → UTUnitSatisfaction + UTUnitSatisfactionQuestion
2. 发送给客户 → UTUnitSatisfactionDetail
3. 客户填写提交 → UpAttachment字段
4. 结果统计分析 → Score字段 