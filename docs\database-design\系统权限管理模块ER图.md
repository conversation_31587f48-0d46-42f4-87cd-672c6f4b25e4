# 系统权限管理模块ER图

## 表结构说明

### 核心实体表

#### UTSYSUser (用户表)
- **主键**: UserID (Int64)
- **外键**: UnitID (所属单位ID, Guid?), DefaultSystemID (默认子系统ID, Int64?)
- **核心字段**: UserName, Account, Password, UserType, OpenID, DefaultTheme

#### UTSYSRole (角色表)
- **主键**: RoleID (Int64)
- **外键**: UnitID (所属单位ID, Guid?)
- **核心字段**: RoleName, RoleType, SortIndex, IsAdmin

#### UTSYSRight (菜单权限表)
- **主键**: RightID (Int64)
- **外键**: SystemID (子系统ID, Int64), ParentRightID (父级权限ID, Int64)
- **核心字段**: RightName, Type, Level, NavigateURL, IsAdminRgiht, IsDisplay

#### UTSYSRoleRight (角色权限关系表)
- **主键**: RoleRightID (Int64)
- **外键**: RoleID (角色ID, Int64), SystemRightID (权限ID, Int64)
- **核心字段**: 关系映射表

#### UTSYSUserRole (用户角色关系表)
- **主键**: UserRoleID (Int64)
- **外键**: UserID (用户ID, Int64), RoleID (角色ID, Int64)
- **核心字段**: 关系映射表

#### UTSYSSystem (子系统信息表)
- **主键**: SystemID (Int64)
- **外键**: 无
- **核心字段**: SystemName, SystemType, SortIndex, DefaultTheme, ImageURL

#### UTBasePostAuth (岗位操作权限表)
- **主键**: ID (Guid)
- **外键**: 无
- **核心字段**: MenuName, MenuValue, MenuType, CreateDate

#### UTBasePostAuthDetail (岗位权限详情表)
- **主键**: ID (Guid)
- **外键**: PostAuthID (岗位权限ID, Guid?), UserID (用户ID, Int64?)
- **核心字段**: UserType

#### UTBaseCodeGroup (代码组表)
- **主键**: CodeGroupID (Guid)
- **外键**: 无
- **核心字段**: CodeGroupKey, CodeGroupName, SortOrder, Memo

#### UTBaseCode (代码表)
- **主键**: CodeID (Guid)
- **外键**: CodeGroupID (代码组ID, Guid?)
- **核心字段**: CodeGroupCode, PCodeValue, CodeName, CodeValue, SortOrder

#### UTBaseWechatAdvertise (微信广告表)
- **主键**: ID (Guid)
- **外键**: CreateUser (创建用户ID, Int64?)
- **核心字段**: AdvertiseTitle, Attachment, AdvertiseContent, ViewsCount, OrderInt, IsSend

#### UTBaseWechatAdvertiseDetial (微信广告详情表)
- **主键**: ID (Guid)
- **外键**: AdvertiseID (广告ID, Guid?), CreateUser (创建用户ID, Int64?)
- **核心字段**: DetialTitle, DetialContent, OrderInt

#### UTBaseWechatAdvertiseDetialFile (微信广告详情文件表)
- **主键**: ID (Guid)
- **外键**: AdvertiseID (广告ID, Guid?), AdvertiseDetialID (详情ID, Guid?), CreateUser (创建用户ID, Int64?)
- **核心字段**: Attachment, OrderInt

### 关联实体表 (与其他模块共用)

#### UTSYSOrganization (组织机构表) - 在集团管理模块中定义
- **主键**: OrgID (Int64)
- **外键**: OrgParentID (父级部门ID, Int64?), UnitID (所属单位ID, Guid?)
- **核心字段**: OrgName, OrgType, OrgNumber, Level

#### UTBaseUnitManage (项目管理表) - 在集团管理模块中定义
- **主键**: ID (Guid)
- **外键**: PID (父单位ID, Guid?)
- **核心字段**: UnitCode, UnitName, UnitLevel, UnitClass

## ER关系图

```mermaid
erDiagram
    UTSYSUser ||--o{ UTSYSUserRole : "用户关联角色"
    UTSYSRole ||--o{ UTSYSUserRole : "角色包含用户"
    UTSYSRole ||--o{ UTSYSRoleRight : "角色拥有权限"
    UTSYSRight ||--o{ UTSYSRoleRight : "权限分配角色"
    UTSYSSystem ||--o{ UTSYSRight : "系统包含权限"
    UTSYSRight ||--|| UTSYSRight : "权限层级关系"
    UTBaseUnitManage ||--o{ UTSYSUser : "单位包含用户"
    UTBaseUnitManage ||--o{ UTSYSRole : "单位包含角色"
    UTSYSUser ||--|| UTSYSSystem : "用户默认系统"
    
    UTBasePostAuth ||--o{ UTBasePostAuthDetail : "岗位权限包含详情"
    UTSYSUser ||--o{ UTBasePostAuthDetail : "用户拥有岗位权限"
    
    UTBaseCodeGroup ||--o{ UTBaseCode : "代码组包含代码"
    
    UTSYSUser ||--o{ UTBaseWechatAdvertise : "用户创建广告"
    UTBaseWechatAdvertise ||--o{ UTBaseWechatAdvertiseDetial : "广告包含详情"
    UTSYSUser ||--o{ UTBaseWechatAdvertiseDetial : "用户创建详情"
    UTBaseWechatAdvertise ||--o{ UTBaseWechatAdvertiseDetialFile : "广告包含文件"
    UTBaseWechatAdvertiseDetial ||--o{ UTBaseWechatAdvertiseDetialFile : "详情包含文件"
    UTSYSUser ||--o{ UTBaseWechatAdvertiseDetialFile : "用户创建文件"
    
    UTSYSUser {
        int64 UserID PK
        string UserName "用户名称"
        string Account "登录账号"
        string Password "登录密码"
        int UserType "用户类型"
        datetime AddDate "添加日期"
        datetime UpdateDate "更新日期"
        bool IsDeleted "删除标记"
        int DefaultTheme "默认主题"
        int64 DefaultSystemID FK "默认子系统"
        int DefaultLoad "默认加载页面"
        Guid REF_USER_HEADER_FILE "用户头像"
        Guid UnitID FK "所属单位ID"
        string OpenID "微信OpenID"
        int DefaultLoadForm "默认加载平台"
        int FontSize "字体大小"
    }
    
    UTSYSRole {
        int64 RoleID PK
        string RoleName "角色名称"
        int RoleType "角色类型"
        int SortIndex "排序"
        datetime AddDate "添加时间"
        datetime UpdateDate "更新时间"
        bool IsDeleted "删除标记"
        bool IsAdmin "是否管理员"
        Guid UnitID FK "所属单位"
    }
    
    UTSYSRight {
        int64 RightID PK
        int64 SystemID FK "系统ID"
        int64 ParentRightID FK "父级权限ID"
        string RightName "权限名称"
        int Type "权限类型"
        bool IsAdminRgiht "是否管理权限"
        int SortOrder "排序号"
        bool IsDisplay "是否显示菜单"
        bool IsDeleted "删除标记"
        string Target "目标窗口"
        int Level "权限级别"
        int Width "窗口宽度"
        int Height "窗口高度"
        string NavigateURL "导航URL"
    }
    
    UTSYSRoleRight {
        int64 RoleRightID PK
        int64 RoleID FK "角色ID"
        int64 SystemRightID FK "权限ID"
    }
    
    UTSYSUserRole {
        int64 UserRoleID PK
        int64 UserID FK "用户ID"
        int64 RoleID FK "角色ID"
    }
    
    UTSYSSystem {
        int64 SystemID PK
        string SystemName "系统名称"
        int SystemType "系统类型"
        int SortIndex "排序号"
        datetime AddDate "添加时间"
        bool IsDeleted "删除标记"
        string DefaultTheme "默认主题"
        string ImageURL "系统图标"
        int ScreenIndex "桌面分页索引"
    }
    
    UTBasePostAuth {
        Guid ID PK
        string MenuName "菜单名称"
        int MenuValue "菜单值"
        int MenuType "类型"
        datetime CreateDate "创建时间"
    }
    
    UTBasePostAuthDetail {
        Guid ID PK
        Guid PostAuthID FK "岗位权限ID"
        int UserType "用户类型"
        int64 UserID FK "用户ID"
    }
    
    UTBaseCodeGroup {
        Guid CodeGroupID PK
        string CodeGroupKey "编码"
        string CodeGroupName "中文名称"
        int SortOrder "排序顺序号"
        string Memo "说明"
        bool IsDeleted "删除标记"
        string Adder "添加人"
        datetime AddDate "添加时间"
        string Updater "修改人"
        datetime UpdateDate "修改时间"
    }
    
    UTBaseCode {
        Guid CodeID PK
        Guid CodeGroupID FK "代码组ID"
        string CodeGroupCode "父编号"
        string PCodeValue "父id"
        string CodeName "名称"
        string CodeValue "值"
        string Memo "说明"
        int SortOrder "排序顺序号"
        bool IsDeleted "删除标记"
        string Adder "添加人"
        datetime AddDate "添加时间"
        string Updater "修改人"
        datetime UpdateDate "修改时间"
    }
    
    UTBaseWechatAdvertise {
        Guid ID PK
        string AdvertiseTitle "标题"
        string Attachment "附件"
        string AdvertiseContent "内容"
        datetime CreateDate "创建时间"
        int64 CreateUser FK "创建人"
        int ViewsCount "点击次数"
        int OrderInt "排序"
        int IsSend "是否发送"
        datetime SendTime "发送时间"
    }
    
    UTBaseWechatAdvertiseDetial {
        Guid ID PK
        Guid AdvertiseID FK "广告ID"
        string DetialTitle "详情标题"
        string DetialContent "详情内容"
        datetime CreateDate "创建时间"
        int64 CreateUser FK "创建人"
        int OrderInt "排序"
    }
    
    UTBaseWechatAdvertiseDetialFile {
        Guid ID PK
        Guid AdvertiseID FK "广告ID"
        Guid AdvertiseDetialID FK "广告详情ID"
        string Attachment "附件名称"
        datetime CreateDate "创建时间"
        int64 CreateUser FK "创建人"
        int OrderInt "排序"
    }
```

## 业务关系说明

1. **用户权限管理**: UTSYSUser表通过UTSYSUserRole中间表与UTSYSRole表建立多对多关系，实现用户角色分配
2. **角色权限管理**: UTSYSRole表通过UTSYSRoleRight中间表与UTSYSRight表建立多对多关系，实现角色权限分配
3. **菜单权限体系**: UTSYSRight表通过ParentRightID自关联实现树形菜单结构，支持多级权限管理
4. **子系统管理**: UTSYSSystem表管理多个子系统，UTSYSRight表关联子系统实现子系统级别的权限控制
5. **岗位权限控制**: UTBasePostAuth表定义岗位操作权限，UTBasePostAuthDetail表实现用户与岗位权限的关联
6. **数据字典管理**: UTBaseCodeGroup和UTBaseCode表实现系统配置的标准化管理，支持分组管理
7. **微信生态集成**: UTBaseWechatAdvertise、UTBaseWechatAdvertiseDetial、UTBaseWechatAdvertiseDetialFile三表实现微信广告内容的分层管理
8. **组织架构集成**: 与UTSYSOrganization表集成，实现基于组织架构的权限管理
9. **项目关联**: 通过UTBaseUnitManage表实现多租户项目级别的权限隔离
10. **会话管理**: 系统通过SessionManager类管理用户登录状态和权限缓存，提高系统性能 