# 考勤管理模块ER图设计

## 模块概述

考勤管理模块是PropertySys智能物业管理系统的重要组成部分，提供完整的员工考勤管理解决方案。模块涵盖考勤打卡、班次管理、排班管理、请假申请、加班管理、补卡申请等全流程功能，并集成钉钉生态实现智能化考勤管理。

## 核心业务流程

### 1. 考勤基础管理流程
考勤点设置 → 员工打卡 → 考勤记录生成 → 状态判断 → 异常处理

### 2. 班次排班管理流程  
班次类型配置 → 排班计划制定 → 员工排班分配 → 动态调整

### 3. 请假管理流程
请假申请 → 工作流审批 → 审批结果处理 → 人事确认 → 考勤关联

### 4. 加班管理流程
加班申请 → 多级审批 → 工时统计 → 调休管理 → 薪资计算

### 5. 补卡管理流程
异常发现 → 补卡申请 → 审批处理 → 考勤记录更新

## 数据库ER图

\`\`\`mermaid
erDiagram
    %% 考勤基础管理
    UTBaseAttPoint {
        uniqueidentifier ID PK "主键"
        nvarchar_50 AttPoint "考勤打卡点名称"
        nvarchar_200 AttAddress "详细地址"
        decimal Lon "经度"
        decimal Lat "纬度"  
        nvarchar_50 Point "点位信息"
        int Distance "允许打卡距离(米)"
        nvarchar_500 Remark "备注"
        datetime CreateDate "创建时间"
        bigint CreateUser "创建人"
        datetime UpdateDate "更新时间"
        bigint UpdateUser "更新人"
        int IsDelete "删除状态"
        datetime DeleteDate "删除时间"
        bigint DeleteUser "删除人"
    }

    UTUnitAttRecord {
        uniqueidentifier ID PK "主键"
        bigint UserID "用户ID"
        uniqueidentifier FaceUserID "人脸识别用户ID"
        datetime AttDate "考勤日期"
        nvarchar_50 TheDay "考勤星期"
        datetime FirstRecordTime "首次打卡时间"
        datetime LastRecordTime "末次打卡时间"
        int Status "状态:0正常,1迟到,2早退,3迟到+早退,4请假"
        datetime CreateDate "创建时间"
        datetime LastUpdateDate "最后更新时间"
        bigint LastUpdateUser "最后更新人"
    }

    %% 班次排班管理
    UTUnitShiftsType {
        uniqueidentifier ID PK "主键"
        uniqueidentifier UnitID "项目ID"
        nvarchar_150 ShiftName "班次名称"
        nvarchar_150 ShortName "班次缩写"
        nvarchar_150 StartTimeStr "开始时间1"
        nvarchar_150 EndTimeStr "结束时间"
        nvarchar_150 StartTimeStr2 "开始时间2"
        decimal WordTime1 "工作时长1"
        decimal WordTime2 "工作时长2"
        int IsCrossDay "是否跨天"
        int ShiftsType "班次类型"
        nvarchar_250 ClassColor "班次颜色"
        int IsOpenRestTime "是否开启休息时间"
        nvarchar_150 RestStartTime "休息开始时间"
        nvarchar_150 RestEndTime "休息结束时间"
        int StartTimeType "上班打卡时间范围"
        int EndTimeType "下班打卡时间范围"
        int SordIndex "排序索引"
        datetime CreateDate "创建时间"
        bigint CreateUser "创建人"
        int IsDelete "删除状态"
    }

    UTUnitSchedualShifts {
        uniqueidentifier ID PK "主键"
        uniqueidentifier UnitID "项目ID"
        uniqueidentifier ShiftTypeID "班次类型ID"
        nvarchar_150 ShiftName "班次名称"
        nvarchar_150 ShortName "班次缩写"
        nvarchar_150 StartTime "开始时间"
        nvarchar_150 EndTime "结束时间"
        bigint UserID "用户ID"
        bigint OrgID "部门ID"
        datetime ShiftDate "排班日期"
        int IsRest "是否休息"
        int Isdynamic "是否动态排班"
        datetime CreateDate "创建时间"
        bigint CreateUser "创建人"
        datetime ModifyDate "修改时间"
        bigint ModifyUser "修改人"
        nvarchar_150 ModifyDesc "修改描述"
        int IsDelete "删除状态"
    }

    %% 请假管理
    UTUnitLeave {
        uniqueidentifier ID PK "主键"
        uniqueidentifier UnitID "项目ID"
        nvarchar_50 LeaveNo "请假单号"
        datetime LeaveStart "请假开始时间"
        datetime LeaveEnd "请假结束时间"
        int LeaveType "请假类型:1事假,2病假,3年假,4调休假"
        int LeaveDay "请假天数"
        decimal LeaveTime "请假小时数"
        ntext LeaveDesc "请假说明"
        datetime LeaveRealStart "实际开始时间"
        datetime LeaveRealEnd "实际结束时间"
        int LeaveRealDay "实际天数"
        decimal LeaveRealTime "实际小时数"
        int Status "状态:0暂存,1退回,2审批中,3审批完成"
        datetime SubmitDate "提交时间"
        int IsAdjust "是否调休"
        datetime CheckDate "审核时间"
        bigint CheckUser "审核人"
        int CheckResult "审核结果"
        ntext CheckDesc "审核描述"
        datetime HRCheckDate "人事确认时间"
        bigint HRCheckUser "人事确认人"
        nvarchar_1000 HRCheckDesc "人事确认描述"
        bigint CreateUser "创建人"
        datetime CreateDate "创建时间"
        bigint OrgID "部门ID"
        bigint ApplyUserID "申请人ID"
        bigint CurrentApprovalUser "当前审批人"
        uniqueidentifier CurrentApproveID "当前审批ID"
        int CurrentApproveOrderIndex "当前审批顺序"
        bigint CurrentDealUser "当前处理人"
        int OrderType "单据类型"
        int ProcessCode "流程代码"
        bigint LastApprovalUser "最后审批人"
        datetime LastApprovalDate "最后审批时间"
        int LastStepCode "最后步骤代码"
        int LastApprovalResult "最后审批结果"
    }

    %% 加班管理
    UTUnitOvertime {
        uniqueidentifier ID PK "主键"
        uniqueidentifier UnitID "项目ID"
        nvarchar_50 OverTimeNo "加班单号"
        datetime OvertimeStart "加班开始时间"
        datetime OvertimeEnd "加班结束时间"
        int OvertimeReason "加班原因:0工作超量,1赶进度,2公司需要,3技术积累,4未按时完成"
        int OvertimeType "加班类型:0平时,1周末,2法定假日"
        int OvertimeDay "加班天数"
        decimal OvertimeTime "加班小时数"
        ntext OvertimeDesc "加班说明"
        int Status "状态:0暂存,1提交,2退回,3批准,4复核退回,5复核批准,6人事确认,99人事作废"
        decimal AdjustTime "可调时间"
        int IsValid "是否有效"
        datetime CheckDate "审核时间"
        bigint CheckUser "审核人"
        int CheckResult "审核结果"
        ntext CheckDesc "审核描述"
        datetime HRCheckDate "人事确认时间"
        bigint HRCheckUser "人事确认人"
        nvarchar_1000 HRCheckDesc "人事确认描述"
        bigint CreateUser "创建人"
        datetime CreateDate "创建时间"
        bigint OrgID "部门ID"
        bigint CurrentDealUser "当前处理人"
        bigint CurrentApprovalUser "当前审批人"
        bigint LastApprovalUser "最后审批人"
        datetime LastApprovalDate "最后审批时间"
        int LastStepCode "最后步骤代码"
        int LastApprovalResult "最后审批结果"
        int IsOver "是否结束"
        datetime UpdateDate "更新时间"
        bigint UpdateUser "更新人"
        int ProcessCode "流程代码"
        int ApplyType "申请类型"
    }

    %% 补卡管理
    UTUnitSchedualCardApply {
        uniqueidentifier ID PK "主键"
        uniqueidentifier UnitID "项目ID"
        nvarchar_50 ApplyCode "申请编号"
        bigint ApplyUserID "申请人ID"
        datetime EventDate "补卡日期"
        nvarchar_500 ApplyInfo "补卡说明"
        int Status "状态:1审批通过"
        bigint CurrentApprovalUser "当前审批人"
        datetime CreateDate "创建时间"
        bigint CreateUser "创建人"
        int IsOver "是否结束"
    }

    %% 钉钉考勤集成
    UTDingDingAttendanceRecordPush {
        uniqueidentifier ID PK "主键"
        bigint UserID "用户ID"
        nvarchar_50 DingDingUserID "钉钉用户ID"
        datetime AttendanceDate "考勤日期"
        nvarchar_50 AttendanceType "考勤类型"
        datetime CheckTime "打卡时间"
        nvarchar_500 CheckAddress "打卡地址"
        nvarchar_50 DeviceID "设备ID"
        decimal Longitude "经度"
        decimal Latitude "纬度"
        nvarchar_50 CheckResult "打卡结果"
        datetime CreateDate "创建时间"
        int IsSync "是否同步"
        datetime SyncDate "同步时间"
        nvarchar_500 Remark "备注"
    }

    %% 年假管理
    UTSYSUserYearHoliday {
        uniqueidentifier ID PK "主键"
        bigint UserID "用户ID"
        int Year "年份"
        decimal TotalDays "总年假天数"
        decimal UsedDays "已使用天数"
        decimal RemainDays "剩余天数"
        decimal CarryOverDays "结转天数"
        datetime CreateDate "创建时间"
        bigint CreateUser "创建人"
        datetime UpdateDate "更新时间"
        bigint UpdateUser "更新人"
        int Status "状态"
        nvarchar_500 Remark "备注"
    }

    %% 关联关系
    UTUnitSchedualShifts }|--|| UTUnitShiftsType : "belongs_to"
    UTUnitAttRecord }|--|| UTBaseAttPoint : "recorded_at"
    UTUnitLeave }|--|| UTUnitAttRecord : "affects"
    UTUnitOvertime }|--|| UTUnitAttRecord : "affects"
    UTUnitSchedualCardApply }|--|| UTUnitAttRecord : "supplements"
    UTDingDingAttendanceRecordPush }|--|| UTUnitAttRecord : "syncs_to"
    UTSYSUserYearHoliday }|--|| UTUnitLeave : "quota_for"
\`\`\`

## 核心表详细说明

### 1. UTBaseAttPoint (考勤打卡点表)
**业务作用**: 管理考勤打卡点的地理位置信息和打卡规则
**核心字段**:
- `AttPoint`: 打卡点名称，支持多个打卡点管理
- `Lon/Lat`: 经纬度坐标，实现地理位置验证
- `Distance`: 允许打卡距离，控制打卡范围精度
- `AttAddress`: 详细地址描述

### 2. UTUnitAttRecord (考勤记录表)  
**业务作用**: 存储员工每日考勤打卡记录和状态
**核心字段**:
- `UserID`: 关联员工信息
- `FirstRecordTime/LastRecordTime`: 首次和末次打卡时间
- `Status`: 考勤状态枚举（正常/迟到/早退/请假等）
- `FaceUserID`: 人脸识别系统用户ID

### 3. UTUnitShiftsType (班次类型表)
**业务作用**: 定义各种班次类型的工作时间和规则
**核心字段**:
- `ShiftName`: 班次名称（如：白班、夜班、轮班等）
- `StartTimeStr/EndTimeStr`: 工作时间段定义
- `WordTime1/WordTime2`: 支持分段工作时长计算
- `IsCrossDay`: 支持跨天班次（如夜班）
- `IsOpenRestTime`: 支持休息时间配置

### 4. UTUnitSchedualShifts (排班表)
**业务作用**: 管理员工具体的排班计划和执行情况
**核心字段**:
- `UserID/OrgID`: 员工和部门关联
- `ShiftTypeID`: 关联班次类型
- `ShiftDate`: 具体排班日期
- `IsRest`: 标识是否为休息日
- `Isdynamic`: 支持动态排班调整

### 5. UTUnitLeave (请假申请表)
**业务作用**: 管理员工请假申请的完整生命周期
**核心字段**:
- `LeaveType`: 请假类型（事假/病假/年假/调休假）
- `LeaveStart/LeaveEnd`: 请假时间段
- `LeaveRealStart/LeaveRealEnd`: 实际请假时间（支持调整）
- `Status`: 审批状态流转
- `CurrentApprovalUser`: 当前审批人（工作流支持）

### 6. UTUnitOvertime (加班申请表)
**业务作用**: 管理员工加班申请和工时统计
**核心字段**:
- `OvertimeReason`: 加班原因分类
- `OvertimeType`: 加班类型（平时/周末/法定假日）
- `OvertimeTime`: 加班工时统计
- `AdjustTime`: 可调休时间计算
- `Status`: 多级审批状态管理

### 7. UTUnitSchedualCardApply (补卡申请表)
**业务作用**: 处理异常考勤的补卡申请
**核心字段**:
- `ApplyCode`: 唯一申请编号
- `EventDate`: 需要补卡的日期
- `ApplyInfo`: 补卡原因说明
- `CurrentApprovalUser`: 审批人管理

### 8. UTDingDingAttendanceRecordPush (钉钉考勤推送表)
**业务作用**: 集成钉钉生态的考勤数据同步
**核心字段**:
- `DingDingUserID`: 钉钉用户标识
- `CheckTime`: 钉钉打卡时间
- `CheckAddress`: 钉钉打卡地址
- `IsSync`: 数据同步状态控制

### 9. UTSYSUserYearHoliday (年假管理表)
**业务作用**: 管理员工年假额度和使用情况
**核心字段**:
- `TotalDays`: 年度总年假天数
- `UsedDays`: 已使用年假天数  
- `RemainDays`: 剩余年假天数
- `CarryOverDays`: 上年度结转天数

## 数据关系特征

### 1. 核心数据流转
- **考勤点** → **考勤记录** → **状态分析** → **异常处理**
- **班次配置** → **排班计划** → **考勤执行** → **结果统计**

### 2. 审批流程集成
- 请假/加班申请表集成工作流引擎
- 支持多级审批和状态流转
- 人事最终确认和数据归档

### 3. 第三方系统集成
- 钉钉考勤数据实时同步
- 人脸识别系统数据关联
- 工资系统工时数据对接

### 4. 数据统计分析
- 支持多维度考勤统计
- 异常考勤数据挖掘
- 工时和调休数据计算

## 业务规则说明

### 1. 考勤状态判断规则
- 基于排班信息和实际打卡时间计算状态
- 支持弹性工作时间和打卡时间窗口
- 考虑请假和加班对考勤状态的影响

### 2. 工时计算规则  
- 班次类型决定标准工作时长
- 加班时间按类型进行工时倍数计算
- 调休时间可冲抵加班工时

### 3. 审批流程规则
- 请假和加班申请支持工作流引擎
- 不同级别审批权限配置
- 人事部门最终确认权限

### 4. 数据同步规则
- 钉钉考勤数据定时同步
- 人脸识别数据实时推送
- 异常数据及时预警处理

## 扩展性设计

### 1. 多项目支持
- 所有表包含UnitID字段实现数据隔离
- 支持集团化多项目管理

### 2. 灵活班次配置
- 支持分段工作时间
- 支持跨天班次
- 支持动态排班调整

### 3. 第三方集成接口
- 标准化的数据同步接口
- 支持多种考勤设备集成
- 开放的API接口设计

### 4. 数据分析扩展
- 预留统计分析字段
- 支持自定义报表配置
- 数据挖掘和智能分析 