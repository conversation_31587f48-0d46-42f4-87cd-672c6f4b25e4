# 人事管理模块UML类图

## 模块概述

人事管理模块是PropertySys企业级物业管理系统的核心人力资源管理子系统，负责员工全生命周期管理，包括入职、转正、异动、离职等关键业务流程。

## 核心类图设计

### 主要实体类关系

```mermaid
classDiagram
    class UTSYSEmployee {
        +string EmployeeID
        +string UserID
        +string EmployeeName
        +string EmployeeGender
        +DateTime? UserBirthDay
        +string EmployeePhone
        +string EmployeeCardId
        +DateTime? UserEntryDate
        +int? UserAnnualLeave
        +string EmployeeEducation
        +string EmployeeBankCard
        +string EmployeeBankName
        +string EmergencyContactName
        +string EmergencyContactPhone
        +string InsuranceNumber
        +DateTime? UserLeaveDate
        +string LeaveReason
        +DateTime? CreateDate
        +string CreateUser
        +DateTime? UpdateDate
        +string UpdateUser
        +bool? IsDeleted
        +getEmployeeInfo()
        +updateEmployeeInfo()
        +validateEmployee()
    }

    class UTSYSUser {
        +string UserID
        +string UserName
        +string UserAccount
        +string UserPassword
        +string UserType
        +string UnitID
        +string WeixinOpenID
        +string WeixinNickName
        +string WeixinHeadImg
        +DateTime? CreateDate
        +string CreateUser
        +DateTime? UpdateDate
        +string UpdateUser
        +bool? IsDeleted
        +authenticateUser()
        +updateUserInfo()
        +resetPassword()
    }

    class UTXZRegularApply {
        +string RegularApplyID
        +string ApplicantUserID
        +DateTime? RegularDate
        +string WorkSummary
        +string SelfAssessment
        +string DeptManagerOpinion
        +string HROpinion
        +string GeneralManagerOpinion
        +int? ApplyStatus
        +string CurrentApprover
        +string ApprovalHistory
        +DateTime? CreateDate
        +string CreateUser
        +DateTime? UpdateDate
        +string UpdateUser
        +submitApply()
        +approveApply()
        +rejectApply()
        +getApplyStatus()
    }

    class UTXZDepartureApply {
        +string DepartureApplyID
        +string ApplicantUserID
        +DateTime? DepartureDate
        +string DepartureReason
        +string WorkHandover
        +string AttendanceSettlement
        +string ExpenseSettlement
        +string EquipmentReturn
        +int? ApplyStatus
        +string CurrentApprover
        +string ApprovalHistory
        +DateTime? CreateDate
        +string CreateUser
        +DateTime? UpdateDate
        +string UpdateUser
        +submitDepartureApply()
        +approveDeparture()
        +completeHandover()
        +calculateSettlement()
    }

    class UTXZUserChangeApply {
        +string ChangeApplyID
        +string ApplicantUserID
        +string ChangeType
        +string CurrentPosition
        +string NewPosition
        +decimal? CurrentSalary
        +decimal? NewSalary
        +string ChangeReason
        +DateTime? EffectiveDate
        +int? ApplyStatus
        +string CurrentApprover
        +string ApprovalHistory
        +DateTime? CreateDate
        +string CreateUser
        +DateTime? UpdateDate
        +string UpdateUser
        +submitChangeApply()
        +approveChange()
        +executeChange()
        +rollbackChange()
    }

    class UTSYSUserEntryManage {
        +string EntryID
        +string UserID
        +string EntryType
        +DateTime? EntryDate
        +string Position
        +string Department
        +decimal? InitialSalary
        +string ContractType
        +DateTime? ContractStartDate
        +DateTime? ContractEndDate
        +int? ProbationPeriod
        +string EntryStatus
        +DateTime? CreateDate
        +string CreateUser
        +DateTime? UpdateDate
        +string UpdateUser
        +processEntry()
        +validateEntry()
        +completeEntry()
        +generateContract()
    }

    class UTXZRegularApplyFile {
        +string FileID
        +string RegularApplyID
        +string FileName
        +string FilePath
        +string FileType
        +long? FileSize
        +DateTime? UploadDate
        +string UploadUser
        +bool? IsDeleted
        +uploadFile()
        +downloadFile()
        +deleteFile()
        +validateFile()
    }

    %% 关联关系
    UTSYSEmployee ||--|| UTSYSUser : "关联"
    UTSYSEmployee ||--o{ UTXZRegularApply : "申请转正"
    UTSYSEmployee ||--o{ UTXZDepartureApply : "申请离职"
    UTSYSEmployee ||--o{ UTXZUserChangeApply : "申请异动"
    UTSYSEmployee ||--o{ UTSYSUserEntryManage : "入职管理"
    UTXZRegularApply ||--o{ UTXZRegularApplyFile : "转正附件"

    %% 业务服务类
    class HRManagementService {
        +processEntryApplication()
        +handleRegularApplication()
        +manageDepartureProcess()
        +executeUserChange()
        +generateHRReports()
        +calculateAttendance()
        +manageInsurance()
        +handleEmployeeCare()
    }

    class WorkflowService {
        +initiateWorkflow()
        +processApproval()
        +sendNotification()
        +trackWorkflowStatus()
        +escalateWorkflow()
        +archiveWorkflow()
    }

    %% 服务类关联
    HRManagementService ..> UTSYSEmployee : "管理"
    HRManagementService ..> UTXZRegularApply : "处理"
    HRManagementService ..> UTXZDepartureApply : "处理"
    HRManagementService ..> UTXZUserChangeApply : "处理"
    WorkflowService ..> UTXZRegularApply : "工作流"
    WorkflowService ..> UTXZDepartureApply : "工作流"
    WorkflowService ..> UTXZUserChangeApply : "工作流"
```

## 核心类详细设计

### 1. 员工基础信息类（UTSYSEmployee）

**职责**：管理员工基本信息和档案
**特点**：
- 完整的员工生命周期信息
- 支持多维度员工信息管理
- 集成保险和银行信息
- 紧急联系人管理

### 2. 用户账户类（UTSYSUser）

**职责**：管理系统用户账户和权限
**特点**：
- 支持多种登录方式
- 微信生态集成
- 多租户项目支持
- 安全认证机制

### 3. 转正申请类（UTXZRegularApply）

**职责**：处理员工转正流程
**特点**：
- 多级审批工作流
- 完整的评估体系
- 审批历史追踪
- 状态流转管理

### 4. 离职申请类（UTXZDepartureApply）

**职责**：管理员工离职流程
**特点**：
- 工作移交管理
- 财务结算处理
- 设备归还跟踪
- 离职手续完整性

### 5. 异动申请类（UTXZUserChangeApply）

**职责**：处理员工岗位异动
**特点**：
- 多类型异动支持
- 薪资变更管理
- 生效日期控制
- 变更历史记录

## 设计模式应用

### 1. 策略模式（Strategy Pattern）

```mermaid
classDiagram
    class ApprovalStrategy {
        <<interface>>
        +approve(application: object): bool
        +reject(application: object): bool
        +getNextApprover(): string
    }

    class RegularApprovalStrategy {
        +approve(application: UTXZRegularApply): bool
        +reject(application: UTXZRegularApply): bool
        +getNextApprover(): string
    }

    class DepartureApprovalStrategy {
        +approve(application: UTXZDepartureApply): bool
        +reject(application: UTXZDepartureApply): bool
        +getNextApprover(): string
    }

    class ChangeApprovalStrategy {
        +approve(application: UTXZUserChangeApply): bool
        +reject(application: UTXZUserChangeApply): bool
        +getNextApprover(): string
    }

    ApprovalStrategy <|-- RegularApprovalStrategy
    ApprovalStrategy <|-- DepartureApprovalStrategy
    ApprovalStrategy <|-- ChangeApprovalStrategy
```

### 2. 工厂模式（Factory Pattern）

```mermaid
classDiagram
    class ApplicationFactory {
        +createApplication(type: string): IApplication
        +createWorkflow(applicationType: string): IWorkflow
    }

    class IApplication {
        <<interface>>
        +submit(): bool
        +approve(): bool
        +reject(): bool
        +getStatus(): string
    }

    class RegularApplication {
        +submit(): bool
        +approve(): bool
        +reject(): bool
        +getStatus(): string
    }

    class DepartureApplication {
        +submit(): bool
        +approve(): bool
        +reject(): bool
        +getStatus(): string
    }

    ApplicationFactory ..> IApplication : "创建"
    IApplication <|-- RegularApplication
    IApplication <|-- DepartureApplication
```

### 3. 观察者模式（Observer Pattern）

```mermaid
classDiagram
    class ApplicationSubject {
        +observers: List~Observer~
        +addObserver(observer: Observer): void
        +removeObserver(observer: Observer): void
        +notifyObservers(): void
    }

    class Observer {
        <<interface>>
        +update(application: IApplication): void
    }

    class EmailNotificationObserver {
        +update(application: IApplication): void
    }

    class WeChatNotificationObserver {
        +update(application: IApplication): void
    }

    class AuditLogObserver {
        +update(application: IApplication): void
    }

    ApplicationSubject o--> Observer
    Observer <|-- EmailNotificationObserver
    Observer <|-- WeChatNotificationObserver
    Observer <|-- AuditLogObserver
```

## 业务流程类图

### 入职流程类图

```mermaid
classDiagram
    class EntryProcess {
        +candidate: CandidateInfo
        +entryManager: UTSYSUserEntryManage
        +employee: UTSYSEmployee
        +user: UTSYSUser
        +initiateEntry(): bool
        +validateDocuments(): bool
        +createEmployee(): bool
        +assignUser(): bool
        +completeEntry(): bool
    }

    class CandidateInfo {
        +name: string
        +idCard: string
        +phone: string
        +email: string
        +position: string
        +department: string
        +validateInfo(): bool
    }

    EntryProcess --> CandidateInfo
    EntryProcess --> UTSYSUserEntryManage
    EntryProcess --> UTSYSEmployee
    EntryProcess --> UTSYSUser
```

### 转正流程类图

```mermaid
classDiagram
    class RegularProcess {
        +application: UTXZRegularApply
        +employee: UTSYSEmployee
        +approvers: List~Approver~
        +workflowEngine: WorkflowEngine
        +submitApplication(): bool
        +processApproval(): bool
        +updateEmployeeStatus(): bool
        +notifyStakeholders(): bool
    }

    class Approver {
        +approverID: string
        +approverName: string
        +approverRole: string
        +approve(application: UTXZRegularApply): bool
        +reject(application: UTXZRegularApply): bool
        +addComments(comments: string): void
    }

    class WorkflowEngine {
        +currentStep: int
        +totalSteps: int
        +processNext(): bool
        +rollback(): bool
        +getStatus(): string
    }

    RegularProcess --> UTXZRegularApply
    RegularProcess --> UTSYSEmployee
    RegularProcess --> Approver
    RegularProcess --> WorkflowEngine
```

## 扩展性设计

### 1. 插件架构

系统支持通过插件方式扩展人事管理功能：

- 薪资管理插件
- 绩效考核插件
- 培训管理插件
- 考勤管理插件

### 2. 工作流引擎集成

支持可配置的工作流引擎，可根据不同公司需求调整审批流程：

- 审批层级配置
- 审批条件设置
- 自动流转规则
- 异常处理机制

### 3. 多系统集成

预留接口支持与外部系统集成：

- 财务系统集成
- 考勤系统集成
- 邮件系统集成
- 微信企业号集成

## 技术特性

1. **多层架构**：Entity → Dal → Biz → Web → Server
2. **ORM支持**：使用NHibernate进行数据访问
3. **工作流引擎**：集成Workflow.Engine支持复杂业务流程
4. **缓存机制**：支持数据缓存提升性能
5. **日志审计**：完整的操作日志和审计追踪
6. **安全控制**：基于角色的权限控制体系

## 总结

人事管理模块UML类图体现了完整的人力资源管理体系设计，通过合理的类关系设计和设计模式应用，确保了系统的可扩展性、可维护性和业务完整性。模块支持员工全生命周期管理，集成工作流引擎，为企业级物业管理提供了完善的人事管理解决方案。 