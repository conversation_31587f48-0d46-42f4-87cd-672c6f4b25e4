# 微信扫码登录

## 概述

本系统支持微信扫码登录，用户可以通过微信开放平台的授权，使用微信账号快速登录系统。

## 前端实现

前端的微信登录功能主要通过 `wxLogin.js` 实现，该脚本由微信官方提供。在 `login.cshtml` 页面中，当用户选择“微信扫码”登录方式时，会初始化 `WxLogin` 对象，并显示一个二维码供用户扫描。

### 相关代码 (`login.cshtml`)

```javascript
if ($("#isneedinspection3").prop("checked")) {
    $('#login_container').show();
    $('.pclogin').hide();
    var redirect = "{$url}"
    var obj = new WxLogin({
        id: 'login_container',
        appid: 'wx7b35456f3546076c',
        scope: 'snsapi_login', 
        redirect_uri: encodeURIComponent('http://www.ccul.vip/ModuleWechat/WXLogin/WxCallBack'),
        state: '1211111',
        style: 'black',
        href: 'data:text/css;base64,LmltcG93ZXJCb3ggLnFyY29kZSB7d2lkdGg6IDE1MHB4O30KLmltcG93ZXJCb3ggLnRpdGxlIHtkaXNwbGF5OiBub25lO30KLmltcG93ZXJCb3ggLmluZm8ge3dpZHRoOiAyMDBweDt9Ci5zdGF0dXNfaWNvbiB7ZGlzcGxheTogbm9uZX0KLmltcG93ZXJCb3ggLnN0YXR1cyB7dGV4dC1hbGlnbjogY2VudGVyO30g',
    })
}
```

**参数说明:**

*   `id`: 用于显示二维码的容器 ID。
*   `appid`: 微信开放平台应用的 AppID。
*   `scope`: 授权作用域，`snsapi_login` 表示获取用户基本信息。
*   `redirect_uri`: 用户扫描二维码并确认授权后，微信服务器会重定向到该地址，并携带 `code` 和 `state` 参数。
*   `state`: 用于防止 CSRF 攻击的随机字符串。

## 后端实现

后端通过 `WXLoginController` 处理微信登录的回调请求。`WxCallBack` 方法接收微信服务器发送的 `code`，然后通过该 `code` 获取用户的 `access_token` 和 `openid`，最终获取用户的基本信息（如 `unionid`）。

### 核心逻辑 (`WXLoginController.cs`)

1.  **获取用户信息**: `WxCallBack` 方法首先调用 `backEnt.getWeixinUserInfoJSON(code)`，通过 `code` 获取微信用户的 `unionid`。

2.  **用户身份验证**: 系统根据获取到的 `unionid` 在 `UTSYSUser` 表中查找对应的用户。

3.  **登录或绑定**:
    *   如果找到了匹配的用户，则直接调用 `model.Login()` 方法，让用户登录系统。
    *   如果没有找到匹配的用户，则重定向到 `BindWxKF` 页面，引导用户将微信账号与系统内的现有账号进行绑定。

### 相关代码 (`WXLoginController.cs`)

```csharp
public ActionResult WxCallBack(string code, string state)
{
    WX_callback backEnt = new WX_callback();
    Weixin_info userEnt = backEnt.getWeixinUserInfoJSON(code);

    string url = "~/home/<USER>";
    if (userEnt == null)
    {
        url = "~/ModuleSys/Account/Login";
    }
    else
    {
        IList<UTSYSUser> UTSYSUserList = serviceUTSYSUser.ListBy(ExpressionWrapper<UTSYSUser>.Where(x => (x.IsDeleted == null || x.IsDeleted == false) && x.UnionID == userEnt.unionid));
        if (UTSYSUserList != null && UTSYSUserList.Count > 0)
        {
            ModelAccountLogin model = new ModelAccountLogin();
            model.AcountType = 3;
            model.User = UTSYSUserList[0];

            string message = model.Login();
            if (!string.IsNullOrEmpty(message))
            {
                url = "~/ModuleSys/Account/Login";
                Response.Redirect(System.Web.HttpUtility.HtmlEncode(url), true);
            }
        }
        else
        {
            url = "~/ModuleWeChat/WXLogin/BindWxKF?UnionID=" + userEnt.unionid;
            Response.Redirect(System.Web.HttpUtility.HtmlEncode(url), true);
        }
    }

    Response.Redirect(System.Web.HttpUtility.HtmlEncode(url), true);

    return null;
}
```
