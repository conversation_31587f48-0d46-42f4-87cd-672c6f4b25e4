# 巡检管理模块ER图

## 表结构说明

### 核心实体表

#### UTEQInspectPlan (巡检计划表)
- **主键**: ID (Guid)
- **外键**: UnitID (项目ID, Guid?)
- **核心字段**: PlanName, PlanType, PlanStartDate, PlanEndDate, WriteYear, PlanStatus
- **说明**: 管理巡检计划的制定、执行周期和状态

#### UTEQInspectTaskDetial (巡检任务详情表)
- **主键**: ID (Guid)
- **外键**: UnitID (项目ID, Guid?), InspectPlanID (计划ID, Guid?), InspectPlanDetialID (计划详情ID, Guid?), EQID (设备ID, Guid?)
- **核心字段**: CycleInt, CycleUnit, StartDate, EndDate, InspectUser, InspectDate, Status
- **说明**: 记录具体设备的巡检任务安排和执行情况

#### UTEQInspectTaskDetialInfo (巡检任务详情内容表)
- **主键**: ID (Guid)
- **外键**: InspectTaskDetialID (任务详情ID, Guid?), CheckInfoID (检查项ID, Guid?)
- **核心字段**: CheckInfo, CheckMethod, BadDesc, IsNeedRepair, IsNeedZG, Status
- **说明**: 记录巡检过程中发现的问题和整改情况

#### UTBaseCheckInfo (设备检查项表)
- **主键**: ID (Guid)
- **外键**: UnitID (单位ID, Guid?), EQClassID (分类ID, Guid?)
- **核心字段**: CheckItem, CheckMethod, CheckStandard, OrderIndex
- **说明**: 定义各类设备的检查项目和标准

#### UTEQEquipment (设备信息表)
- **主键**: ID (Guid)
- **外键**: UnitID (所属园区, Guid?), BuildID (区域ID, Guid?), EQClassID (设备类型, Guid?)
- **核心字段**: EQCode, EQName, Manager, EQStatus, IsNeedInspection
- **说明**: 管理物业项目中的各类设备信息

#### UTBaseEQClass (设备分类表)
- **主键**: ID (Guid)
- **外键**: EQSystemID (所属系统, Guid?), PClassID (上级分类, Guid?)
- **核心字段**: ClassCode, ClassName, ClassLevel, IsLast
- **说明**: 设备分类管理，支持多级分类体系

### 关联支撑表

#### UTBaseUnitManage (项目管理表)
- **主键**: ID (Guid)
- **外键**: PID (父级ID, Guid?)
- **核心字段**: UnitCode, UnitName, UnitLevel, Manager, Tel
- **说明**: 物业管理项目的组织架构

#### UTUnitBuild (建筑物信息表)
- **主键**: ID (Guid)
- **外键**: UnitID (项目ID, Guid?)
- **核心字段**: BuildName, BuildType, BuildArea, UpFloor, DownFloor
- **说明**: 项目内建筑物和区域信息

## ER关系图

```mermaid
erDiagram
    UTBaseUnitManage {
        Guid ID PK
        Guid PID FK "父级项目ID"
        string UnitCode "项目编码"
        string UnitName "项目名称"
        int UnitLevel "项目级别"
        string Manager "负责人"
        string Tel "联系电话"
        string UnitAddress "项目地址"
        DateTime CreateDate "创建时间"
        int64 CreateUser "创建人"
    }
    
    UTUnitBuild {
        Guid ID PK
        Guid UnitID FK "项目ID"
        string BuildName "建筑名称"
        int BuildType "建筑类型"
        string Contacts "联系人"
        string ContactsTel "联系电话"
        decimal BuildArea "建筑面积"
        int UpFloor "地上层数"
        int DownFloor "地下层数"
    }
    
    UTBaseEQClass {
        Guid ID PK
        Guid EQSystemID FK "所属系统"
        Guid PClassID FK "上级分类"
        string ClassCode "分类编号"
        string ClassName "分类名称"
        int ClassLevel "分类级别"
        int IsLast "是否最终类型"
        int IsDayCheck "是否日检"
        int IsWeekCheck "是否周检"
        int IsMonthCheck "是否月检"
        int IsQuarterCheck "是否季检"
        int IsHalfYearCheck "是否半年检"
        int IsYearCheck "是否年检"
        int OrderIndex "排序"
    }
    
    UTEQEquipment {
        Guid ID PK
        Guid UnitID FK "所属园区"
        Guid BuildID FK "区域ID"
        Guid EQClassID FK "设备类型"
        string EQCode "设备编号"
        string EQName "设备名称"
        string Manager "责任人"
        decimal Count "数量"
        int EQStatus "状态"
        int IsNeedInspection "是否需要巡检"
        string InstallSite "安装位置"
        DateTime InstallDate "安装日期"
        string Brand "品牌"
        string EQModel "型号"
    }
    
    UTBaseCheckInfo {
        Guid ID PK
        Guid UnitID FK "单位ID"
        Guid EQClassID FK "分类ID"
        string CheckItem "检查项"
        string CheckMethod "检查方法"
        string CheckStandard "检查标准"
        int OrderIndex "排序"
        string Remark "备注"
    }
    
    UTEQInspectPlan {
        Guid ID PK
        Guid UnitID FK "项目ID"
        int SystemType "系统类型"
        string PlanName "计划名称"
        int PlanType "计划类型"
        DateTime PlanStartDate "开始时间"
        DateTime PlanEndDate "结束时间"
        string WriteYear "编写年份"
        DateTime WriteDate "编写日期"
        int PlanStatus "状态"
        DateTime CurrentDateS "本轮巡检开始"
        DateTime CurrentDateE "本轮巡检结束"
        int64 WriteUser "编制人"
        DateTime CreateDate "创建时间"
        int64 CreateUser "创建人"
    }
    
    UTEQInspectTaskDetial {
        Guid ID PK
        Guid UnitID FK "项目ID"
        Guid InspectPlanID FK "计划ID"
        Guid InspectPlanDetialID FK "计划详情ID"
        Guid EQID FK "设备ID"
        int CycleInt "周期"
        int CycleUnit "周期单位"
        int CycleCount "总次数"
        int IndexOrder "次数"
        DateTime StartDate "开始时间"
        DateTime EndDate "结束时间"
        string NeedInspectUser "该巡检人"
        int64 InspectUser "实际巡检人"
        DateTime InspectDate "巡检时间"
        int ISOK "是否完好"
        int Status "状态"
    }
    
    UTEQInspectTaskDetialInfo {
        Guid ID PK
        Guid InspectTaskDetialID FK "任务详情ID"
        Guid CheckInfoID FK "检查项ID"
        string CheckInfo "巡检项目"
        string CheckMethod "内容"
        string BadDesc "问题描述"
        int OrderInt "顺序"
        int IsFJ "是否已复检OK"
        DateTime FJTime "复检时间"
        string FJDesc "复检描述"
        int64 FJUser "复检人"
        int IsNeedRepair "是否需要维修"
        int64 IsNeedZG "是否需要整改"
        int Status "状态"
        string ZGUserID "待整改人ID"
        string ZGUserName "待整改人姓名"
        DateTime ZGDate "整改日期"
    }
    
    %% 关系定义
    UTBaseUnitManage ||--o{ UTBaseUnitManage : "项目层级"
    UTBaseUnitManage ||--o{ UTUnitBuild : "包含建筑"
    UTBaseUnitManage ||--o{ UTEQEquipment : "管理设备"
    UTBaseUnitManage ||--o{ UTEQInspectPlan : "制定巡检计划"
    UTBaseUnitManage ||--o{ UTBaseCheckInfo : "定义检查项"
    
    UTUnitBuild ||--o{ UTEQEquipment : "包含设备"
    
    UTBaseEQClass ||--o{ UTBaseEQClass : "分类层级"
    UTBaseEQClass ||--o{ UTEQEquipment : "设备分类"
    UTBaseEQClass ||--o{ UTBaseCheckInfo : "检查项分类"
    
    UTEQInspectPlan ||--o{ UTEQInspectTaskDetial : "生成巡检任务"
    UTEQEquipment ||--o{ UTEQInspectTaskDetial : "设备巡检任务"
    UTEQInspectTaskDetial ||--o{ UTEQInspectTaskDetialInfo : "任务检查内容"
    UTBaseCheckInfo ||--o{ UTEQInspectTaskDetialInfo : "检查项依据"
```

## 业务关系分析

### 主要业务流程

1. **巡检计划制定**: 
   - 基于项目(UTBaseUnitManage)和设备分类(UTBaseEQClass)制定巡检计划(UTEQInspectPlan)
   - 根据设备类型配置相应的检查项(UTBaseCheckInfo)

2. **巡检任务生成**:
   - 根据巡检计划(UTEQInspectPlan)和设备信息(UTEQEquipment)生成具体的巡检任务(UTEQInspectTaskDetial)
   - 按照周期和频次自动或手动生成任务

3. **巡检执行**:
   - 巡检人员根据任务(UTEQInspectTaskDetial)进行现场检查
   - 针对每个检查项(UTBaseCheckInfo)记录检查结果
   - 发现问题时创建问题记录(UTEQInspectTaskDetialInfo)

4. **问题整改**:
   - 根据问题严重程度决定是否需要整改(IsNeedZG)或维修(IsNeedRepair)
   - 指派整改人员并跟踪整改进度
   - 整改完成后进行复检确认

### 关键特性

1. **多级项目管理**: 支持集团-分公司-项目的多级管理架构
2. **设备分类体系**: 支持多级设备分类，便于管理不同类型设备
3. **灵活的巡检周期**: 支持日检、周检、月检、季检、半年检、年检
4. **完整的问题跟踪**: 从发现问题到整改完成的全流程管理
5. **检查标准化**: 通过检查项配置实现检查内容的标准化

### 数据完整性约束

1. **外键约束**: 确保数据引用完整性
2. **业务规则**: 设备必须属于某个项目和建筑
3. **状态控制**: 通过状态字段控制业务流程
4. **权限控制**: 基于项目层级的数据访问权限

## 模块功能覆盖

该ER设计完整支持菜单中的所有功能：

- **巡检自主设置**: 通过UTBaseCheckInfo配置检查项
- **巡检计划**: UTEQInspectPlan管理计划制定
- **巡检任务**: UTEQInspectTaskDetial管理任务执行
- **巡检问题明细**: UTEQInspectTaskDetialInfo记录问题详情
- **巡检整改**: 通过IsNeedZG字段和相关流程管理整改
- **巡检统计**: 基于各表数据进行统计分析 