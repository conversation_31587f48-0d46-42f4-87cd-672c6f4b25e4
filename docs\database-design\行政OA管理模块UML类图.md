# PropertySys_ZGHQ 行政OA管理模块 UML类图设计

## 模块概述

行政OA管理模块是PropertySys企业级物业管理系统的重要组成部分，主要负责企业内部的行政事务管理，包括报销管理、出差管理、预支管理、用车管理等核心业务功能。

## 系统架构

### 三层架构设计
- **表现层**: Controller + View + ViewModel
- **业务逻辑层**: Service + Domain Model + Workflow
- **数据访问层**: Repository + Entity + UnitOfWork

## 核心类图

### 报销管理子系统

```mermaid
classDiagram
    class FeeApplyService {
        +CreateApplication(dto: FeeApplicationDto) Result
        +SubmitForApproval(id: string) Result
        +ApproveApplication(id: string, decision: string) Result
        +GetApplicationsByStatus(status: string) List
    }

    class UTUnitFeeApply {
        +FeeApplyID: string
        +UnitManageID: string
        +EmployeeID: string
        +FeeApplyCode: string
        +TotalAmount: decimal
        +Status: string
        +CreateTime: DateTime
        +ValidateApplication() ValidationResult
        +CalculateTotalAmount() decimal
    }

    class UTUnitFeeApplyDetail {
        +FeeApplyDetailID: string
        +FeeApplyID: string
        +ExpenseType: string
        +Amount: decimal
        +ExpenseDate: DateTime
        +Description: string
        +ValidateExpenseItem() ValidationResult
    }

    class UTUnitFeeApplyDetailFile {
        +FileID: string
        +FeeApplyDetailID: string
        +FileName: string
        +FilePath: string
        +FileSize: long
        +UploadTime: DateTime
    }

    FeeApplyService --> UTUnitFeeApply
    UTUnitFeeApply ||--o{ UTUnitFeeApplyDetail
    UTUnitFeeApplyDetail ||--o{ UTUnitFeeApplyDetailFile
```

### 出差管理子系统

```mermaid
classDiagram
    class EvectionService {
        +CreateEvectionPlan(dto: EvectionDto) Result
        +SubmitForApproval(id: string) Result
        +ApproveEvection(id: string, decision: string) Result
        +RecordEvectionDetails(details: EvectionDetailDto) Result
    }

    class UTUnitEvection {
        +EvectionID: string
        +UnitManageID: string
        +EmployeeID: string
        +EvectionCode: string
        +StartDate: DateTime
        +EndDate: DateTime
        +Destination: string
        +Purpose: string
        +Status: string
        +CalculateDuration() int
        +CanModify() bool
    }

    class UTUnitEvectionDetail {
        +EvectionDetailID: string
        +EvectionID: string
        +DetailDate: DateTime
        +Location: string
        +Activity: string
        +ExpenseAmount: decimal
        +Transport: string
        +Accommodation: string
    }

    EvectionService --> UTUnitEvection
    UTUnitEvection ||--o{ UTUnitEvectionDetail
```

### 预支管理子系统

```mermaid
classDiagram
    class AdvanceService {
        +CreateAdvanceRequest(dto: AdvanceDto) Result
        +ApproveAdvance(id: string, decision: string) Result
        +DisburseAdvance(id: string, amount: decimal) Result
        +RecordRepayment(repayment: RepaymentDto) Result
    }

    class UTCWAdvance {
        +AdvanceID: string
        +UnitManageID: string
        +EmployeeID: string
        +AdvanceCode: string
        +AdvanceAmount: decimal
        +RepaidAmount: decimal
        +DueDate: DateTime
        +Status: string
        +CalculateOutstanding() decimal
        +IsOverdue() bool
    }

    class UTCWAdvanceDetail {
        +AdvanceDetailID: string
        +AdvanceID: string
        +RepayAmount: decimal
        +RepayDate: DateTime
        +RepayMethod: string
        +Remark: string
    }

    class UTCWAdvanceFile {
        +FileID: string
        +AdvanceID: string
        +FileName: string
        +FilePath: string
        +FileType: string
        +UploadTime: DateTime
    }

    AdvanceService --> UTCWAdvance
    UTCWAdvance ||--o{ UTCWAdvanceDetail
    UTCWAdvance ||--o{ UTCWAdvanceFile
```

### 用车管理子系统

```mermaid
classDiagram
    class CompanyCarService {
        +CreateCarApplication(dto: CarApplicationDto) Result
        +ApproveCarUsage(id: string, decision: string) Result
        +AssignVehicle(applicationId: string, carId: string) Result
        +RecordCarUsage(usage: CarUsageDto) Result
        +GetAvailableCars(startTime: DateTime, endTime: DateTime) List
    }

    class UTUnitCompanyCar {
        +CompanyCarID: string
        +UnitManageID: string
        +CarNumber: string
        +CarModel: string
        +CarBrand: string
        +Status: string
        +Mileage: decimal
        +LastMaintenance: DateTime
        +IsAvailable(startTime: DateTime, endTime: DateTime) bool
        +RequiresMaintenance() bool
    }

    class UTUnitCompanyCarApply {
        +CarApplyID: string
        +CompanyCarID: string
        +EmployeeID: string
        +StartTime: DateTime
        +EndTime: DateTime
        +Destination: string
        +Purpose: string
        +Status: string
        +CalculateUsageDuration() TimeSpan
        +ValidateTimeSlot() ValidationResult
    }

    CompanyCarService --> UTUnitCompanyCar
    CompanyCarService --> UTUnitCompanyCarApply
    UTUnitCompanyCar ||--o{ UTUnitCompanyCarApply
```

## 设计模式应用

### 1. 服务层模式 (Service Layer Pattern)
每个业务领域都有对应的服务类，封装复杂的业务逻辑：
- `FeeApplyService`: 报销业务逻辑
- `EvectionService`: 出差业务逻辑  
- `AdvanceService`: 预支业务逻辑
- `CompanyCarService`: 用车业务逻辑

### 2. 仓储模式 (Repository Pattern)
```mermaid
classDiagram
    class IFeeApplyRepository {
        <<interface>>
        +GetById(id: string) UTUnitFeeApply
        +Save(entity: UTUnitFeeApply) void
        +GetByStatus(status: string) List
    }

    class FeeApplyRepository {
        +GetById(id: string) UTUnitFeeApply
        +Save(entity: UTUnitFeeApply) void
        +GetByStatus(status: string) List
    }

    IFeeApplyRepository <|-- FeeApplyRepository
    FeeApplyService --> IFeeApplyRepository
```

### 3. 工作单元模式 (Unit of Work Pattern)
```mermaid
classDiagram
    class IUnitOfWork {
        <<interface>>
        +BeginTransaction() void
        +Commit() void
        +Rollback() void
        +SaveChanges() void
    }

    class UnitOfWork {
        +BeginTransaction() void
        +Commit() void
        +Rollback() void
        +SaveChanges() void
    }

    IUnitOfWork <|-- UnitOfWork
    FeeApplyService --> IUnitOfWork
```

### 4. 策略模式 (Strategy Pattern)
```mermaid
classDiagram
    class IApprovalStrategy {
        <<interface>>
        +GetApprovers(entity: BaseEntity) List
        +ValidateApproval(approver: string, decision: string) ValidationResult
    }

    class DepartmentApprovalStrategy {
        +GetApprovers(entity: BaseEntity) List
        +ValidateApproval(approver: string, decision: string) ValidationResult
    }

    class FinanceApprovalStrategy {
        +GetApprovers(entity: BaseEntity) List
        +ValidateApproval(approver: string, decision: string) ValidationResult
    }

    IApprovalStrategy <|-- DepartmentApprovalStrategy
    IApprovalStrategy <|-- FinanceApprovalStrategy
```

## 核心业务流程

### 1. 报销申请流程
1. 员工创建报销申请(`FeeApplyService.CreateApplication`)
2. 添加费用明细和附件
3. 提交审批(`FeeApplyService.SubmitForApproval`)
4. 多级审批处理(`FeeApplyService.ApproveApplication`)
5. 财务确认支付
6. 流程完成归档

### 2. 出差管理流程
1. 制定出差计划(`EvectionService.CreateEvectionPlan`)
2. 提交审批(`EvectionService.SubmitForApproval`)
3. 审批通过开始出差
4. 记录出差详情(`EvectionService.RecordEvectionDetails`)
5. 完成出差并关联报销

### 3. 预支管理流程
1. 申请预支资金(`AdvanceService.CreateAdvanceRequest`)
2. 审批预支申请(`AdvanceService.ApproveAdvance`)
3. 发放预支资金(`AdvanceService.DisburseAdvance`)
4. 分期归还预支(`AdvanceService.RecordRepayment`)
5. 账务平衡确认

### 4. 用车管理流程
1. 申请使用车辆(`CompanyCarService.CreateCarApplication`)
2. 检查车辆可用性(`CompanyCarService.GetAvailableCars`)
3. 审批用车申请(`CompanyCarService.ApproveCarUsage`)
4. 分配车辆(`CompanyCarService.AssignVehicle`)
5. 记录使用情况(`CompanyCarService.RecordCarUsage`)

## 系统集成

### 工作流集成
所有业务流程都集成了工作流引擎，支持：
- 多级审批流程配置
- 审批节点自定义
- 审批历史追踪
- 超时提醒机制

### 通知系统集成
支持多种通知方式：
- 系统内消息通知
- 邮件通知
- 短信通知
- 微信推送

### 权限系统集成
基于角色的权限控制：
- 申请人权限：创建、查看自己的申请
- 审批人权限：审批指定类型的申请
- 管理员权限：查看所有申请和统计报表

## 扩展性设计

### 1. 插件化扩展
- 支持自定义审批策略
- 支持扩展通知渠道
- 支持业务规则配置

### 2. 多租户支持
- 基于UnitManageID的数据隔离
- 独立的配置管理
- 灵活的权限控制

### 3. API接口支持
- RESTful API设计
- 支持移动端调用
- 第三方系统集成接口

## 总结

行政OA管理模块采用面向对象设计，通过合理的分层架构和设计模式应用，实现了：

1. **高内聚低耦合**: 各业务模块独立，接口清晰
2. **可扩展性**: 支持插件化扩展和配置化管理
3. **可维护性**: 代码结构清晰，便于维护和升级
4. **可测试性**: 接口分离，便于单元测试和集成测试
5. **高性能**: 合理的缓存策略和数据库优化
6. **安全性**: 完善的权限控制和数据保护机制

整个设计既满足了当前的业务需求，又为未来的功能扩展和系统集成奠定了坚实的基础。 