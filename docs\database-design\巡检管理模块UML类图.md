# 巡检管理模块UML类图

## 类结构说明

### 核心业务实体类

#### UTEQInspectPlan (巡检计划类)
**职责**: 管理巡检计划的制定、调度和状态控制
**主要属性**: 
- ID, UnitID, SystemType, PlanName, PlanType
- PlanStartDate, PlanEndDate, WriteYear, PlanStatus
- CurrentDateS, CurrentDateE, WriteUser

**主要方法**:
- 创建计划、修改计划、激活计划、取消计划
- 生成巡检任务、查询计划状态

#### UTEQInspectTaskDetial (巡检任务详情类)
**职责**: 管理具体设备的巡检任务执行
**主要属性**:
- ID, UnitID, InspectPlanID, EQID
- CycleInt, CycleUnit, StartDate, EndDate
- InspectUser, InspectDate, Status, ISOK

**主要方法**:
- 分配任务、执行巡检、更新状态
- 记录巡检结果、生成问题单

#### UTEQInspectTaskDetialInfo (巡检内容详情类)
**职责**: 记录巡检过程中的具体检查内容和问题
**主要属性**:
- ID, InspectTaskDetialID, CheckInfoID
- CheckInfo, BadDesc, IsNeedRepair, IsNeedZG
- Status, ZGUserID, ZGDate, FJTime

**主要方法**:
- 记录检查结果、创建问题记录
- 分派整改任务、执行复检

#### UTBaseCheckInfo (检查项配置类)
**职责**: 定义和管理各类设备的检查标准
**主要属性**:
- ID, UnitID, EQClassID, CheckItem
- CheckMethod, CheckStandard, OrderIndex

**主要方法**:
- 配置检查项、更新检查标准
- 关联设备分类、排序管理

### 基础支撑类

#### UTEQEquipment (设备信息类)
**职责**: 管理物业项目中的设备基本信息
**主要属性**:
- ID, UnitID, BuildID, EQClassID
- EQCode, EQName, Manager, EQStatus
- IsNeedInspection, InstallSite

**主要方法**:
- 设备注册、信息更新、状态管理
- 巡检计划关联、责任人分配

#### UTBaseEQClass (设备分类类)
**职责**: 设备分类管理和检查周期配置
**主要属性**:
- ID, ClassCode, ClassName, PClassID
- ClassLevel, IsLast, IsDayCheck, IsWeekCheck
- IsMonthCheck, IsQuarterCheck, IsHalfYearCheck

**主要方法**:
- 分类管理、层级维护
- 检查周期配置、分类查询

## UML类图

```mermaid
classDiagram
    class UTBaseUnitManage {
        +Guid ID
        +Guid? PID
        +string UnitCode
        +string UnitName
        +int UnitLevel
        +string Manager
        +string Tel
        +string UnitAddress
        +DateTime CreateDate
        +int64 CreateUser
        +GetSubUnits() List~UTBaseUnitManage~
        +GetEquipments() List~UTEQEquipment~
        +CreateInspectPlan() UTEQInspectPlan
        +GetCheckInfos() List~UTBaseCheckInfo~
    }
    
    class UTUnitBuild {
        +Guid ID
        +Guid UnitID
        +string BuildName
        +int BuildType
        +string Contacts
        +decimal BuildArea
        +int UpFloor
        +int DownFloor
        +GetEquipments() List~UTEQEquipment~
        +GetBuildInfo() BuildInfo
    }
    
    class UTBaseEQClass {
        +Guid ID
        +Guid? EQSystemID
        +Guid? PClassID
        +string ClassCode
        +string ClassName
        +int ClassLevel
        +int IsLast
        +int IsDayCheck
        +int IsWeekCheck
        +int IsMonthCheck
        +int IsQuarterCheck
        +int IsHalfYearCheck
        +int IsYearCheck
        +int OrderIndex
        +GetSubClasses() List~UTBaseEQClass~
        +GetEquipments() List~UTEQEquipment~
        +GetCheckInfos() List~UTBaseCheckInfo~
        +ConfigInspectCycle() void
    }
    
    class UTEQEquipment {
        +Guid ID
        +Guid UnitID
        +Guid? BuildID
        +Guid? EQClassID
        +string EQCode
        +string EQName
        +string Manager
        +decimal Count
        +int EQStatus
        +int IsNeedInspection
        +string InstallSite
        +DateTime InstallDate
        +string Brand
        +string EQModel
        +GetInspectTasks() List~UTEQInspectTaskDetial~
        +CreateInspectTask() UTEQInspectTaskDetial
        +UpdateStatus() void
        +GetCheckHistory() List~InspectHistory~
    }
    
    class UTBaseCheckInfo {
        +Guid ID
        +Guid UnitID
        +Guid? EQClassID
        +string CheckItem
        +string CheckMethod
        +string CheckStandard
        +int OrderIndex
        +string Remark
        +GetByEQClass() List~UTBaseCheckInfo~
        +CreateCheckItem() void
        +UpdateStandard() void
        +ValidateMethod() bool
    }
    
    class UTEQInspectPlan {
        +Guid ID
        +Guid UnitID
        +int SystemType
        +string PlanName
        +int PlanType
        +DateTime PlanStartDate
        +DateTime PlanEndDate
        +string WriteYear
        +DateTime WriteDate
        +int PlanStatus
        +DateTime CurrentDateS
        +DateTime CurrentDateE
        +int64 WriteUser
        +DateTime CreateDate
        +int64 CreateUser
        +CreatePlan() void
        +GenerateTasks() List~UTEQInspectTaskDetial~
        +ActivatePlan() void
        +CancelPlan() void
        +GetTasks() List~UTEQInspectTaskDetial~
        +UpdateStatus() void
    }
    
    class UTEQInspectTaskDetial {
        +Guid ID
        +Guid UnitID
        +Guid? InspectPlanID
        +Guid? InspectPlanDetialID
        +Guid? EQID
        +int CycleInt
        +int CycleUnit
        +int CycleCount
        +int IndexOrder
        +DateTime StartDate
        +DateTime EndDate
        +string NeedInspectUser
        +int64 InspectUser
        +DateTime InspectDate
        +int ISOK
        +int Status
        +AssignTask() void
        +ExecuteInspect() void
        +RecordResult() void
        +GetInspectInfos() List~UTEQInspectTaskDetialInfo~
        +UpdateStatus() void
        +CreateProblem() UTEQInspectTaskDetialInfo
    }
    
    class UTEQInspectTaskDetialInfo {
        +Guid ID
        +Guid? InspectTaskDetialID
        +Guid? CheckInfoID
        +string CheckInfo
        +string CheckMethod
        +string BadDesc
        +int OrderInt
        +int IsFJ
        +DateTime FJTime
        +string FJDesc
        +int64 FJUser
        +int IsNeedRepair
        +int64 IsNeedZG
        +int Status
        +string ZGUserID
        +string ZGUserName
        +DateTime ZGDate
        +RecordProblem() void
        +AssignRectification() void
        +ExecuteRectification() void
        +ReInspect() void
        +GetInspectTask() UTEQInspectTaskDetial
        +GetCheckInfo() UTBaseCheckInfo
    }
    
    %% 继承关系
    UTEQInspectPlan --|> BaseEntity : 继承基础实体
    UTEQInspectTaskDetial --|> BaseEntity : 继承基础实体
    UTEQInspectTaskDetialInfo --|> BaseEntity : 继承基础实体
    UTEQEquipment --|> BaseEntity : 继承基础实体
    UTBaseCheckInfo --|> BaseEntity : 继承基础实体
    UTBaseEQClass --|> BaseEntity : 继承基础实体
    UTBaseUnitManage --|> BaseEntity : 继承基础实体
    UTUnitBuild --|> BaseEntity : 继承基础实体
    
    %% 组合关系
    UTBaseUnitManage "1" *-- "0..*" UTBaseUnitManage : 项目层级
    UTBaseUnitManage "1" *-- "0..*" UTUnitBuild : 包含建筑
    UTBaseUnitManage "1" *-- "0..*" UTEQEquipment : 管理设备
    UTBaseUnitManage "1" *-- "0..*" UTEQInspectPlan : 制定计划
    UTBaseUnitManage "1" *-- "0..*" UTBaseCheckInfo : 配置检查项
    
    UTUnitBuild "1" *-- "0..*" UTEQEquipment : 包含设备
    
    UTBaseEQClass "1" *-- "0..*" UTBaseEQClass : 分类层级
    UTBaseEQClass "1" *-- "0..*" UTEQEquipment : 设备分类
    UTBaseEQClass "1" *-- "0..*" UTBaseCheckInfo : 检查项分类
    
    %% 关联关系
    UTEQInspectPlan "1" o-- "0..*" UTEQInspectTaskDetial : 生成任务
    UTEQEquipment "1" o-- "0..*" UTEQInspectTaskDetial : 产生巡检任务
    UTEQInspectTaskDetial "1" o-- "0..*" UTEQInspectTaskDetialInfo : 包含检查内容
    UTBaseCheckInfo "1" o-- "0..*" UTEQInspectTaskDetialInfo : 检查依据
    
    %% 依赖关系
    UTEQInspectTaskDetialInfo ..> UTEQMaintain : 巡检发现问题
```

## 业务逻辑设计

### 主要业务流程类交互

#### 1. 巡检计划制定流程
```mermaid
sequenceDiagram
    participant PM as PlanManager
    participant Plan as UTEQInspectPlan
    participant Unit as UTBaseUnitManage
    participant EQClass as UTBaseEQClass
    participant CheckInfo as UTBaseCheckInfo
    
    PM->>Plan: 创建巡检计划
    Plan->>Unit: 获取项目信息
    Plan->>EQClass: 获取设备分类
    Plan->>CheckInfo: 配置检查项
    Plan->>Plan: 保存计划配置
```

#### 2. 巡检任务执行流程
```mermaid
sequenceDiagram
    participant Inspector as 巡检员
    participant Task as UTEQInspectTaskDetial
    participant Equipment as UTEQEquipment
    participant TaskInfo as UTEQInspectTaskDetialInfo
    participant CheckInfo as UTBaseCheckInfo
    
    Inspector->>Task: 开始巡检任务
    Task->>Equipment: 获取设备信息
    Task->>CheckInfo: 获取检查项
    Inspector->>TaskInfo: 记录检查结果
    TaskInfo->>Task: 更新任务状态
    Task->>Task: 完成巡检
```

#### 3. 问题整改流程
```mermaid
sequenceDiagram
    participant TaskInfo as UTEQInspectTaskDetialInfo
    participant Rectifier as 整改人员
    participant Inspector as 复检人员
    
    TaskInfo->>TaskInfo: 发现问题
    TaskInfo->>Rectifier: 分派整改任务
    Rectifier->>TaskInfo: 执行整改
    TaskInfo->>Inspector: 申请复检
    Inspector->>TaskInfo: 执行复检
    TaskInfo->>TaskInfo: 关闭问题
```

### 设计模式应用

#### 1. 策略模式 - 巡检周期策略
不同类型设备采用不同的巡检周期策略：
- 日检策略、周检策略、月检策略等
- 通过UTBaseEQClass的周期配置字段实现

#### 2. 状态模式 - 任务状态管理
UTEQInspectTaskDetial的状态流转：
- 待执行 → 执行中 → 已完成 → 已确认

#### 3. 观察者模式 - 问题通知
UTEQInspectTaskDetialInfo问题状态变化时：
- 通知相关责任人
- 触发整改流程
- 更新统计数据

### 业务规则约束

#### 1. 数据完整性约束
- 设备必须属于某个项目和建筑
- 巡检任务必须关联有效的设备和计划
- 问题记录必须关联具体的检查项

#### 2. 业务逻辑约束
- 只有激活状态的计划才能生成任务
- 设备状态为"停用"时不生成巡检任务
- 整改完成后必须经过复检才能关闭

#### 3. 权限控制约束
- 基于项目层级的数据访问权限
- 巡检人员只能操作分配给自己的任务
- 管理员可以查看所有项目的巡检数据

## 关键算法设计

### 1. 巡检任务自动生成算法
```
算法：GenerateInspectTasks
输入：巡检计划(plan), 设备列表(equipments)
输出：任务列表(tasks)

For each equipment in equipments:
    If equipment.IsNeedInspection == true:
        taskCount = 计算周期内任务次数
        For i = 1 to taskCount:
            task = 创建巡检任务
            task.StartDate = 计算开始时间
            task.EndDate = 计算结束时间
            task.EQID = equipment.ID
            tasks.Add(task)
Return tasks
```

### 2. 巡检统计分析算法
```
算法：CalculateInspectStatistics
输入：时间范围(dateRange), 项目ID(unitId)
输出：统计结果(statistics)

totalTasks = 查询总任务数
completedTasks = 查询已完成任务数
problemTasks = 查询有问题任务数
rectificationTasks = 查询整改任务数

statistics.CompletionRate = completedTasks / totalTasks
statistics.ProblemRate = problemTasks / completedTasks
statistics.RectificationRate = 已完成整改数 / rectificationTasks

Return statistics
```

## 扩展性设计

1. **支持移动端巡检**: 通过RESTful API支持移动应用
2. **集成IoT设备**: 支持物联网设备自动巡检数据采集
3. **AI辅助诊断**: 集成图像识别和智能诊断功能
4. **多租户支持**: 支持多个物业公司共用系统
5. **报表定制**: 支持用户自定义巡检报表模板

该UML设计完整支持巡检管理模块的所有业务需求，提供了清晰的类职责划分和灵活的扩展机制。 