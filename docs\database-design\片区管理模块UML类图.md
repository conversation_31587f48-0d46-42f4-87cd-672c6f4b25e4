# 片区管理模块UML类图

## 类结构说明

### 片区管理模块架构
- **实体层 (Entity)**: 数据表实体类，对应数据库表结构
- **业务层 (Biz)**: 业务逻辑处理类，封装具体业务规则
- **数据访问层 (Dal)**: 数据访问类，处理数据库操作
- **控制器层 (Controller)**: MVC控制器，处理Web请求
- **模型层 (Model)**: 视图模型，封装页面数据

### 核心业务实体

#### UTBaseUnitManage (项目基本信息)
- 管理项目的基础信息和配置
- 支持项目层级结构管理
- 提供项目权限控制基础

#### UTUnitBuild (建筑物管理)
- 管理项目下的建筑物信息
- 支持不同类型建筑物的分类管理
- 为设备提供安装位置基础

#### UTEQEquipment (设备设施管理)
- 管理项目下所有设备设施
- 支持设备全生命周期管理
- 关联建筑物和设备分类

## UML类关系图

```mermaid
classDiagram
    class UTBaseUnitManage {
        +Guid ID
        +Guid? PID
        +String UnitCode
        +String UnitName
        +Int32? UnitLevel
        +String CompanyName
        +String UnitAddress
        +Decimal? UnitArea
        +String Manager
        +String Tel
        +IsProject() Boolean
        +GetChildUnits() List~UTBaseUnitManage~
        +GetBuildings() List~UTUnitBuild~
        +GetOrganizations() List~UTSYSOrganization~
    }
    
    class UTSYSOrganization {
        +Int64 OrgID
        +String OrgName
        +Int32? OrgType
        +String OrgNumber
        +Int64? OrgParentID
        +String ChargePerson
        +Guid? UnitID
        +Int32? MaintenanceNY
        +GetParentOrg() UTSYSOrganization
        +GetChildOrgs() List~UTSYSOrganization~
        +GetOrgUsers() List~UTSYSUser~
        +IsMaintenanceOrg() Boolean
    }
    
    class UTUnitBuild {
        +Guid ID
        +Guid? UnitID
        +Int32? BuildType
        +String BuildName
        +String Contacts
        +String ContactsTel
        +Decimal? BuildArea
        +Decimal? UsedArea
        +Int32? UpFloor
        +Int32? DownFloor
        +Int32? BuildLevel
        +GetEquipments() List~UTEQEquipment~
        +GetBuildTypeDescription() String
        +GetTotalArea() Decimal
        +GetFloorCount() Int32
    }
    
    class UTEQEquipment {
        +Guid ID
        +Guid? UnitID
        +Guid? BuildID
        +Guid? EQClassID
        +String EQCode
        +String EQName
        +String Manager
        +Int32? EQStatus
        +String InstallSite
        +String EQModel
        +DateTime? InstallDate
        +DateTime? WarrantyDate
        +String Brand
        +Decimal? Amount
        +GetStatusDescription() String
        +IsUnderWarranty() Boolean
        +GetMaintenanceHistory() List~UTEQMaintain~
        +GetInspectionTasks() List~UTEQInspectTask~
    }
    
    class UTWLItemClass {
        +Guid ID
        +String ClassCode
        +String ClassName
        +Guid? PClassID
        +String AllClassName
        +Int32? IsLast
        +Int32? OrderIndex
        +GetParentClass() UTWLItemClass
        +GetChildClasses() List~UTWLItemClass~
        +GetFullPath() String
        +GetItems() List~UTWLItem~
    }
    
    class UTBaseEQClass {
        +Guid ID
        +String ClassCode
        +String ClassName
        +Guid? PClassID
        +Int32? IsLast
        +Int32? IsDayCheck
        +Int32? IsWeekCheck
        +Int32? IsMonthCheck
        +GetParentClass() UTBaseEQClass
        +GetChildClasses() List~UTBaseEQClass~
        +GetEquipments() List~UTEQEquipment~
        +GetCheckPeriods() List~String~
    }
    
    class BizUTBaseUnitManage {
        <<Service>>
        +GetProjectInfo(Guid unitId) UTBaseUnitManage
        +GetProjectBuildings(Guid unitId) List~UTUnitBuild~
        +GetProjectEquipments(Guid unitId) List~UTEQEquipment~
        +GetProjectOrganizations(Guid unitId) List~UTSYSOrganization~
        +SaveProjectInfo(UTBaseUnitManage entity) Guid
        +ValidateProjectData(UTBaseUnitManage entity) Boolean
    }
    
    class BizUTUnitBuild {
        <<Service>>
        +GetByUnitID(Guid unitId) List~UTUnitBuild~
        +GetBuildingEquipments(Guid buildId) List~UTEQEquipment~
        +SaveOrUpdate(UTUnitBuild entity) Guid
        +DeleteBuilding(Guid buildId) Boolean
        +ValidateBuildingData(UTUnitBuild entity) Boolean
        +GetBuildingStatistics(Guid unitId) BuildingStatistics
    }
    
    class BizUTEQEquipment {
        <<Service>>
        +GetByUnitID(Guid unitId) List~UTEQEquipment~
        +GetByBuildID(Guid buildId) List~UTEQEquipment~
        +GetByClassID(Guid classId) List~UTEQEquipment~
        +SaveOrUpdate(UTEQEquipment entity) Guid
        +DeleteEquipment(Guid eqId) Boolean
        +GetEquipmentStatistics(Guid unitId) EquipmentStatistics
        +GenerateQRCode(Guid eqId) String
    }
    
    class EquipmentController {
        <<Controller>>
        +Index(Int32 systemType) ActionResult
        +Create(Guid? id, Guid? eqclassid) ActionResult
        +Edit(Guid id) ActionResult
        +Delete(Guid id) JsonResult
        +GetEquipmentsByBuild(Guid buildId) JsonResult
        +ExportToExcel(SearchEquipment search) ActionResult
    }
    
    class BuildingController {
        <<Controller>>
        +Index(Guid unitId) ActionResult
        +Create(Guid? unitId) ActionResult
        +Edit(Guid id) ActionResult
        +Delete(Guid id) JsonResult
        +GetBuildingTree(Guid unitId) JsonResult
    }
    
    class ModelEquipmentIndex {
        <<ViewModel>>
        +SearchEntity SearchEquipment
        +GridDataSources List~UVEQEquipment~
        +UTUnitBuildList List~UTUnitBuild~
        +UTBaseEQClassList List~UTBaseEQClass~
        +SystemType Int32
        +UnitID Guid?
        +RetriveData() void
    }
    
    class ModelBuildingIndex {
        <<ViewModel>>
        +SearchEntity SearchBuilding
        +GridDataSources List~UTUnitBuild~
        +UnitID Guid?
        +BuildingStatistics BuildingStatistics
        +RetriveData() void
    }

    %% 聚合关系
    UTBaseUnitManage ||--o{ UTSYSOrganization : "包含部门"
    UTBaseUnitManage ||--o{ UTUnitBuild : "包含建筑物"
    UTUnitBuild ||--o{ UTEQEquipment : "容纳设备"
    UTBaseEQClass ||--o{ UTEQEquipment : "分类设备"
    
    %% 自关联关系
    UTSYSOrganization ||--o{ UTSYSOrganization : "部门层级"
    UTWLItemClass ||--o{ UTWLItemClass : "分类层级"
    UTBaseEQClass ||--o{ UTBaseEQClass : "分类层级"
    
    %% 服务依赖关系
    BizUTBaseUnitManage --> UTBaseUnitManage : "管理"
    BizUTUnitBuild --> UTUnitBuild : "管理"
    BizUTEQEquipment --> UTEQEquipment : "管理"
    BizUTEQEquipment --> UTBaseEQClass : "依赖"
    BizUTEQEquipment --> UTUnitBuild : "依赖"
    
    %% 控制器依赖关系  
    EquipmentController --> BizUTEQEquipment : "使用"
    EquipmentController --> BizUTUnitBuild : "使用"
    EquipmentController --> ModelEquipmentIndex : "使用"
    BuildingController --> BizUTUnitBuild : "使用"
    BuildingController --> ModelBuildingIndex : "使用"
    
    %% 视图模型依赖关系
    ModelEquipmentIndex --> UTEQEquipment : "展示"
    ModelEquipmentIndex --> UTUnitBuild : "依赖"
    ModelBuildingIndex --> UTUnitBuild : "展示"
```

## 类职责说明

### 实体类职责

1. **UTBaseUnitManage**: 项目基本信息管理
   - 存储项目基础数据
   - 提供项目层级关系
   - 支持权限控制基础

2. **UTUnitBuild**: 建筑物信息管理
   - 管理建筑物基本信息
   - 提供设备安装位置
   - 支持建筑物统计分析

3. **UTEQEquipment**: 设备设施管理
   - 管理设备全生命周期
   - 关联建筑物和分类
   - 支持维保和巡检

4. **UTSYSOrganization**: 组织架构管理
   - 管理项目部门结构
   - 支持维修组标识
   - 提供人员组织基础

### 业务服务类职责

1. **BizUTBaseUnitManage**: 项目信息业务逻辑
   - 项目信息的CRUD操作
   - 项目数据验证
   - 项目统计分析

2. **BizUTUnitBuild**: 建筑物业务逻辑
   - 建筑物的CRUD操作
   - 建筑物数据验证
   - 建筑物统计分析

3. **BizUTEQEquipment**: 设备业务逻辑
   - 设备的CRUD操作
   - 设备二维码生成
   - 设备统计分析

### 控制器类职责

1. **EquipmentController**: 设备管理控制器
   - 处理设备相关HTTP请求
   - 调用业务服务
   - 返回JSON或视图结果

2. **BuildingController**: 建筑物管理控制器
   - 处理建筑物相关HTTP请求
   - 提供建筑物树形结构
   - 支持建筑物导出功能

### 设计模式应用

- **MVC模式**: 分离视图、控制器和模型
- **Repository模式**: 数据访问层封装
- **Service模式**: 业务逻辑封装
- **ViewModel模式**: 视图数据封装
- **Composite模式**: 支持层级结构管理 