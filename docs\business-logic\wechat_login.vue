<template>
  <div class="wechat-login-container">
    <h3>微信扫码登录</h3>
    <p>请使用微信扫描下方二维码进行登录</p>
    <div id="wechat-login-qrcode"></div>
  </div>
</template>

<script>
export default {
  name: 'WeChatLogin',
  mounted() {
    // 确保在使用此组件的 HTML 页面中已经引入了微信的 wxLogin.js
    // 例如: <script src="https://res.wx.qq.com/connect/zh_CN/htmledition/js/wxLogin.js"></script>
    if (typeof window.WxLogin !== 'undefined') {
      this.initWxLogin();
    } else {
      console.error('Error: WxLogin.js is not loaded. Please include it in your HTML file.');
    }
  },
  methods: {
    initWxLogin() {
      new window.WxLogin({
        self_redirect: false, // 设置为 false，回调页面会进行跳转
        id: 'wechat-login-qrcode', // 用于显示二维码的容器 ID
        appid: 'wx7b35456f3546076c', // 您的微信开放平台应用的 AppID
        scope: 'snsapi_login', // 授权作用域
        redirect_uri: encodeURIComponent('http://www.ccul.vip/ModuleWechat/WXLogin/WxCallBack'), // 您的回调地址
        state: '1211111', // 用于防止 CSRF 攻击的随机字符串
        style: 'black', // 二维码样式
        href: 'data:text/css;base64,LmltcG93ZXJCb3ggLnFyY29kZSB7d2lkdGg6IDIwMHB4O2hlaWdodDogMjAwcHg7fQ0KLmltcG93ZXJCb3ggLnRpdGxlIHtkaXNwbGF5OiBub25lO30NCi5pbXBvd2VyQm94IC5pbmZvIHt3aWR0aDogMjAwcHg7fQ0KLnN0YXR1c19pY29uIHtkaXNwbGF5OiBub25lfQ0KLmltcG93ZXJCb3ggLnN0YXR1cyB7dGV4dC1hbGlnbjogY2VudGVyO30=' // 自定义样式
      });
    }
  }
};
</script>

<style scoped>
.wechat-login-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  width: 300px;
  margin: 40px auto;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
}

#wechat-login-qrcode {
  width: 200px;
  height: 200px;
}
</style>
