# 物料管理模块ER图

## 表结构说明

### 1. 核心实体表

#### UTWLItem (物料信息表)
- **表名**：UT_WL_Item
- **描述**：存储物料基础信息，是整个物料管理的核心实体
- **主要字段**：
  - `ID` (Guid): 主键，物料唯一标识
  - `UnitID` (Guid): 项目ID，关联项目管理
  - `ItemCode` (String, 50): 物料编号，系统自动生成
  - `ItemName` (String, 50): 物料名称，必填
  - `ItemModel` (String, 50): 物料规格型号
  - `Brand` (String, 50): 品牌信息
  - `ItemClassID` (Guid): 物料分类ID，外键关联
  - `ItemClass` (String, 50): 物料分类名称
  - `Price` (Decimal): 单价
  - `Unit` (String, 50): 计量单位，必填
  - `AlarmQty` (Decimal): 库存下限报警值
  - `MaxAlarmQty` (Decimal): 库存上限报警值
  - `Remark` (String, 50): 备注信息
  - `OldItemCode` (String, 50): 旧物料编号
  - `IsDelete` (Int): 删除标记
  - `CreateDate` (DateTime): 创建时间
  - `CreateUser` (Long): 创建人

#### UTWLItemClass (物料分类表)
- **表名**：UT_WL_ItemClass
- **描述**：物料分类管理，支持树形层级结构
- **主要字段**：
  - `ID` (Guid): 主键，分类唯一标识
  - `ClassCode` (String, 50): 分类编码，必填
  - `ClassName` (String, 50): 分类名称，必填
  - `PClassID` (Guid): 父分类ID，外键自关联
  - `PClassCode` (String, 50): 父分类编码
  - `AllClassName` (String, 200): 完整分类路径名称
  - `IsLast` (Int): 是否叶子节点，必填
  - `OrderIndex` (Int): 排序索引
  - `IsDelete` (Int): 删除标记
  - `Remark` (String, 50): 备注信息

#### UTWLStock (库存表)
- **表名**：UT_WL_Stock
- **描述**：物料库存信息，记录各仓库中物料的实时库存
- **主要字段**：
  - `ID` (Guid): 主键，库存记录唯一标识
  - `WHID` (Guid): 仓库ID，必填，外键关联
  - `ItemID` (Guid): 物料ID，必填，外键关联
  - `TotalQty` (Decimal): 库存数量，必填
  - `Price` (Decimal): 单价
  - `Unit` (String, 50): 单位
  - `ItemCode` (String, 50): 物料编号（冗余字段）
  - `ItemName` (String, 50): 物料名称（冗余字段）
  - `ItemModel` (String, 50): 物料规格（冗余字段）
  - `Brand` (String, 50): 品牌（冗余字段）
  - `WHCode` (String, 50): 仓库编号（冗余字段）
  - `WHName` (String, 150): 仓库名称（冗余字段）
  - `UnitID` (Guid): 项目ID
  - `LastUpdateDate` (DateTime): 最后更新时间

#### UTWLWareHouse (仓库表)
- **表名**：UT_WL_WareHouse
- **描述**：仓库基础信息管理
- **主要字段**：
  - `ID` (Guid): 主键，仓库唯一标识
  - `UnitID` (Guid): 项目ID，必填，外键关联
  - `WHCode` (String, 50): 仓库编码
  - `WHName` (String, 50): 仓库名称，必填
  - `WHDesc` (String, 50): 仓库描述
  - `IsDelete` (Int): 删除标记

## ER图

```mermaid
erDiagram
    UTWLItemClass {
        Guid ID PK "分类ID"
        String ClassCode "分类编码"
        String ClassName "分类名称"
        Guid PClassID FK "父分类ID"
        String PClassCode "父分类编码"
        String AllClassName "完整分类路径"
        Int IsLast "是否叶子节点"
        Int OrderIndex "排序索引"
        Int IsDelete "删除标记"
        String Remark "备注"
    }
    
    UTWLItem {
        Guid ID PK "物料ID"
        Guid UnitID FK "项目ID"
        String ItemCode UK "物料编号"
        String ItemName "物料名称"
        String ItemModel "物料规格"
        String Brand "品牌"
        Guid ItemClassID FK "分类ID"
        String ItemClass "分类名称"
        Decimal Price "单价"
        String Unit "计量单位"
        Decimal AlarmQty "下限报警值"
        Decimal MaxAlarmQty "上限报警值"
        String Remark "备注"
        String OldItemCode "旧编号"
        Int IsDelete "删除标记"
        DateTime CreateDate "创建时间"
        Long CreateUser "创建人"
        DateTime UpdateDate "更新时间"
        Long UpdateUser "更新人"
    }
    
    UTWLWareHouse {
        Guid ID PK "仓库ID"
        Guid UnitID FK "项目ID"
        String WHCode "仓库编码"
        String WHName "仓库名称"
        String WHDesc "仓库描述"
        Int IsDelete "删除标记"
    }
    
    UTWLStock {
        Guid ID PK "库存ID"
        Guid WHID FK "仓库ID"
        Guid ItemID FK "物料ID"
        Decimal TotalQty "库存数量"
        Decimal Price "单价"
        String Unit "单位"
        String ItemCode "物料编号(冗余)"
        String ItemName "物料名称(冗余)"
        String ItemModel "物料规格(冗余)"
        String Brand "品牌(冗余)"
        String WHCode "仓库编码(冗余)"
        String WHName "仓库名称(冗余)"
        Guid UnitID FK "项目ID"
        DateTime LastUpdateDate "最后更新时间"
    }
    
    UTBaseUnitManage {
        Guid ID PK "项目ID"
        String UnitName "项目名称"
        String UnitCode "项目编码"
    }
    
    %% 关系定义
    UTWLItemClass ||--o{ UTWLItemClass : "父子分类关系"
    UTWLItemClass ||--o{ UTWLItem : "分类包含物料"
    UTBaseUnitManage ||--o{ UTWLItem : "项目管理物料"
    UTBaseUnitManage ||--o{ UTWLWareHouse : "项目管理仓库"
    UTWLWareHouse ||--o{ UTWLStock : "仓库包含库存"
    UTWLItem ||--o{ UTWLStock : "物料对应库存"
    UTBaseUnitManage ||--o{ UTWLStock : "项目库存管理"
```

## 业务关系分析

### 1. 物料分类体系
- **树形结构**：UTWLItemClass支持多级分类树，通过PClassID实现父子关系
- **分类路径**：AllClassName记录完整分类路径，便于快速查询和显示
- **叶子节点**：IsLast标识是否为最末级分类，只有叶子节点可以关联物料
- **编码规则**：分类编码支持层级编码，便于系统管理和排序

### 2. 物料基础管理
- **编码生成**：物料编号采用"项目编码-分类编码-序号"的规则自动生成
- **分类关联**：每个物料必须归属于某个叶子级分类
- **报警机制**：支持库存上下限报警，AlarmQty和MaxAlarmQty设置报警阈值
- **多项目支持**：通过UnitID实现多项目隔离管理

### 3. 仓库库存体系
- **多仓库管理**：支持项目下多个仓库的独立管理
- **实时库存**：UTWLStock记录物料在各仓库的实时库存数量
- **冗余设计**：库存表保存物料和仓库的关键信息，提高查询性能
- **价格管理**：支持按仓库记录物料的不同进价

### 4. 主要业务流程

#### 物料目录管理流程
```
分类创建 → 物料录入 → 编码生成 → 报警设置 → 启用管理
```

#### 库存更新流程
```
入库操作 → 库存增加 → 价格更新 → 报警检查 → 记录更新
出库操作 → 库存减少 → 余量检查 → 报警提醒 → 记录更新
```

#### 物料查询流程
```
条件筛选 → 分类过滤 → 关键字匹配 → 库存关联 → 结果展示
```

## 关键特性

### 1. 分类管理特性
- **无限级分类**：支持任意深度的分类树结构
- **分类编码**：自动生成层级编码，便于管理
- **动态分类**：支持分类的新增、修改、删除操作
- **分类路径**：自动维护完整分类路径信息

### 2. 物料管理特性
- **唯一编码**：系统自动生成唯一物料编码
- **多维属性**：支持规格、品牌、单位等多种属性
- **历史兼容**：支持旧编号映射，便于系统迁移
- **批量管理**：支持物料信息的批量导入导出

### 3. 库存管理特性
- **实时监控**：实时跟踪物料库存变化
- **多仓库支持**：同一物料可在多个仓库分别管理库存
- **报警机制**：支持库存上下限自动报警
- **历史追踪**：记录库存变更的时间信息

### 4. 系统集成特性
- **多项目隔离**：支持集团-分公司-项目的多层级管理
- **权限控制**：基于职位和用户的操作权限管理
- **数据导入**：支持Excel批量导入物料信息
- **移动端支持**：支持移动端物料查询和管理

## 数据完整性约束

### 1. 主键约束
- 所有表均采用Guid类型主键，确保全局唯一性
- 支持分布式环境下的数据一致性

### 2. 外键约束
- UTWLItem.ItemClassID → UTWLItemClass.ID
- UTWLItem.UnitID → UTBaseUnitManage.ID
- UTWLStock.ItemID → UTWLItem.ID
- UTWLStock.WHID → UTWLWareHouse.ID
- UTWLWareHouse.UnitID → UTBaseUnitManage.ID

### 3. 业务约束
- 物料编码在项目内唯一
- 仓库名称在项目内唯一
- 只有叶子级分类可以关联物料
- 库存数量不能为负数
- 报警阈值上限不能小于下限

### 4. 数据同步约束
- 库存表中的冗余字段需与主表保持同步
- 物料分类变更时需同步更新物料表中的分类信息
- 物料信息变更时需同步更新库存表中的冗余信息 