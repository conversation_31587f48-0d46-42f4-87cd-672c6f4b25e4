# 设备设施管理模块ER图

## 表结构说明

### 核心实体表

#### UTEQEquipment (设备信息表)
- **主键**: ID (Guid)
- **外键**: UnitID (所属园区, Guid?), BuildID (区域ID, Guid?), EQClassID (设备类型, Guid?)
- **核心字段**: EQCode, EQName, Manager, EQStatus, EQType, IsNeedInspection, InstallSite

#### UTBaseEQClass (设备分类表)
- **主键**: ID (Guid)
- **外键**: EQSystemID (所属系统, Guid?), PClassID (上级分类, Guid?)
- **核心字段**: ClassCode, ClassName, ClassLevel, IsLast, OrderIndex

#### UTBaseEQSystem (设备系统表)
- **主键**: ID (Guid)
- **核心字段**: SystemCode, SystemName, SystemType, IsFire, IsSpecial, OrderIndex

#### UTEQInspectTaskDetial (巡检任务详情表)
- **主键**: ID (Guid)
- **外键**: UnitID (项目ID, Guid?), InspectPlanID (计划ID, Guid?), EQID (设备ID, Guid?)
- **核心字段**: CycleInt, CycleUnit, StartDate, EndDate, InspectUser, InspectDate, Status

#### UTEQInspectTaskDetialInfo (巡检任务详情内容表)
- **主键**: ID (Guid)
- **外键**: InspectTaskDetialID (任务详情ID, Guid?), CheckInfoID (检查项ID, Guid?)
- **核心字段**: CheckInfo, CheckMethod, BadDesc, IsNeedRepair, IsNeedZG, Status

#### UTEQMaintain (维修单表)
- **主键**: ID (Guid)
- **外键**: UnitID (园区ID, Guid?), CustomerUserID (客户人员ID, Guid?), EqID (设备ID, Guid?)
- **核心字段**: MaintainCode, FromType, BaseType, BadDesc, BadSite, Status, IsUrgent

#### UTEQMaintainDetial (维修单处理表)
- **主键**: ID (Guid)
- **外键**: MaintainID (维修单ID, Guid?)
- **核心字段**: DetialType, BeginDeal, EndDeal, DealTime, DealUserID, DealUserName

#### UTBaseCheckInfo (设备检查项表)
- **主键**: ID (Guid)
- **外键**: UnitID (单位ID, Guid?), EQClassID (分类ID, Guid?)
- **核心字段**: CheckItem, CheckMethod, CheckStandard, OrderIndex

### 基础支撑表

#### UTBaseUnitManage (项目管理表)
- **主键**: ID (Guid)
- **外键**: PID (父级ID, Guid?)
- **核心字段**: UnitCode, UnitName, UnitLevel, Manager, Tel, UnitAddress

#### UTUnitBuild (建筑物信息表)
- **主键**: ID (Guid)
- **外键**: UnitID (项目ID, Guid?)
- **核心字段**: BuildName, BuildType, BuildArea, UpFloor, DownFloor, Contacts

## ER关系图

```mermaid
erDiagram
    UTBaseUnitManage {
        Guid ID PK
        Guid PID FK "父级项目ID"
        string UnitCode "项目编码"
        string UnitName "项目名称"
        int UnitLevel "项目级别"
        string Manager "负责人"
        string Tel "联系电话"
        string UnitAddress "项目地址"
        decimal UnitArea "项目面积"
        DateTime CreateDate "创建时间"
        int64 CreateUser "创建人"
    }
    
    UTUnitBuild {
        Guid ID PK
        Guid UnitID FK "项目ID"
        int BuildType "建筑类型"
        string BuildName "建筑名称"
        string Contacts "联系人"
        string ContactsTel "联系电话"
        decimal BuildArea "建筑面积"
        decimal UsedArea "使用面积"
        int UpFloor "地上层数"
        int DownFloor "地下层数"
        int BuildLevel "建筑物期数"
    }
    
    UTBaseEQSystem {
        Guid ID PK
        string SystemCode "系统编号"
        string SystemName "系统名称"
        int SystemType "系统类型"
        int IsFire "是否消防范围"
        int IsSpecial "是否特种设备"
        string Remark "备注"
        int OrderIndex "排序"
    }
    
    UTBaseEQClass {
        Guid ID PK
        Guid EQSystemID FK "所属系统"
        int ClassLevel "分类级别"
        Guid PClassID FK "上级分类"
        string ClassCode "分类编号"
        string ClassName "分类名称"
        int IsLast "是否最终类型"
        int OrderIndex "排序"
        int IsDayCheck "是否日检"
        int IsWeekCheck "是否周检"
        int IsMonthCheck "是否月检"
        int IsQuarterCheck "是否季检"
        int IsHalfYearCheck "是否半年检"
        int IsYearCheck "是否年检"
    }
    
    UTEQEquipment {
        Guid ID PK
        Guid UnitID FK "所属园区"
        Guid BuildID FK "区域ID"
        Guid EQClassID FK "设备类型"
        string EQCode "设备编号"
        string EQName "设备名称"
        string Manager "责任人"
        decimal Count "数量"
        string CountUnit "单位"
        int EQStatus "状态"
        string EQType "类型"
        int IsNeedInspection "是否需要巡检"
        string InstallSite "安装位置"
        DateTime InstallDate "安装日期"
        DateTime WarrantyDate "质保日期"
        string Brand "品牌"
        string EQModel "型号"
        decimal Amount "金额"
    }
    
    UTBaseCheckInfo {
        Guid ID PK
        Guid UnitID FK "单位ID"
        Guid EQClassID FK "分类ID"
        string CheckItem "检查项"
        string CheckMethod "检查方法"
        string CheckStandard "检查标准"
        int OrderIndex "排序"
        string Remark "备注"
    }
    
    UTEQInspectTaskDetial {
        Guid ID PK
        Guid UnitID FK "项目ID"
        Guid InspectPlanID FK "计划ID"
        Guid InspectPlanDetialID FK "计划详情ID"
        Guid EQID FK "设备ID"
        int CycleInt "周期"
        int CycleUnit "周期单位"
        int CycleCount "总次数"
        int IndexOrder "次数"
        DateTime StartDate "开始时间"
        DateTime EndDate "结束时间"
        string NeedInspectUser "该巡检人"
        int64 InspectUser "实际巡检人"
        DateTime InspectDate "巡检时间"
        int ISOK "是否完好"
        int Status "状态"
    }
    
    UTEQInspectTaskDetialInfo {
        Guid ID PK
        Guid InspectTaskDetialID FK "任务详情ID"
        Guid CheckInfoID FK "检查项ID"
        string CheckInfo "巡检项目"
        string CheckMethod "内容"
        string BadDesc "问题描述"
        int OrderInt "顺序"
        int IsFJ "是否已复检OK"
        DateTime FJTime "复检时间"
        string FJDesc "复检描述"
        int64 FJUser "复检人"
        int IsNeedRepair "是否需要维修"
        int64 IsNeedZG "是否需要整改"
        int Status "状态"
    }
    
    UTEQMaintain {
        Guid ID PK
        Guid UnitID FK "园区ID"
        Guid CustomerUserID FK "客户人员ID"
        int64 UserID "内部人员ID"
        string MaintainCode "维修单编号"
        int EQSystemType "设备大类"
        int FromType "维修单来源"
        int BaseType "报修类型"
        string BadDesc "故障描述"
        string BadSite "故障位置"
        Guid EqID FK "设备ID"
        Guid BuildID FK "建筑ID"
        string SendUser "发送人"
        DateTime SendTime "发送时间"
        int IsUrgent "是否紧急"
        int Status "状态"
        DateTime CreateDate "创建时间"
    }
    
    UTEQMaintainDetial {
        Guid ID PK
        Guid MaintainID FK "维修单ID"
        int DetialType "明细类型"
        string BeginDeal "处理前说明"
        string EndDeal "处理后说明"
        DateTime DealTime "处理时间"
        int64 DealUserID "处理人"
        string DealUserName "处理人姓名"
        string Remark "备注"
    }

    %% 关系定义
    UTBaseUnitManage ||--|| UTBaseUnitManage : "项目层级关系"
    UTBaseUnitManage ||--o{ UTUnitBuild : "包含建筑"
    UTBaseUnitManage ||--o{ UTEQEquipment : "管理设备"
    UTBaseUnitManage ||--o{ UTEQInspectTaskDetial : "巡检任务"
    UTBaseUnitManage ||--o{ UTEQMaintain : "维修任务"
    UTBaseUnitManage ||--o{ UTBaseCheckInfo : "检查标准"
    
    UTUnitBuild ||--o{ UTEQEquipment : "安装设备"
    UTUnitBuild ||--o{ UTEQMaintain : "报修位置"
    
    UTBaseEQSystem ||--o{ UTBaseEQClass : "系统分类"
    UTBaseEQClass ||--|| UTBaseEQClass : "分类层级"
    UTBaseEQClass ||--o{ UTEQEquipment : "设备分类"
    UTBaseEQClass ||--o{ UTBaseCheckInfo : "检查标准"
    
    UTEQEquipment ||--o{ UTEQInspectTaskDetial : "巡检对象"
    UTEQEquipment ||--o{ UTEQMaintain : "维修对象"
    
    UTEQInspectTaskDetial ||--o{ UTEQInspectTaskDetialInfo : "巡检内容"
    UTBaseCheckInfo ||--o{ UTEQInspectTaskDetialInfo : "检查依据"
    
    UTEQMaintain ||--o{ UTEQMaintainDetial : "处理过程"
    UTEQInspectTaskDetialInfo ||--o{ UTEQMaintain : "巡检发现问题"
```

## 业务关系说明

### 设备台账管理
- UTEQEquipment为核心，通过EQClassID关联UTBaseEQClass实现设备分类
- UTBaseEQClass通过EQSystemID关联UTBaseEQSystem实现系统分类
- 支持树形分类结构，通过PClassID实现分类层级

### 设备巡检管理
- UTEQInspectTaskDetial记录设备巡检任务
- UTEQInspectTaskDetialInfo记录具体巡检内容和发现的问题
- UTBaseCheckInfo定义各设备分类的标准检查项
- 支持按项目、系统、设备、个人等多维度分析

### 设备报修管理
- UTEQMaintain记录报修单基本信息
- UTEQMaintainDetial记录维修处理过程
- 支持多种报修来源：直接报修、巡检报修、巡更报修、物联报警
- 支持按项目、设备、系统等维度进行报修分析

### 统计分析支持
- 通过设备分类和系统分类实现按系统统计
- 通过项目归属实现按项目统计  
- 通过设备关联实现按设备统计
- 通过人员字段实现按个人统计 