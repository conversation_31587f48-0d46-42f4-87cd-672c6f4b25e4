# 房产管理模块分析

## 模块概述

房产管理模块是项目管理系统的重要组成部分，负责管理项目内的建筑物和房间信息。该模块提供建筑物的创建、编辑、删除以及房间的分配管理等功能，为客户管理、物业收费、设备管理等其他业务模块提供房产基础数据支撑。

## 路由信息

- **子菜单名**: 房产管理 / 建筑物管理
- **路由地址**: ~/ModuleUnit/UnitBuild/UnitManageTree
- **模块名**: ModuleUnit
- **视图名**: UnitBuild

## 页面逻辑分析

### 主要功能

1. **建筑物信息管理**
   - 建筑物基本信息的增删改查
   - 建筑物类型、名称、面积管理
   - 建筑物联系人和联系方式维护
   - 建筑物图片上传和管理

2. **房间信息管理**
   - 房间基本信息维护
   - 房间类型和使用类型设置
   - 房间状态管理（在用、空置）
   - 房间面积和物业费信息

3. **项目树形结构**
   - 多项目支持和项目选择
   - 项目-建筑物层级展示
   - 动态加载的建筑物结构

4. **批量导入功能**
   - Excel模板批量导入建筑物和房间信息
   - 数据验证和错误处理
   - 导入结果反馈

### 控制器分析

#### UnitBuildController (ModuleUnit)

**主要方法**:
- `UnitManageTree()`: 项目建筑物树形结构页面
- `Index()`: 建筑物管理主页面
- `Create()`: 建筑物创建/编辑页面
- `Delete()`: 建筑物删除操作
- `UnitBuildIndex()`: 建筑物管理维护页面

**核心业务逻辑**:
```csharp
// 建筑物创建和保存
[HttpPost]
public ActionResult Create(ModelUnitBuildCreate model, FormCollection collection, HttpPostedFileBase filename)
{
    try
    {
        model.fileName = filename;
        model.Save();
        return Content(WebTools.ScriptCloseDialog(DialogOption.GetDefaultInstance(new DialogOption()
        {
            RefreshOpener = false,
            CallbackFromOpener = "callback",
            CallbackFromOpenerParameter = string.Format("'{0}','{1}','{2}','{3}'", (int)model.PageState, "B" + model.UTUnitBuildEnt.ID, model.UTUnitBuildEnt.BuildName, model.ParentID)
        })));
    }
    catch (Exception ex)
    {
        Error = ex.Message;
        model.RetriveData();
    }
    return View(model);
}

// 建筑物删除操作
[HttpPost]
public ActionResult Delete(Guid? ID)
{
    JsonResult jsResult = new JsonResult();
    jsResult.ContentType = Consts.CONTENT_TYPE;
    jsResult.Data = new { result = string.Empty };
    try
    {
        // 检查是否存在关联的房间信息
        IList<UTUnitRoom> UTUnitRoomList = serviceUTUnitRoom.ListBy(ExpressionWrapper<UTUnitRoom>.Where(x => x.BuildID == ID));
        IList<UTUnitBuildArea> UTUnitBuildAreaList = serviceUTUnitBuildArea.ListBy(ExpressionWrapper<UTUnitBuildArea>.Where(x => x.BuildID == ID));
        if (UTUnitRoomList.Count > 0)
        {
            jsResult.Data = new { result = "请先删除房间号！" };
            return jsResult;
        }
        if (UTUnitBuildAreaList.Count > 0)
        {
            jsResult.Data = new { result = "请先删除建筑物区域！" };
            return jsResult;
        }
        serviceUTUnitBuild.DeleteByID(ID);
    }
    catch (Exception ex)
    {
        jsResult.Data = new { result = ex.Message };
    }
    return jsResult;
}
```

### 模型分析

#### ModelUnitBuildCreate
- 负责建筑物创建和编辑功能
- 图片文件上传和处理
- 数据验证和保存操作

**核心保存逻辑**:
```csharp
public void Save()
{
    if (fileName != null)
    {
        UTUnitBuildEnt.BuildPic = UploadHelperC.SaveFile(fileName, "Build");
    }
    
    if (ID != null && ID != Guid.Empty)
    {
        // 编辑模式
        UTUnitBuildEnt.UpdateDate = DateTime.Now;
        UTUnitBuildEnt.UpdateUser = SessionManager.CurrentUserAccount.UserID;
        serviceUTUnitBuild.Update(UTUnitBuildEnt);
    }
    else
    {
        // 新增模式
        UTUnitBuildEnt.CreateDate = DateTime.Now;
        UTUnitBuildEnt.CreateUser = SessionManager.CurrentUserAccount.UserID;
        UTUnitBuildEnt.UnitID = UnitID;
        UTUnitBuildEnt.ID = (Guid)serviceUTUnitBuild.Save(UTUnitBuildEnt);
    }
}
```

#### ModelUnitBuildImport
- 负责批量导入建筑物和房间信息
- Excel文件解析和数据验证
- 导入结果处理和反馈

## 数据表结构

### 主表: UT_Unit_Build (建筑物表)

| 字段名 | 数据类型 | 长度 | 允许空 | 说明 |
|--------|----------|------|--------|------|
| ID | uniqueidentifier | - | NO | 主键ID |
| UnitID | uniqueidentifier | - | YES | 项目ID |
| BuildType | int | - | YES | 建筑类型（住宅，商用，办公楼，综合体） |
| BuildName | nvarchar | 50 | YES | 建筑名称 |
| Contacts | nvarchar | 50 | YES | 联系人 |
| ContactsTel | nvarchar | 50 | YES | 联系电话 |
| BuildArea | decimal | - | YES | 建筑面积 |
| UsedArea | decimal | - | YES | 使用面积 |
| UpFloor | int | - | YES | 地上层数 |
| DownFloor | int | - | YES | 地下层数 |
| BuildLevel | int | - | YES | 建筑等级 |
| CreateDate | datetime | - | YES | 创建时间 |
| CreateUser | bigint | - | YES | 创建用户 |
| UpdateDate | datetime | - | YES | 更新时间 |
| UpdateUser | bigint | - | YES | 更新用户 |
| BuildPic | nvarchar | 255 | YES | 建筑图片 |
| OrderIndex | int | - | YES | 排序索引 |

**建表SQL**:
```sql
create table [dbo].[UT_Unit_Build] (  
    [ID] uniqueidentifier  NOT NULL ,  
    [UnitID] uniqueidentifier  NULL ,  
    [BuildType] int  NULL ,  
    [BuildName] nvarchar(50)  NULL ,  
    [Contacts] nvarchar(50)  NULL ,  
    [ContactsTel] nvarchar(50)  NULL ,  
    [BuildArea] decimal(18,2)  NULL ,  
    [UsedArea] decimal(18,2)  NULL ,  
    [UpFloor] int  NULL ,  
    [DownFloor] int  NULL ,  
    [BuildLevel] int  NULL ,  
    [CreateDate] datetime  NULL ,  
    [CreateUser] bigint  NULL ,  
    [UpdateDate] datetime  NULL ,  
    [UpdateUser] bigint  NULL ,  
    [BuildPic] nvarchar(255)  NULL ,  
    [OrderIndex] int  NULL 
);  
ALTER TABLE [dbo].[UT_Unit_Build] ADD CONSTRAINT PK_UT_Unit_Build PRIMARY KEY  ([ID]);
```

### 关联表: UT_Unit_Room (房间管理表)

| 字段名 | 数据类型 | 长度 | 允许空 | 说明 |
|--------|----------|------|--------|------|
| ID | uniqueidentifier | - | NO | 主键ID |
| BuildID | uniqueidentifier | - | YES | 楼幢ID |
| RoomNo | nvarchar | 50 | NO | 房号 |
| RoomArea | decimal | - | NO | 房屋面积 |
| RoomType | int | - | NO | 房屋类型（业主自有，业主租用） |
| UsedType | int | - | NO | 使用类型（居住，办公，商业） |
| PropertyFeeDate | datetime | - | YES | 物业费缴费到期 |
| RoomStatus | int | - | NO | 房屋状态（0在用，1空置） |
| ElectricMeterNo | nvarchar | 50 | YES | 电表编号 |
| WaterMeterNo | nvarchar | 50 | YES | 水表编号 |
| GasMeterNo | nvarchar | 50 | YES | 燃气编号 |
| PropertyFeePrice | decimal | - | YES | 物业费单价 |
| BuildAreaID | uniqueidentifier | - | YES | 分区ID |
| CreateDate | datetime | - | YES | 创建时间 |
| CreateUser | bigint | - | YES | 创建用户 |

**建表SQL**:
```sql
create table [dbo].[UT_Unit_Room] (  
    [ID] uniqueidentifier  NOT NULL ,  
    [BuildID] uniqueidentifier  NULL ,  
    [RoomNo] nvarchar(50)  NOT NULL ,  
    [RoomArea] decimal(18,2)  NOT NULL ,  
    [RoomType] int  NOT NULL ,  
    [UsedType] int  NOT NULL ,  
    [PropertyFeeDate] datetime  NULL ,  
    [RoomStatus] int  NOT NULL ,  
    [ElectricMeterNo] nvarchar(50)  NULL ,  
    [WaterMeterNo] nvarchar(50)  NULL ,  
    [GasMeterNo] nvarchar(50)  NULL ,  
    [PropertyFeePrice] decimal(18,2)  NULL ,  
    [BuildAreaID] uniqueidentifier  NULL ,  
    [CreateDate] datetime  NULL ,  
    [CreateUser] bigint  NULL 
);  
ALTER TABLE [dbo].[UT_Unit_Room] ADD CONSTRAINT PK_UT_Unit_Room PRIMARY KEY  ([ID]);
```

## 业务流程

### 建筑物创建流程
1. 用户选择项目
2. 填写建筑物基本信息（名称、类型、面积等）
3. 设置联系人和联系方式
4. 上传建筑物图片（可选）
5. 保存建筑物信息到数据库
6. 更新建筑物排序索引

### 建筑物编辑流程
1. 从建筑物列表选择要编辑的建筑物
2. 加载现有建筑物信息
3. 修改建筑物信息或更换图片
4. 验证数据完整性
5. 保存更新后的建筑物信息

### 建筑物删除流程
1. 选择要删除的建筑物
2. 检查建筑物下是否存在房间信息
3. 检查建筑物下是否存在分区信息
4. 如果存在关联数据，提示先删除关联信息
5. 如果无关联数据，执行删除操作

### 房间管理流程
1. 选择建筑物
2. 创建或编辑房间信息
3. 设置房间类型和使用类型
4. 配置表计编号（电表、水表、燃气表）
5. 设置物业费单价
6. 保存房间信息

### 批量导入流程
1. 下载Excel导入模板
2. 按模板格式填写建筑物和房间信息
3. 上传Excel文件
4. 系统验证文件格式和数据完整性
5. 批量创建建筑物和房间记录
6. 返回导入结果报告

## 权限控制

### 功能权限
- 建筑物查看权限
- 建筑物创建权限
- 建筑物编辑权限
- 建筑物删除权限
- 房间管理权限
- 批量导入权限

### 数据权限
- 按项目隔离建筑物数据
- 用户只能管理所属项目的建筑物
- 支持跨项目建筑物查看（特殊权限）

## 数据字典

### 建筑类型 (BuildType)
- 0: 住宅
- 1: 商用
- 2: 办公楼
- 3: 综合体

### 房屋类型 (RoomType)
- 0: 业主自有
- 1: 业主租用

### 使用类型 (UsedType)
- 0: 居住
- 1: 办公
- 2: 商业

### 房屋状态 (RoomStatus)
- 0: 在用
- 1: 空置

## 技术特点

1. **多项目支持**: 支持多项目建筑物数据隔离
2. **图片管理**: 集成建筑物图片上传和管理
3. **层级管理**: 支持项目-建筑物-房间的层级结构
4. **关联检查**: 删除前检查关联数据完整性
5. **批量导入**: 支持Excel批量导入功能
6. **数据验证**: 完整的数据验证和错误处理

## 扩展功能

1. **建筑物模板**: 支持建筑物信息模板化管理
2. **3D展示**: 集成3D建筑物展示功能
3. **建筑物统计**: 提供建筑物使用情况统计分析
4. **房间分配**: 智能房间分配和推荐
5. **表计管理**: 集成表计读数和管理功能
6. **移动端**: 支持移动端建筑物和房间管理

## 关联模块

1. **项目管理**: 建筑物隶属于项目
2. **分区管理**: 建筑物包含多个分区
3. **客户管理**: 房间与客户关联
4. **物业收费**: 房间物业费管理
5. **设备管理**: 设备安装在建筑物内
6. **能耗管理**: 表计数据采集和分析

## 性能优化

1. **索引优化**: 在UnitID、BuildID等字段建立索引
2. **缓存机制**: 建筑物信息缓存
3. **分页加载**: 大量数据分页加载
4. **图片压缩**: 建筑物图片自动压缩
5. **批量操作**: 优化批量导入性能

## 安全控制

1. **文件上传**: 严格的文件类型和大小限制
2. **数据验证**: 多层次数据验证机制
3. **权限验证**: 操作权限验证
4. **SQL注入**: 防止SQL注入攻击
5. **XSS防护**: 防止跨站脚本攻击
