	using System.Collections.Generic; 
	using System.Text; 
	using System;
	using System.ServiceModel;
	using System.ServiceModel.Web;
	using NHibernate;
	using NHibernate.Linq;
	using Project.Common;
	using Project.Dal.Base;
	using Project.Biz.Base;
	using PropertySys_ZGHQ.ProcedureEntity;
	using PropertySys_ZGHQ.ProcedureDal;

	namespace PropertySys_ZGHQ.ProcedureBiz
	{
		public class  BizUPSearchFireAlarmStatistics : BaseSPBiz
		{
			private DalUPSearchFireAlarmStatistics dalUPSearchFireAlarmStatistics;
		
			private  BizUPSearchFireAlarmStatistics()
			{
				dalUPSearchFireAlarmStatistics = DalFactory.Get<DalUPSearchFireAlarmStatistics>();
			}

			[OperationContract]
			[WebInvoke(BodyStyle = WebMessageBodyStyle.Wrapped)]
			public IList<UPSearchFireAlarmStatistics> Invoke(UPSearchFireAlarmStatisticsParameter parameter)
			{
									var result = dalUPSearchFireAlarmStatistics.Invoke(parameter);
					return result;
							}
		}
	}

	