# 满意度调查模块分析

## 模块概述

满意度调查模块是客户客服管理系统中用于开展客户满意度调查活动的专门模块。该模块基于满意度调查模板，创建具体的调查活动，收集客户反馈，并提供详细的满意度分析报告。

## 路由信息

- **子菜单名**: 满意度调查
- **路由地址**: ~/ModuleKF/Satisfaction/Index
- **模块名**: ModuleKF
- **视图名**: Satisfaction

## 页面逻辑分析

### 主要功能

1. **调查活动管理**
   - 基于模板创建调查活动
   - 调查参数设置和配置
   - 调查发布和状态管理

2. **参与者管理**
   - 调查对象选择和邀请
   - 参与状态跟踪
   - 提醒和催办功能

3. **数据收集**
   - 在线问卷填写
   - 答题数据实时收集
   - 数据完整性验证

4. **结果分析**
   - 满意度统计分析
   - 图表展示和报告生成
   - 趋势分析和对比

## 控制器分析

### SatisfactionController (ModuleKF)

**主要方法**:
- `Index()`: 满意度调查列表页面
- `Create()`: 创建调查活动
- `Publish()`: 发布调查
- `Statistics()`: 统计分析页面
- `Report()`: 生成调查报告

## 数据表结构

### 主表: UT_Unit_Satisfaction (满意度调查)

| 字段名 | 数据类型 | 长度 | 允许空 | 说明 |
|--------|----------|------|--------|------|
| ID | uniqueidentifier | - | NO | 主键ID |
| UnitID | uniqueidentifier | - | YES | 单位ID |
| SatisfactionName | nvarchar | 50 | YES | 调查名称 |
| SatisfactionClassID | uniqueidentifier | - | YES | 调查分类ID |
| StartDate | datetime | - | YES | 开始时间 |
| EndDate | datetime | - | YES | 结束时间 |
| Status | int | - | YES | 状态 |
| CreateDate | datetime | - | YES | 创建时间 |
| CreateUser | bigint | - | YES | 创建用户 |
| CustomerType | int | - | YES | 客户类型 |
| IsScore | int | - | YES | 是否评分 |

### 问题表: UT_Unit_SatisfactionQuestion (满意度调查问题)

| 字段名 | 数据类型 | 长度 | 允许空 | 说明 |
|--------|----------|------|--------|------|
| ID | uniqueidentifier | - | NO | 主键ID |
| SatisfactionID | uniqueidentifier | - | YES | 调查ID |
| QestionType | int | - | YES | 问题类型 |
| Question | nvarchar | 500 | YES | 问题内容 |
| AnswerType | int | - | YES | 答案类型 |
| ItemType | int | - | YES | 题目类型 |
| OrderIndex | int | - | YES | 排序索引 |
| Score | decimal | 18,1 | YES | 得分 |

### 答案表: UT_Unit_SatisfactionQuestionAnswer (满意度调查问题答案)

| 字段名 | 数据类型 | 长度 | 允许空 | 说明 |
|--------|----------|------|--------|------|
| ID | uniqueidentifier | - | NO | 主键ID |
| SatisfactionQuestionID | uniqueidentifier | - | YES | 问题ID |
| AnswerTitle | nvarchar | 50 | YES | 答案标题 |
| AnswerValue | nvarchar | MAX | YES | 答案内容 |
| OrderIndex | int | - | YES | 排序索引 |
| Score | decimal | 18,1 | YES | 得分 |

### 结果表: UT_Unit_SatisfactionQuestionAnswerResult (满意度调查问题答案结果)

| 字段名 | 数据类型 | 长度 | 允许空 | 说明 |
|--------|----------|------|--------|------|
| ID | uniqueidentifier | - | NO | 主键ID |
| SatisfactionDetialID | uniqueidentifier | - | YES | 调查详情ID |
| SatisfactionQuestionAnswerID | uniqueidentifier | - | YES | 问题答案ID |
| AnswerValue | nvarchar | MAX | YES | 答案值 |
| CreateDate | datetime | - | YES | 创建时间 |

### 详情表: UT_Unit_SatisfactionDetial (满意度调查详情)

| 字段名 | 数据类型 | 长度 | 允许空 | 说明 |
|--------|----------|------|--------|------|
| ID | uniqueidentifier | - | NO | 主键ID |
| SatisfactionID | uniqueidentifier | - | YES | 调查ID |
| CustomerID | uniqueidentifier | - | YES | 客户ID |
| CreateDate | datetime | - | YES | 参与时间 |
| Score | decimal | 18,1 | YES | 总得分 |
| IsFinish | int | - | YES | 是否完成 |

## 存储过程

### UP_Unit_SatisfactionStaticsDZ
**功能**: 满意度调查统计分析（大众版）

**参数**:
- @ID uniqueidentifier - 调查ID

**主要功能**:
- 统计各问题的答题情况
- 计算满意度得分分布
- 生成详细的统计报告

## 业务流程

### 调查创建流程
1. **选择模板** → 2. **设置参数** → 3. **选择对象** → 4. **发布调查** → 5. **数据收集** → 6. **结果分析**

### 调查状态
- 草稿状态
- 已发布
- 进行中
- 已结束
- 已归档

## 微信集成

### 移动端调查
- 微信端问卷展示
- 移动端友好界面
- 一键分享和邀请
- 实时提交和保存

### 推送通知
- 调查邀请推送
- 参与提醒通知
- 结果反馈推送

## 统计分析

### 参与度分析
- 参与率统计
- 完成率分析
- 参与时间分布
- 用户行为分析

### 满意度分析
- 总体满意度得分
- 各维度满意度对比
- 满意度趋势分析
- 问题热点分析

## 技术特点

1. **模板驱动**: 基于模板快速创建调查
2. **实时统计**: 动态的数据统计和分析
3. **多维分析**: 多角度的满意度分析
4. **移动优先**: 优化的移动端体验
5. **智能推荐**: 基于历史数据的改进建议
