# 报销审核

## 基本信息
- **子菜单名**: 报销审核
- **路由**: ~/ModuleXZ/FeeApply/MainSubmitIndex
- **模块名**: ModuleXZ
- **视图名**: FeeApply
- **功能描述**: 用于审核员工的报销申请，管理费用报销的审批流程

## 页面逻辑分析

### 主要Controller方法
**FeeApplyController.cs**

1. **MainSubmitIndex(ModelFeeApplyIndex model)** - 报销审核主页
   - 功能：显示报销审核主页面
   - 逻辑：返回审核主页视图

2. **SubmitIndex(ModelFeeApplyIndex model)** - 待审核报销列表
   - 功能：显示当前用户需要审核的报销申请
   - 逻辑：调用model.RetriveDataSubmit()获取待审核数据

3. **SubmitCreate(ModelFeeApplyCreate model)** - 报销审核详情
   - 功能：显示报销申请详情供审核
   - 逻辑：调用model.SubmitCreate()初始化审核数据

4. **SubmitCreate(POST)** - 提交审核结果
   - 功能：保存审核意见和结果
   - 逻辑：
     - 调用model.Submit()提交审核结果
     - 返回对话框关闭脚本

5. **FinanceAuditIndex(ModelFeeApplyIndex model)** - 财务审核列表
   - 功能：显示财务部门需要审核的报销申请
   - 逻辑：调用model.RetriveDataFinanceAudit()获取财务审核数据

6. **FinanceAuditCreate(ModelFeeApplyCreate model)** - 财务审核
   - 功能：财务部门审核报销申请
   - 逻辑：调用model.FinanceAuditCreate()初始化财务审核数据

### 主要Model类
**ModelFeeApply.cs**

1. **ModelFeeApplyIndex** - 报销审核列表模型
   - 方法：
     - RetriveDataSubmit()：获取待审核报销申请列表
     - RetriveDataFinanceAudit()：获取财务审核列表

2. **ModelFeeApplyCreate** - 报销审核模型
   - 属性：
     - UTUnitFeeApplyEntity：报销申请实体
     - UTBaseProcessOpDetialEnt：审核操作详情实体
     - UTUnitFeeApplyDetailList：报销明细列表
   - 方法：
     - SubmitCreate()：初始化审核数据
     - Submit()：提交审核结果
     - FinanceAuditCreate()：初始化财务审核数据

## 数据库使用情况

### 主要数据表
1. **UTUnitFeeApply** - 报销申请主表
   - 审核相关字段：
     - Status：当前状态
     - CurrentApprovalUser：当前审批人
     - ProcessCode：流程代码
     - FinanceAuditUser：财务审核人
     - FinanceAuditDate：财务审核时间
   - 操作：状态更新、审批人设置

2. **UTBaseProcessOpDetial** - 审核操作详情表
   - 字段：
     - ProcessCode：流程代码
     - OperateStepCode：操作步骤代码
     - OperateInfo：审核意见
     - ApprovalResult：审核结果
     - OperateUser：审核人
   - 操作：记录审核历史

3. **UTUnitFeeApplyDetail** - 报销明细表
   - 审核相关字段：
     - AuditAmount：审核金额
     - AuditRemark：审核备注
     - IsApproved：是否通过审核
   - 操作：明细审核

### 主要Service接口
1. **IServiceUTUnitFeeApply** - 报销申请服务
   - 审核相关方法：
     - SubmitInfo()：提交审核信息
     - ApprovalOrderPass()：审核通过
     - ApprovalOrderNoPass()：审核不通过
     - FinanceAudit()：财务审核

2. **IServiceUTUnitFeeApplyDetail** - 报销明细服务
   - 方法：明细审核处理

## 业务流程
1. **审核分配**：
   - 根据报销金额确定审核级别
   - 分配相应的审核人员

2. **部门审核**：
   - 部门负责人审核报销合理性
   - 检查费用是否符合规定
   - 验证发票和凭证真实性

3. **财务审核**：
   - 财务部门审核账务处理
   - 检查发票合规性
   - 确认预算和资金可用性

4. **明细审核**：
   - 逐项审核报销明细
   - 可调整审核金额
   - 填写审核意见

5. **最终审批**：
   - 高级管理层最终审批
   - 确定最终报销金额
   - 安排付款

## 审核要点
### 合规性审核
- 费用类型是否符合规定
- 发票是否真实有效
- 报销标准是否符合制度

### 合理性审核
- 费用金额是否合理
- 费用发生是否必要
- 是否有预算支持

### 完整性审核
- 申请信息是否完整
- 附件是否齐全
- 审批流程是否规范

## 审核权限
### 部门审核权限
- 部门负责人审核本部门报销
- 可审核一定金额以下的报销

### 财务审核权限
- 财务人员有专业审核权限
- 负责发票和账务审核

### 高级审批权限
- 高级管理层审批大额报销
- 特殊情况的最终决策

## 审核结果处理
### 全部通过
- 按申请金额全额报销
- 进入付款流程

### 部分通过
- 按审核金额报销
- 说明调整原因

### 不通过
- 退回申请人重新申请
- 说明不通过原因

## 财务集成
1. **预算控制**：与预算系统集成，控制费用支出
2. **账务处理**：自动生成会计凭证
3. **付款管理**：与付款系统集成
4. **成本核算**：费用归集到相应成本中心

## 相关枚举
- **EnumFeeApplyStatus**：报销申请状态枚举
- **EnumAuditResult**：审核结果枚举
- **EnumProcessCode**：审批流程代码枚举
