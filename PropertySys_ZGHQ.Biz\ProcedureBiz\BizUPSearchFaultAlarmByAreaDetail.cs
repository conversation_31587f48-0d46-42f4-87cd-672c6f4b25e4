	using System.Collections.Generic; 
	using System.Text; 
	using System;
	using System.ServiceModel;
	using System.ServiceModel.Web;
	using NHibernate;
	using NHibernate.Linq;
	using Project.Common;
	using Project.Dal.Base;
	using Project.Biz.Base;
	using PropertySys_ZGHQ.ProcedureEntity;
	using PropertySys_ZGHQ.ProcedureDal;

	namespace PropertySys_ZGHQ.ProcedureBiz
	{
		public class  BizUPSearchFaultAlarmByAreaDetail : BaseSPBiz
		{
			private DalUPSearchFaultAlarmByAreaDetail dalUPSearchFaultAlarmByAreaDetail;
		
			private  BizUPSearchFaultAlarmByAreaDetail()
			{
				dalUPSearchFaultAlarmByAreaDetail = DalFactory.Get<DalUPSearchFaultAlarmByAreaDetail>();
			}

			[OperationContract]
			[WebInvoke(BodyStyle = WebMessageBodyStyle.Wrapped)]
			public IList<UPSearchFaultAlarmByAreaDetail> Invoke(UPSearchFaultAlarmByAreaDetailParameter parameter)
			{
									var result = dalUPSearchFaultAlarmByAreaDetail.Invoke(parameter);
					return result;
							}
		}
	}

	