# 采购管理模块ER图

## 表结构说明

### 核心实体表

#### UTWLItemApply (采购申请单表)
- **主键**: ID (Guid)
- **外键**: UnitID (项目ID, Guid?), RelationUnitID (所属项目, Guid?), BaseID (维修单ID, Guid?)
- **核心字段**: ApplyNo, BaseType, FromType, ApplyUser, ApplyDate, Status, UseDesc
- **说明**: 管理物料采购申请单的基本信息和审批流程

#### UTWLItemApplyDetial (采购申请明细表)
- **主键**: ID (Guid)
- **外键**: ItemApplyID (申请ID, Guid?), ItemID (物料ID, Guid?), ItemClassID (物料分类ID, Guid?), SupplierID (供应商ID, Guid?)
- **核心字段**: ItemCode, ItemName, ItemModel, Brand, ApplyQty, Unit, Price, CGType
- **说明**: 记录采购申请单中的具体物料明细信息

#### UTWLItem (物料信息表)
- **主键**: ID (Guid)
- **外键**: UnitID (项目ID, Guid?), ItemClassID (设备分类ID, Guid?)
- **核心字段**: ItemCode, ItemName, ItemModel, Brand, Price, Unit, AlarmQty
- **说明**: 管理物料的基本信息、价格和库存配置

#### UTWLItemClass (物料分类表)
- **主键**: ID (Guid)
- **外键**: PClassID (父分类ID, Guid?)
- **核心字段**: ClassCode, ClassName, PClassCode, AllClassName, IsLast
- **说明**: 物料分类管理，支持多级分类体系

#### UTBaseSupplier (供应商管理表)
- **主键**: ID (Guid)
- **外键**: UnitID (项目ID, Guid?)
- **核心字段**: SupplierCode, SupplierName, SupplierType, SupplierAddress, SupplierContacts, SupplierTel
- **说明**: 管理供应商基本信息、联系方式和合作关系

### 关联支撑表

#### UTBaseUnitManage (项目管理表)
- **主键**: ID (Guid)
- **外键**: PID (父级ID, Guid?)
- **核心字段**: UnitCode, UnitName, UnitLevel, Manager, Tel
- **说明**: 物业管理项目的组织架构

#### UTEQMaintain (维修单表)
- **主键**: ID (Guid)
- **外键**: UnitID (园区ID, Guid?), EqID (设备ID, Guid?)
- **核心字段**: MaintainCode, BaseType, BadDesc, Status
- **说明**: 维修单信息，可关联生成采购申请

## ER关系图

```mermaid
erDiagram
    UTBaseUnitManage {
        Guid ID PK
        Guid PID FK "父级项目ID"
        string UnitCode "项目编码"
        string UnitName "项目名称"
        int UnitLevel "项目级别"
        string Manager "负责人"
        string Tel "联系电话"
        string UnitAddress "项目地址"
        DateTime CreateDate "创建时间"
        int64 CreateUser "创建人"
    }
    
    UTWLItemClass {
        Guid ID PK
        Guid PClassID FK "父分类ID"
        string ClassCode "分类编码"
        string ClassName "分类名称"
        string PClassCode "分类父编码"
        string AllClassName "全名"
        int IsLast "是否最终类型"
        int IsDelete "是否删除"
        string Remark "备注"
        int OrderIndex "排序"
    }
    
    UTWLItem {
        Guid ID PK
        Guid UnitID FK "项目ID"
        Guid ItemClassID FK "设备分类ID"
        string ItemCode "设备编号"
        string ItemName "设备名称"
        string ItemModel "设备规格"
        string Brand "品牌"
        decimal Price "单价"
        string Unit "计量单位"
        decimal AlarmQty "库存下限报警值"
        decimal MaxAlarmQty "库存上限报警值"
        string Remark "备注"
        int IsDelete "是否删除"
        DateTime CreateDate "创建时间"
        int64 CreateUser "创建人"
    }
    
    UTBaseSupplier {
        Guid ID PK
        Guid UnitID FK "项目ID"
        string SupplierCode "供应商编号"
        string SupplierType "类型"
        string SupplierAbbreviation "供应商简称"
        string SupplierName "供应商名称"
        string Suppliernfo "服务内容"
        string SupplierAddress "供应商地址"
        string SupplierContacts "联系人"
        string SupplierTel "联系人电话"
        string SupplierEmail "邮件"
        string Corporation "法人"
        string CorporationTel "法人电话"
        int IsDelete "是否删除"
        DateTime CooperateDate "首次合作日期"
        string SupplierLevel "供应商等级"
        string FileName "资质附件名称"
        string SupplierAccount "供应商账号"
        string SupplierOpenid "openid"
        DateTime CreateDate "创建时间"
        int64 CreateUser "创建人"
    }
    
    UTWLItemApply {
        Guid ID PK
        Guid UnitID FK "项目ID"
        Guid RelationUnitID FK "所属项目"
        Guid BaseID FK "维修单ID"
        Guid SpecialApplyID FK "通用申请单号ID"
        string ApplyNo "申请单号"
        int BaseType "申请源类型"
        int FromType "数据类型"
        int64 ApplyUser "申请人"
        DateTime ApplyDate "申请日期"
        int64 CheckUser "审核人"
        DateTime CheckDate "审核时间"
        string CheckDesc "审核说明"
        int Status "状态"
        string Remark "备注"
        string UseDesc "使用说明"
        DateTime CreateDate "创建时间"
        int64 CreateUser "创建人"
        int64 CurrentDealUser "当前处理人"
        int64 CurrentApprovalUser "当前审批人"
        int ProcessCode "流程Code"
        int64 LastApprovalUser "当前最后审批人"
        DateTime LastApprovalDate "最后审批时间"
        int LastStepCode "最后审批步骤"
        int LastApprovalResult "最后审批结果"
        int64 ApplyUserID "申请人ID"
        int IsOver "是否完成"
    }
    
    UTWLItemApplyDetial {
        Guid ID PK
        Guid ItemApplyID FK "申请ID"
        Guid ItemID FK "物料ID"
        Guid ItemClassID FK "物料分类ID"
        Guid SupplierID FK "供应商ID"
        string ItemCode "物料编号"
        string ItemName "物料名称"
        string ItemModel "规格型号"
        string Brand "品牌"
        decimal ApplyQty "申请数量"
        string Unit "单位"
        decimal Price "单价"
        decimal OutQty "出库数量"
        DateTime LastOutTime "最后一次出库时间"
        int IsNeedBuy "是否需要购买"
        string SupplierName "供应商"
        string Manufacturer "制造商"
        string Specifications "规格"
        DateTime RequirementDate "要求交期"
        int Status "状态"
        int CGType "采购方式"
        string Remark "备注"
        DateTime CreateDate "创建时间"
        int64 CreateUser "创建人"
    }
    
    UTEQMaintain {
        Guid ID PK
        Guid UnitID FK "园区ID"
        Guid CustomerUserID FK "客户人员ID"
        Guid EqID FK "设备ID"
        Guid BuildID FK "建筑ID"
        string MaintainCode "维修单编号"
        int EQSystemType "设备大类"
        int FromType "维修单来源"
        int BaseType "报修类型"
        string BadDesc "故障描述"
        string BadSite "故障位置"
        string SendUser "发送人"
        DateTime SendTime "发送时间"
        int Status "状态"
        int IsUrgent "是否紧急"
    }
    
    %% 关系定义
    UTBaseUnitManage ||--o{ UTBaseUnitManage : "项目层级"
    UTBaseUnitManage ||--o{ UTWLItem : "管理物料"
    UTBaseUnitManage ||--o{ UTBaseSupplier : "管理供应商"
    UTBaseUnitManage ||--o{ UTWLItemApply : "采购申请"
    
    UTWLItemClass ||--o{ UTWLItemClass : "分类层级"
    UTWLItemClass ||--o{ UTWLItem : "物料分类"
    UTWLItemClass ||--o{ UTWLItemApplyDetial : "申请明细分类"
    
    UTWLItem ||--o{ UTWLItemApplyDetial : "申请物料"
    UTBaseSupplier ||--o{ UTWLItemApplyDetial : "供应商供货"
    
    UTWLItemApply ||--o{ UTWLItemApplyDetial : "申请明细"
    UTEQMaintain ||--o{ UTWLItemApply : "维修引起采购"
```

## 业务关系分析

### 主要业务流程

1. **物料信息管理**:
   - 建立物料分类体系(UTWLItemClass)
   - 注册物料基础信息(UTWLItem)
   - 设置库存预警阈值和价格信息

2. **供应商管理**:
   - 供应商资质注册(UTBaseSupplier)
   - 供应商能力评估和等级管理
   - 供应商合作关系维护

3. **采购申请流程**:
   - 创建采购申请单(UTWLItemApply)
   - 添加采购明细(UTWLItemApplyDetial)
   - 选择采购方式和供应商
   - 提交审批流程

4. **审批管理**:
   - 多级审批流程控制
   - 审批历史记录
   - 审批结果通知

5. **采购执行**:
   - 采购单生成和管理
   - 供应商订单跟踪
   - 到货验收和入库

### 采购方式支持

1. **自行采购** (CGType=1):
   - 项目自主采购
   - 无需指定供应商

2. **公司采购** (CGType=2):
   - 公司统一采购
   - 集中议价和配送

3. **供应商供货** (CGType=3):
   - 指定供应商直接供货
   - 必须选择具体供应商

### 申请来源类型

1. **维修申请** (BaseType=1):
   - 由维修单(UTEQMaintain)触发
   - 关联具体设备和故障

2. **直接申请** (BaseType=2):
   - 主动发起的采购需求
   - 基于业务需要或库存预警

### 关键特性

1. **多级项目管理**: 支持集团-分公司-项目的多级管理架构
2. **物料分类体系**: 支持多级物料分类，便于管理不同类型物料
3. **灵活的采购方式**: 支持多种采购模式适应不同业务场景
4. **完整的审批流程**: 支持多级审批和流程控制
5. **供应商评估**: 支持供应商等级管理和能力评估
6. **库存预警**: 支持库存上下限预警机制

### 数据完整性约束

1. **外键约束**: 确保数据引用完整性
2. **业务规则**: 
   - 采购明细必须关联有效的申请单
   - 供应商供货方式必须指定供应商
   - 物料必须属于某个分类
3. **状态控制**: 通过状态字段控制业务流程
4. **权限控制**: 基于项目层级的数据访问权限

## 状态流转说明

### 采购申请单状态 (UTWLItemApply.Status)
- **0: 保存** - 草稿状态，可以修改
- **1: 提交** - 已提交审核，等待审批
- **2: 审核通过** - 审批通过，可以执行采购
- **3: 审核不通过** - 审批拒绝，需要修改重新提交
- **99: 出库** - 已出库完成

### 采购明细状态 (UTWLItemApplyDetial.Status)
- **4: 已采购** - 该明细已完成采购

## 扩展功能支持

### 移动端支持
- 通过微信小程序支持移动端采购申请
- 支持移动端审批和查询功能

### 供应商门户
- 供应商可通过专门账号登录查看订单
- 支持供应商报价和订单确认

### 集成能力
- 与财务系统集成实现预算控制
- 与库存管理系统集成实现自动补货
- 与维修系统集成实现需求驱动采购

## 模块功能覆盖

该ER设计完整支持菜单中的所有功能：

- **供应商管理**: UTBaseSupplier管理供应商信息
- **物料分类管理**: UTWLItemClass管理分类体系
- **物料目录**: UTWLItem管理物料信息
- **采购申请单**: UTWLItemApply管理申请流程
- **采购审核**: 通过Status和审批字段管理
- **采购管理**: UTWLItemApplyDetial管理采购明细
- **采购详情**: 详细的明细信息查看
- **采购申请单查询**: 基于各表数据进行查询
- **待采购申请单**: 基于状态筛选待处理申请
- **采购申请查询**: 全面的申请单检索功能 