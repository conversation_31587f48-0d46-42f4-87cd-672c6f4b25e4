	using System.Collections.Generic; 
	using System.Text; 
	using System;
	using System.ServiceModel;
	using System.ServiceModel.Web;
	using NHibernate;
	using NHibernate.Linq;
	using Project.Common;
	using Project.Dal.Base;
	using Project.Biz.Base;
	using PropertySys_ZGHQ.ProcedureEntity;
	using PropertySys_ZGHQ.ProcedureDal;

	namespace PropertySys_ZGHQ.ProcedureBiz
	{
		public class  BizUPSearchInspectTaskReportByPEQ : BaseSPBiz
		{
			private DalUPSearchInspectTaskReportByPEQ dalUPSearchInspectTaskReportByPEQ;
		
			private  BizUPSearchInspectTaskReportByPEQ()
			{
				dalUPSearchInspectTaskReportByPEQ = DalFactory.Get<DalUPSearchInspectTaskReportByPEQ>();
			}

			[OperationContract]
			[WebInvoke(BodyStyle = WebMessageBodyStyle.Wrapped)]
			public IList<UPSearchInspectTaskReportByPEQ> Invoke(UPSearchInspectTaskReportByPEQParameter parameter)
			{
									var result = dalUPSearchInspectTaskReportByPEQ.Invoke(parameter);
					return result;
							}
		}
	}

	