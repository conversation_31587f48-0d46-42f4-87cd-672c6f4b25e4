	using System.Collections.Generic; 
	using System.Text; 
	using System;
	using System.ServiceModel;
	using System.ServiceModel.Web;
	using NHibernate;
	using NHibernate.Linq;
	using Project.Common;
	using Project.Dal.Base;
	using Project.Biz.Base;
	using PropertySys_ZGHQ.ProcedureEntity;
	using PropertySys_ZGHQ.ProcedureDal;

	namespace PropertySys_ZGHQ.ProcedureBiz
	{
		public class  BizUPSearchFaultAlarmByArea : BaseSPBiz
		{
			private DalUPSearchFaultAlarmByArea dalUPSearchFaultAlarmByArea;
		
			private  BizUPSearchFaultAlarmByArea()
			{
				dalUPSearchFaultAlarmByArea = DalFactory.Get<DalUPSearchFaultAlarmByArea>();
			}

			[OperationContract]
			[WebInvoke(BodyStyle = WebMessageBodyStyle.Wrapped)]
			public IList<UPSearchFaultAlarmByArea> Invoke(UPSearchFaultAlarmByAreaParameter parameter)
			{
									var result = dalUPSearchFaultAlarmByArea.Invoke(parameter);
					return result;
							}
		}
	}

	