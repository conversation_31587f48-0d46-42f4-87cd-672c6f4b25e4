	using System.Collections.Generic; 
	using System.Text; 
	using System;
	using System.ServiceModel;
	using System.ServiceModel.Web;
	using NHibernate;
	using NHibernate.Linq;
	using Project.Common;
	using Project.Dal.Base;
	using Project.Biz.Base;
	using PropertySys_ZGHQ.ProcedureEntity;
	using PropertySys_ZGHQ.ProcedureDal;

	namespace PropertySys_ZGHQ.ProcedureBiz
	{
		public class  BizUPSearchFireByUnitByWeek : BaseSPBiz
		{
			private DalUPSearchFireByUnitByWeek dalUPSearchFireByUnitByWeek;
		
			private  BizUPSearchFireByUnitByWeek()
			{
				dalUPSearchFireByUnitByWeek = DalFactory.Get<DalUPSearchFireByUnitByWeek>();
			}

			[OperationContract]
			[WebInvoke(BodyStyle = WebMessageBodyStyle.Wrapped)]
			public IList<UPSearchFireByUnitByWeek> Invoke(UPSearchFireByUnitByWeekParameter parameter)
			{
									var result = dalUPSearchFireByUnitByWeek.Invoke(parameter);
					return result;
							}
		}
	}

	