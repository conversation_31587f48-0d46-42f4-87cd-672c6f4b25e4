# 系统权限管理模块UML类图

## 类结构说明

### 实体类层级
- **TableEntity**: 数据表实体类 (对应数据库表结构)
- **TableBiz**: 业务逻辑类 (处理具体业务逻辑)
- **TableDal**: 数据访问类 (处理数据库访问)

### 核心业务实体

#### UTSYSUser (系统用户)
- 系统用户账户管理，支持多平台登录
- 集成微信生态(OpenID/UnionID)和钉钉系统
- 提供用户个性化设置和权限控制

#### UTSYSRole (系统角色)
- 角色定义和管理，支持多种角色类型
- 管理员角色特殊标识和权限继承
- 支持角色层级和权限分组

#### UTSYSRight (系统权限)
- 菜单权限树形结构管理
- 支持多子系统权限分离
- 页面访问权限和功能权限控制

#### UTSYSRoleRight (角色权限关系)
- 角色与权限的多对多关联
- 支持权限的灵活组合分配
- 权限继承和覆盖机制

#### UTSYSUserRole (用户角色关系)
- 用户与角色的多对多关联
- 支持用户多角色权限合并
- 角色切换和权限动态加载

#### UTSYSSystem (子系统)
- 子系统信息管理和配置
- 支持多子系统独立权限控制
- 系统间权限隔离和集成

#### UTBasePostAuth (岗位权限)
- 基于岗位的操作权限管理
- 补充RBAC的岗位级权限控制
- 支持岗位权限模板化管理

#### UTBasePostAuthDetail (岗位权限详情)
- 岗位权限的具体操作明细
- 细粒度的功能权限控制
- 支持权限的动态配置

#### UTBaseCodeGroup (代码组)
- 数据字典分组管理
- 系统配置项的标准化管理
- 支持层级分组和权限控制

#### UTBaseCode (代码表)
- 数据字典详细配置管理
- 系统枚举值和选项管理
- 支持多语言和动态配置

#### UTBaseWechatAdvertise (微信广告)
- 微信生态广告内容管理
- 支持多媒体内容和定时发布
- 广告投放权限和审核流程

#### UTBaseWechatAdvertiseDetial (微信广告详情)
- 广告详细内容和配置管理
- 支持富文本和多媒体内容
- 广告效果统计和分析

#### UTBaseWechatAdvertiseDetialFile (微信广告文件)
- 广告附件和多媒体文件管理
- 支持图片、视频、文档等多种格式
- 文件权限控制和访问统计

## UML类关系图

```mermaid
classDiagram
    class UTSYSUser {
        +Int64 UserID
        +String UserName
        +String Account
        +String Password
        +Int32? UserType
        +Guid? UnitID
        +String OpenID
        +String UnionID
        +String MiniOpenID
        +String DDUserID
        +String DDUnionID
        +Int64? DDOrgID
        +Int32? DefaultTheme
        +Int64? DefaultSystemID
        +Int32? UserStatus
        +Int32? IsActive
        +DateTime? AddDate
        +GetUserRoles() List~UTSYSRole~
        +GetUserPermissions() List~UTSYSRight~
        +HasPermission(String rightCode) Boolean
        +IsInRole(String roleName) Boolean
        +GetDefaultSystem() UTSYSSystem
        +IsWechatUser() Boolean
        +IsDingTalkUser() Boolean
    }
    
    class UTSYSRole {
        +Int64 RoleID
        +String RoleName
        +Int32? RoleType
        +Guid? UnitID
        +Int32? SortIndex
        +Int32? IsAdmin
        +DateTime? AddDate
        +GetRoleRights() List~UTSYSRight~
        +GetRoleUsers() List~UTSYSUser~
        +IsAdminRole() Boolean
        +HasRight(Int64 rightId) Boolean
        +GetPermissionCount() Int32
    }
    
    class UTSYSRight {
        +Int64 RightID
        +Int64? SystemID
        +Int64? ParentRightID
        +String RightName
        +String RightCode
        +Int32? Type
        +Int32? Level
        +String NavigateURL
        +String ImageURL
        +Int32? IsAdminRgiht
        +Int32? IsDisplay
        +Int32? SortIndex
        +GetParentRight() UTSYSRight
        +GetChildRights() List~UTSYSRight~
        +GetSystem() UTSYSSystem
        +IsMenuRight() Boolean
        +IsOperateRight() Boolean
        +GetFullPath() String
        +GetRightTree() List~UTSYSRight~
    }
    
    class UTSYSRoleRight {
        +Int64 RoleRightID
        +Int64? RoleID
        +Int64? SystemRightID
        +DateTime? AddDate
        +GetRole() UTSYSRole
        +GetRight() UTSYSRight
        +IsValid() Boolean
    }
    
    class UTSYSUserRole {
        +Int64 UserRoleID
        +Int64? UserID
        +Int64? RoleID
        +DateTime? AddDate
        +GetUser() UTSYSUser
        +GetRole() UTSYSRole
        +IsValid() Boolean
    }
    
    class UTSYSSystem {
        +Int64 SystemID
        +String SystemName
        +String SystemCode
        +String SystemURL
        +String SystemImage
        +Int32? SortIndex
        +Int32? IsActive
        +DateTime? AddDate
        +GetSystemRights() List~UTSYSRight~
        +IsActive() Boolean
        +GetRootRights() List~UTSYSRight~
    }
    
    class UTBasePostAuth {
        +Guid ID
        +Guid? UnitID
        +Int64? PostID
        +String PostName
        +String AuthCode
        +String AuthName
        +Int32? AuthType
        +DateTime? CreateDate
        +GetPostAuthDetails() List~UTBasePostAuthDetail~
        +GetUnit() UTBaseUnitManage
        +HasAuthority(String code) Boolean
        +GetAuthorities() List~String~
    }
    
    class UTBasePostAuthDetail {
        +Guid ID
        +Guid? PostAuthID
        +String AuthCode
        +String AuthName
        +String AuthValue
        +Int32? AuthType
        +DateTime? CreateDate
        +GetPostAuth() UTBasePostAuth
        +IsReadAuth() Boolean
        +IsWriteAuth() Boolean
        +IsDeleteAuth() Boolean
    }
    
    class UTBaseCodeGroup {
        +Guid ID
        +String GroupCode
        +String GroupName
        +String GroupDesc
        +Int32? SortIndex
        +DateTime? CreateDate
        +GetCodes() List~UTBaseCode~
        +GetCodeByValue(String value) UTBaseCode
        +IsSystemGroup() Boolean
    }
    
    class UTBaseCode {
        +Guid ID
        +Guid? GroupID
        +String CodeValue
        +String CodeText
        +String CodeDesc
        +Int32? SortIndex
        +Int32? IsActive
        +DateTime? CreateDate
        +GetGroup() UTBaseCodeGroup
        +IsActive() Boolean
        +GetDisplayText() String
    }
    
    class UTBaseWechatAdvertise {
        +Guid ID
        +Guid? UnitID
        +String Title
        +String Summary
        +DateTime? StartDate
        +DateTime? EndDate
        +Int32? Status
        +Int32? ViewCount
        +DateTime? CreateDate
        +Int64? CreateUserID
        +GetAdvertiseDetails() List~UTBaseWechatAdvertiseDetial~
        +GetUnit() UTBaseUnitManage
        +IsActive() Boolean
        +IsExpired() Boolean
        +IncrementViewCount() void
    }
    
    class UTBaseWechatAdvertiseDetial {
        +Guid ID
        +Guid? AdvertiseID
        +String Content
        +String HtmlContent
        +Int32? SortIndex
        +DateTime? CreateDate
        +GetAdvertise() UTBaseWechatAdvertise
        +GetDetailFiles() List~UTBaseWechatAdvertiseDetialFile~
        +HasAttachments() Boolean
    }
    
    class UTBaseWechatAdvertiseDetialFile {
        +Guid ID
        +Guid? AdvertiseDetailID
        +String FileName
        +String FileUrl
        +String FileType
        +Int64? FileSize
        +Int32? SortIndex
        +DateTime? CreateDate
        +GetAdvertiseDetail() UTBaseWechatAdvertiseDetial
        +IsImage() Boolean
        +IsVideo() Boolean
        +IsDocument() Boolean
        +GetFileExtension() String
    }
    
    class BizUTSYSUser {
        <<Service>>
        +Login(String account, String password) UTSYSUser
        +GetByID(Int64 userId) UTSYSUser
        +GetUserPermissions(Int64 userId) List~UTSYSRight~
        +GetUserRoles(Int64 userId) List~UTSYSRole~
        +SaveOrUpdate(UTSYSUser entity) Int64
        +ChangePassword(Int64 userId, String oldPwd, String newPwd) Boolean
        +ValidatePermission(Int64 userId, String rightCode) Boolean
        +GetWechatUser(String openId) UTSYSUser
        +GetDingTalkUser(String ddUserId) UTSYSUser
        +ActiveUser(Int64 userId) Boolean
        +DisableUser(Int64 userId) Boolean
    }
    
    class BizUTSYSRole {
        <<Service>>
        +GetByID(Int64 roleId) UTSYSRole
        +GetRolesByUser(Int64 userId) List~UTSYSRole~
        +GetRoleRights(Int64 roleId) List~UTSYSRight~
        +SaveOrUpdate(UTSYSRole entity) Int64
        +AssignRoleToUser(Int64 userId, Int64 roleId) Boolean
        +RemoveRoleFromUser(Int64 userId, Int64 roleId) Boolean
        +AssignRightToRole(Int64 roleId, Int64 rightId) Boolean
        +RemoveRightFromRole(Int64 roleId, Int64 rightId) Boolean
        +GetAdminRoles() List~UTSYSRole~
    }
    
    class BizUTSYSRight {
        <<Service>>
        +GetByID(Int64 rightId) UTSYSRight
        +GetRightTree(Int64 systemId) List~UTSYSRight~
        +GetUserRights(Int64 userId) List~UTSYSRight~
        +GetRoleRights(Int64 roleId) List~UTSYSRight~
        +SaveOrUpdate(UTSYSRight entity) Int64
        +GetMenuRights(Int64 systemId) List~UTSYSRight~
        +GetOperateRights(Int64 systemId) List~UTSYSRight~
        +ValidateRightCode(String rightCode) Boolean
        +GetRightByCode(String rightCode) UTSYSRight
    }
    
    class BizUTBasePostAuth {
        <<Service>>
        +GetByUnitID(Guid unitId) List~UTBasePostAuth~
        +GetByPostID(Int64 postId) List~UTBasePostAuth~
        +SaveOrUpdate(UTBasePostAuth entity) Guid
        +ValidatePostAuth(Int64 postId, String authCode) Boolean
        +GetPostAuthorities(Int64 postId) List~String~
        +AssignAuthToPost(Int64 postId, String authCode) Boolean
        +RemoveAuthFromPost(Int64 postId, String authCode) Boolean
    }
    
    class BizUTBaseCode {
        <<Service>>
        +GetByGroupCode(String groupCode) List~UTBaseCode~
        +GetCodeGroups() List~UTBaseCodeGroup~
        +GetCodeByValue(String groupCode, String value) UTBaseCode
        +SaveOrUpdate(UTBaseCode entity) Guid
        +SaveOrUpdateGroup(UTBaseCodeGroup entity) Guid
        +GetSystemCodes() List~UTBaseCode~
        +RefreshCodeCache() void
    }

    %% RBAC权限体系核心关系
    UTSYSUser }|--|| UTSYSUserRole : "用户角色关联"
    UTSYSRole }|--|| UTSYSUserRole : "角色用户关联"
    UTSYSRole }|--|| UTSYSRoleRight : "角色权限关联"
    UTSYSRight }|--|| UTSYSRoleRight : "权限角色关联"
    UTSYSSystem ||--o{ UTSYSRight : "子系统权限"
    
    %% 权限层级关系
    UTSYSRight ||--o{ UTSYSRight : "权限层级"
    
    %% 岗位权限体系
    UTBasePostAuth ||--o{ UTBasePostAuthDetail : "岗位权限详情"
    
    %% 数据字典体系
    UTBaseCodeGroup ||--o{ UTBaseCode : "代码分组"
    
    %% 微信广告体系
    UTBaseWechatAdvertise ||--o{ UTBaseWechatAdvertiseDetial : "广告详情"
    UTBaseWechatAdvertiseDetial ||--o{ UTBaseWechatAdvertiseDetialFile : "广告文件"
    
    %% 服务依赖关系
    BizUTSYSUser --> UTSYSUser : "管理"
    BizUTSYSUser --> UTSYSUserRole : "依赖"
    BizUTSYSUser --> UTSYSRole : "依赖"
    BizUTSYSUser --> UTSYSRight : "依赖"
    BizUTSYSRole --> UTSYSRole : "管理"
    BizUTSYSRole --> UTSYSRoleRight : "依赖"
    BizUTSYSRole --> UTSYSRight : "依赖"
    BizUTSYSRight --> UTSYSRight : "管理"
    BizUTSYSRight --> UTSYSSystem : "依赖"
    BizUTBasePostAuth --> UTBasePostAuth : "管理"
    BizUTBasePostAuth --> UTBasePostAuthDetail : "依赖"
    BizUTBaseCode --> UTBaseCode : "管理"
    BizUTBaseCode --> UTBaseCodeGroup : "依赖"
```

## 类职责说明

### 权限管理核心类
1. **UTSYSUser**: 用户账户管理，支持多平台集成(微信/钉钉)
2. **UTSYSRole**: 角色定义管理，支持管理员角色和普通角色
3. **UTSYSRight**: 权限菜单管理，支持树形结构和多子系统
4. **UTSYSRoleRight**: 角色权限关联，实现RBAC权限分配
5. **UTSYSUserRole**: 用户角色关联，支持多角色权限合并
6. **UTSYSSystem**: 子系统管理，支持多子系统权限隔离

### 扩展权限管理类
7. **UTBasePostAuth**: 岗位权限管理，补充RBAC的岗位级权限
8. **UTBasePostAuthDetail**: 岗位权限详情，细粒度权限控制

### 系统配置管理类
9. **UTBaseCodeGroup**: 数据字典分组，系统配置标准化
10. **UTBaseCode**: 数据字典条目，系统枚举值管理

### 微信生态管理类
11. **UTBaseWechatAdvertise**: 微信广告管理，支持定时发布
12. **UTBaseWechatAdvertiseDetial**: 广告详情管理，富文本支持
13. **UTBaseWechatAdvertiseDetialFile**: 广告文件管理，多媒体支持

### 业务逻辑服务类
- **BizUTSYSUser**: 用户管理服务，登录验证、权限检查
- **BizUTSYSRole**: 角色管理服务，角色分配、权限授权
- **BizUTSYSRight**: 权限管理服务，权限树构建、权限验证
- **BizUTBasePostAuth**: 岗位权限服务，岗位权限分配管理
- **BizUTBaseCode**: 数据字典服务，字典缓存、动态配置

## 设计模式应用

### 1. 策略模式 (Strategy Pattern)
**应用场景**: 用户权限验证
```csharp
// 权限验证策略接口
interface IPermissionStrategy {
    bool ValidatePermission(UTSYSUser user, string rightCode);
}

// RBAC权限验证策略
class RBACPermissionStrategy : IPermissionStrategy {
    public bool ValidatePermission(UTSYSUser user, string rightCode) {
        // 通过用户角色获取权限
        var roles = user.GetUserRoles();
        return roles.Any(r => r.HasRight(rightCode));
    }
}

// 岗位权限验证策略
class PostPermissionStrategy : IPermissionStrategy {
    public bool ValidatePermission(UTSYSUser user, string rightCode) {
        // 通过用户岗位获取权限
        var postAuths = GetUserPostAuthorities(user);
        return postAuths.Any(p => p.HasAuthority(rightCode));
    }
}
```

### 2. 组合模式 (Composite Pattern)
**应用场景**: 权限菜单树形结构
```csharp
// 权限组件抽象类
abstract class RightComponent {
    public abstract bool HasPermission(UTSYSUser user);
    public abstract List<RightComponent> GetChildren();
}

// 权限菜单叶子节点
class MenuRight : RightComponent {
    private UTSYSRight _right;
    
    public override bool HasPermission(UTSYSUser user) {
        return user.HasPermission(_right.RightCode);
    }
    
    public override List<RightComponent> GetChildren() {
        return new List<RightComponent>();
    }
}

// 权限菜单容器节点
class MenuGroup : RightComponent {
    private List<RightComponent> _children = new List<RightComponent>();
    
    public override bool HasPermission(UTSYSUser user) {
        return _children.Any(child => child.HasPermission(user));
    }
    
    public override List<RightComponent> GetChildren() {
        return _children;
    }
}
```

### 3. 观察者模式 (Observer Pattern)
**应用场景**: 权限变更通知
```csharp
// 权限变更观察者接口
interface IPermissionObserver {
    void OnPermissionChanged(UTSYSUser user, UTSYSRight right, string action);
}

// 权限管理主题
class PermissionSubject {
    private List<IPermissionObserver> _observers = new List<IPermissionObserver>();
    
    public void AttachObserver(IPermissionObserver observer) {
        _observers.Add(observer);
    }
    
    public void NotifyPermissionChanged(UTSYSUser user, UTSYSRight right, string action) {
        foreach (var observer in _observers) {
            observer.OnPermissionChanged(user, right, action);
        }
    }
}

// 权限缓存更新观察者
class PermissionCacheObserver : IPermissionObserver {
    public void OnPermissionChanged(UTSYSUser user, UTSYSRight right, string action) {
        // 清理用户权限缓存
        CacheManager.RemoveUserPermissions(user.UserID);
        // 重新加载用户权限
        CacheManager.LoadUserPermissions(user.UserID);
    }
}
```

### 4. 工厂模式 (Factory Pattern)
**应用场景**: 权限验证器创建
```csharp
// 权限验证器工厂
class PermissionValidatorFactory {
    public static IPermissionValidator CreateValidator(string validatorType) {
        switch (validatorType) {
            case "RBAC":
                return new RBACPermissionValidator();
            case "POST":
                return new PostPermissionValidator();
            case "COMBINED":
                return new CombinedPermissionValidator();
            default:
                throw new ArgumentException("Unknown validator type");
        }
    }
}
```

### 5. 建造者模式 (Builder Pattern)
**应用场景**: 权限菜单构建
```csharp
// 权限菜单构建器
class PermissionMenuBuilder {
    private List<UTSYSRight> _rights = new List<UTSYSRight>();
    private UTSYSUser _user;
    
    public PermissionMenuBuilder ForUser(UTSYSUser user) {
        _user = user;
        return this;
    }
    
    public PermissionMenuBuilder WithSystem(UTSYSSystem system) {
        var systemRights = GetSystemRights(system.SystemID);
        _rights.AddRange(systemRights);
        return this;
    }
    
    public PermissionMenuBuilder FilterByPermission() {
        _rights = _rights.Where(r => _user.HasPermission(r.RightCode)).ToList();
        return this;
    }
    
    public List<UTSYSRight> Build() {
        return BuildMenuTree(_rights);
    }
}
```

## 面向对象设计原则

### 1. 单一职责原则 (SRP)
- **UTSYSUser**: 专注于用户账户管理
- **UTSYSRole**: 专注于角色定义管理
- **UTSYSRight**: 专注于权限菜单管理
- **BizUTSYSUser**: 专注于用户业务逻辑处理

### 2. 开闭原则 (OCP)
- 通过接口定义权限验证策略，支持扩展新的验证方式
- 权限菜单支持新的菜单类型扩展
- 数据字典支持新的配置类型添加

### 3. 里氏替换原则 (LSP)
- 所有权限验证策略实现都可以互相替换
- 不同类型的权限组件可以统一处理

### 4. 接口隔离原则 (ISP)
- 权限验证接口与权限管理接口分离
- 用户管理接口与角色管理接口分离

### 5. 依赖倒置原则 (DIP)
- 业务逻辑层依赖于抽象接口而非具体实现
- 权限验证依赖于策略接口而非具体策略类

## NHibernate映射模式

### 1. 实体映射配置
```xml
<!-- UTSYSUser实体映射 -->
<hibernate-mapping>
    <class name="UTSYSUser" table="UT_SYS_User">
        <id name="UserID">
            <generator class="native" />
        </id>
        <property name="UserName" not-null="true" />
        <property name="Account" />
        <property name="Password" />
        <property name="UserType" />
        <property name="UnitID" />
        <property name="OpenID" />
        <property name="DefaultSystemID" />
    </class>
</hibernate-mapping>
```

### 2. 关联映射模式
```xml
<!-- UTSYSRole实体映射 -->
<hibernate-mapping>
    <class name="UTSYSRole" table="UT_SYS_Role">
        <id name="RoleID">
            <generator class="native" />
        </id>
        <!-- 一对多关联 - 角色权限 -->
        <set name="RoleRights" cascade="all">
            <key column="RoleID" />
            <one-to-many class="UTSYSRoleRight" />
        </set>
        <!-- 一对多关联 - 用户角色 -->
        <set name="UserRoles" cascade="all">
            <key column="RoleID" />
            <one-to-many class="UTSYSUserRole" />
        </set>
    </class>
</hibernate-mapping>
```

### 3. 多对多映射模式
```xml
<!-- UTSYSUser与UTSYSRole多对多关联 -->
<hibernate-mapping>
    <class name="UTSYSUser" table="UT_SYS_User">
        <set name="Roles" table="UT_SYS_UserRole">
            <key column="UserID" />
            <many-to-many class="UTSYSRole" column="RoleID" />
        </set>
    </class>
</hibernate-mapping>
```

### 4. 自关联映射模式
```xml
<!-- UTSYSRight权限菜单树形结构 -->
<hibernate-mapping>
    <class name="UTSYSRight" table="UT_SYS_Right">
        <id name="RightID">
            <generator class="native" />
        </id>
        <!-- 自关联 - 父权限 -->
        <many-to-one name="ParentRight" class="UTSYSRight" column="ParentRightID" />
        <!-- 自关联 - 子权限集合 -->
        <set name="ChildRights" cascade="all">
            <key column="ParentRightID" />
            <one-to-many class="UTSYSRight" />
        </set>
    </class>
</hibernate-mapping>
```

## 多层架构集成

### 1. Entity层职责
- **实体定义**: 对应数据库表结构
- **属性映射**: 字段到属性的映射
- **关联关系**: 实体间的关联定义
- **业务方法**: 实体级别的业务逻辑

### 2. Dal层职责
- **数据访问**: 封装数据库操作
- **查询构建**: 复杂查询的构建
- **事务管理**: 数据操作的事务控制
- **缓存管理**: 数据缓存策略

### 3. Biz层职责
- **业务逻辑**: 核心业务规则处理
- **权限验证**: 业务级权限检查
- **数据验证**: 业务数据有效性验证
- **流程控制**: 业务流程的控制

### 4. Web层职责
- **用户界面**: 权限管理界面展示
- **权限控制**: 页面级权限控制
- **用户交互**: 用户操作的响应处理
- **数据展示**: 权限数据的格式化展示

### 5. 层间协作模式
```csharp
// Web层调用示例
public class UserController : BaseController {
    private BizUTSYSUser _userBiz = new BizUTSYSUser();
    
    [HttpPost]
    public ActionResult Login(string account, string password) {
        var user = _userBiz.Login(account, password);
        if (user != null) {
            // 设置用户会话
            SessionManager.SetCurrentUser(user);
            // 加载用户权限
            var permissions = _userBiz.GetUserPermissions(user.UserID);
            SessionManager.SetUserPermissions(permissions);
            return RedirectToAction("Index", "Home");
        }
        return View("Login");
    }
}

// Biz层业务逻辑示例
public class BizUTSYSUser {
    private DalUTSYSUser _userDal = new DalUTSYSUser();
    
    public UTSYSUser Login(string account, string password) {
        // 数据验证
        if (string.IsNullOrEmpty(account) || string.IsNullOrEmpty(password)) {
            throw new ArgumentException("账号密码不能为空");
        }
        
        // 调用数据访问层
        var user = _userDal.GetByAccount(account);
        if (user != null && VerifyPassword(user.Password, password)) {
            // 更新登录时间
            user.LastLoginDate = DateTime.Now;
            _userDal.SaveOrUpdate(user);
            return user;
        }
        return null;
    }
}
```

## 权限验证机制

### 1. 页面权限控制
```csharp
// 权限过滤器
public class PermissionFilter : ActionFilterAttribute {
    public string RightCode { get; set; }
    
    public override void OnActionExecuting(ActionExecutingContext context) {
        var user = SessionManager.GetCurrentUser();
        if (user == null || !user.HasPermission(RightCode)) {
            context.Result = new RedirectResult("/Error/NoPermission");
        }
        base.OnActionExecuting(context);
    }
}

// 控制器权限标注
[PermissionFilter(RightCode = "USER_MANAGE")]
public class UserManageController : BaseController {
    // 用户管理相关操作
}
```

### 2. 动态权限加载
```csharp
// 权限缓存管理
public class PermissionCacheManager {
    private static Dictionary<long, List<string>> _userPermissions = 
        new Dictionary<long, List<string>>();
    
    public static List<string> GetUserPermissions(long userId) {
        if (!_userPermissions.ContainsKey(userId)) {
            LoadUserPermissions(userId);
        }
        return _userPermissions[userId];
    }
    
    private static void LoadUserPermissions(long userId) {
        var biz = new BizUTSYSUser();
        var permissions = biz.GetUserPermissions(userId);
        _userPermissions[userId] = permissions.Select(p => p.RightCode).ToList();
    }
}
```

### 3. 权限菜单动态构建
```csharp
// 权限菜单构建服务
public class MenuBuilderService {
    public List<MenuTreeNode> BuildUserMenu(long userId) {
        var user = GetUser(userId);
        var allRights = GetAllRights();
        var userRights = GetUserRights(userId);
        
        var allowedRights = allRights.Where(r => 
            userRights.Any(ur => ur.RightID == r.RightID)).ToList();
        
        return BuildMenuTree(allowedRights);
    }
    
    private List<MenuTreeNode> BuildMenuTree(List<UTSYSRight> rights) {
        var rootNodes = rights.Where(r => r.ParentRightID == null).ToList();
        var menuTree = new List<MenuTreeNode>();
        
        foreach (var root in rootNodes) {
            var node = new MenuTreeNode {
                RightID = root.RightID,
                RightName = root.RightName,
                NavigateURL = root.NavigateURL,
                Children = BuildChildNodes(root.RightID, rights)
            };
            menuTree.Add(node);
        }
        
        return menuTree;
    }
}
```

这个UML类图文档全面展现了PropertySys系统权限管理模块的类结构设计，包含了完整的RBAC权限体系、岗位权限扩展、数据字典管理、微信生态集成等核心功能的面向对象设计。通过合理的设计模式应用和多层架构集成，实现了灵活、可扩展的权限管理系统。 