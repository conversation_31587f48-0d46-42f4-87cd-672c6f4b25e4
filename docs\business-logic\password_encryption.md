# 登录密码加密方式

## 概述

在用户登录时，如果系统配置为加密密码，则会使用 RSA 非对称加密算法在前端对用户输入的密码进行加密，然后再将加密后的密文发送到服务器。

## 加密流程

1.  **触发条件**:
    *   仅当 `Web.config` 配置文件中的 `IsPasswordEncryption` 的值设置为 `1` 时，才会启用密码加密功能。

2.  **前端加密**:
    *   前端使用 `jsencrypt.min.js` 库进行 RSA 加密。
    *   加密使用的公钥是硬编码在 `login.cshtml` 文件中的，具体如下：
        ```
        MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDHCkaSCVy6sHB/5rAKS/1EEtyzWuy30gLyrpNbFI3GtpsdFGdsqQ/uwiscGD+pZ7Mxj1ZumPs4jHvPpcAeCb8gKsqP/f5+pputTMuTkhTQqlDT1plHR7w3REQI8MaJ8KTA/pJiPo6iWToFynQeJNWjicxXxNURSZQ7nmC2rl4uPQIDAQAB
        ```
    *   加密过程在 `loginConfirm()` 函数中执行。
    *   原始密码经过加密后，会再进行一次 `encodeURI` 编码，并且会将 `+` 符号替换为 `%2B`，以确保数据在传输过程中不会丢失或被错误解析。

## 相关代码

加密逻辑主要位于 `Areas/ModuleSys/Views/Account/login.cshtml` 文件中的 `loginConfirm()` JavaScript 函数中：

```javascript
if(@System.Configuration.ConfigurationManager.AppSettings["IsPasswordEncryption"].ToString()=="1")
{
    var publicKey = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDHCkaSCVy6sHB/5rAKS/1EEtyzWuy30gLyrpNbFI3GtpsdFGdsqQ/uwiscGD+pZ7Mxj1ZumPs4jHvPpcAeCb8gKsqP/f5+pputTMuTkhTQqlDT1plHR7w3REQI8MaJ8KTA/pJiPo6iWToFynQeJNWjicxXxNURSZQ7nmC2rl4uPQIDAQAB";
    var encrypt = new JSEncrypt();
    encrypt.setPublicKey(publicKey);
    var mingwen = $("#password").val();
    miwen = encrypt.encrypt(mingwen);
    $("#password").val(encodeURI(miwen).replace(/\+/g, '%2B'));
}
```



### 解密

``` c#
	/// <summary>
    /// RSA解密
    /// </summary>
    /// <param name="content">加密后的字符串</param>
    /// <returns>解密后的字符串</returns>
    public static string RSADecrypt(string content)
    {
        string privatekey = @"<RSAKeyValue><Modulus>xwpGkglcurBwf+awCkv9RBLcs1rst9IC8q6TWxSNxrabHRRnbKkP7sIrHBg/qWezMY9Wbpj7OIx7z6XAHgm/ICrKj/3+fqabrUzLk5IU0KpQ09aZR0e8N0RECPDGifCkwP6SYj6Oolk6Bcp0HiTVo4nMV8TVEUmUO55gtq5eLj0=</Modulus><Exponent>AQAB</Exponent><P>/LnrFrusoNklOl6d0zSWZ1aCdQC2l3XXU8SSgYNLuSmgVwl7wQ2w0Jn9SQqyiVRmbcp1SX28/bH6TaA2v9ejHQ==</P><Q>yZ5TTtLOfTqrikYjy/fyktTk977y2GG2R9sgNPHtnH5EIIC9CoJETDwfSu40YUlHeUUXHQ1nG1WmWbEOC76toQ==</Q><DP>5UfC+YfgkLkQJklqxA+EmFIK3x17iiO16+B9zhQQ4fba6bvH05iZHldmTBrxaNfyaY7xI3B4wmzymfRNV3TKHQ==</DP><DQ>k7EvRaaXLJU14+zNfDT9tSHPOMzgCDJL3Qdf6GjwrpqwPT8RPAmBDndcVP95z2pmuScrb1TKGvP7D+jraR8dAQ==</DQ><InverseQ>4gbDpW7ca3dn0XXPkYsVmIl7SBqU8lq9X2xji/Nyg1M0pjDcpdQm0bqOm+/5usQl+kRotpIoK+Yf6J++zbmNjg==</InverseQ><D>Ccxfc356/mTDsQQv+93ISsLb8wdhml4AD6bY8bWmEhd4tNqFieObuW79FM27ypDkkSbDhD/LNDo0OSFpfwEPU8VxnEMzFnVw7MIWGSVKWocZHIhsclkHtHNtHaKS0LNEie2q0PGMiIYty/QG5k3bJeA8R42teXv3nARYEgzuNmE=</D></RSAKeyValue>";
        RSACryptoServiceProvider rsa = new RSACryptoServiceProvider();
        byte[] cipherbytes;
        rsa.FromXmlString(privatekey);

        cipherbytes = rsa.Decrypt(Convert.FromBase64String(content.Trim('\0').Replace("%2B", "+")), false);
        return Encoding.UTF8.GetString(cipherbytes);
    }
```

