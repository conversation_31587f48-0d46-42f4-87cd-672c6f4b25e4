# 项目各分区管理模块分析

## 模块概述

项目各分区管理模块是项目管理系统的核心功能模块，负责管理项目内各个建筑物的分区信息。该模块提供分区的创建、编辑、删除、图片管理等功能，为设备设施管理、房间管理等其他业务模块提供基础的空间定位支撑。

## 路由信息

- **子菜单名**: 项目各分区管理
- **路由地址**: ~/ModuleUnit/UnitBuildArea/UnitManageTree
- **模块名**: ModuleUnit
- **视图名**: UnitBuildArea

## 页面逻辑分析

### 主要功能

1. **分区信息管理**
   - 分区基本信息的增删改查
   - 分区名称、位置、状态管理
   - 分区排序和层级管理
   - 分区与建筑物的关联管理

2. **分区图片管理**
   - 分区背景图片上传
   - 图片尺寸自动获取和存储
   - 图片文件管理和存储路径维护

3. **项目树形结构**
   - 多项目支持和项目选择
   - 建筑物-分区层级展示
   - 树形结构的动态加载

4. **权限控制**
   - 基于岗位的操作权限控制
   - 分区管理权限验证
   - 数据访问权限隔离

### 控制器分析

#### UnitBuildAreaController (ModuleUnit)

**主要方法**:
- `UnitManageTree()`: 项目管理树形结构页面
- `Index()`: 分区管理主页面
- `UnitBuildAreaIndex()`: 分区列表页面
- `UnitBuildAreaCreate()`: 分区创建/编辑页面
- `Delete()`: 分区删除操作

**核心业务逻辑**:
```csharp
// 项目树形结构管理
public ActionResult UnitManageTree(ModelSysOrganizationIndex model)
{
    IList<UTBaseUnitManage> UTBaseUnitManageList = SessionManager.CurrentUserProjects;
    if (UTBaseUnitManageList == null || UTBaseUnitManageList.Count == 0|| UTBaseUnitManageList.Count == 1)
    {
        return RedirectToAction("Index", new { UnitID = SessionManager.CurrentUserAccount.UnitID });
    }
    else
    {
        return View(model);
    }
}

// 分区删除操作
[HttpPost]
public ActionResult Delete(Guid? id)
{
    JsonResult jsResult = new JsonResult();
    jsResult.ContentType = Consts.CONTENT_TYPE;
    jsResult.Data = new { result = string.Empty };
    try
    {
        // 检查是否存在关联的房间信息
        if (serviceUTUnitRoom.CountBy(ExpressionWrapper<UTUnitRoom>.Where(x => x.BuildAreaID == id))>0)
        {
            jsResult.Data = new { result = "请先删除区域下的房间信息！" };
            return jsResult;
        }
        serviceUTUnitBuildArea.DeleteByID(id);
    }
    catch (Exception ex)
    {
        jsResult.Data = new { result = ex.Message };
    }
    return jsResult;
}
```

### 模型分析

#### ModelUnitBuildAreaIndex
- 负责分区列表页面的数据检索和展示
- 支持按建筑物筛选分区信息
- 权限控制和操作按钮显示控制

#### ModelUnitBuildAreaCreate
- 负责分区创建和编辑功能
- 图片文件上传和处理
- 数据验证和保存操作

**核心保存逻辑**:
```csharp
public string Save()
{
    string str = "";
    try
    {
        // 文件保存处理
        if (PostedFileBase != null)
        {
            UTUnitBuildAreaEnt.BuildAreaImg = UploadHelperC.SaveFile(PostedFileBase, "ImgBuildArea");
            System.Drawing.Image localImage = System.Drawing.Image.FromStream(PostedFileBase.InputStream);
            UTUnitBuildAreaEnt.Width = localImage.Width;
            UTUnitBuildAreaEnt.Height = localImage.Height;
        }
        if (ID == null || ID == Guid.Empty)
        {
            UTUnitBuildAreaEnt.BuildID = BuildID;
        }
        UTUnitBuildAreaEnt.ID = (Guid)serviceUTUnitBuildArea.SaveOrUpdate(UTUnitBuildAreaEnt);
    }
    catch (Exception ex)
    {
        str = ex.Message;
    }
    return str;
}
```

## 数据表结构

### 主表: UT_Unit_BuildArea (区域管理)

| 字段名 | 数据类型 | 长度 | 允许空 | 说明 |
|--------|----------|------|--------|------|
| ID | uniqueidentifier | - | NO | 主键ID |
| BuildID | uniqueidentifier | - | YES | 建筑ID |
| BuildAreaName | nvarchar | 150 | YES | 区域名称 |
| BuildAreaSite | nvarchar | 150 | YES | 区域位置 |
| BuildAreaImg | nvarchar | 255 | YES | 背景图 |
| Status | int | - | YES | 状态 |
| Width | int | - | YES | 背景图宽 |
| Height | int | - | YES | 背景图高 |
| OrderIndex | int | - | YES | 顺序 |

**建表SQL**:
```sql
create table [dbo].[UT_Unit_BuildArea] (  
    [ID] uniqueidentifier  NOT NULL ,  
    [BuildID] uniqueidentifier  NULL ,  
    [BuildAreaName] nvarchar(150)  NULL ,  
    [BuildAreaSite] nvarchar(150)  NULL ,  
    [BuildAreaImg] nvarchar(255)  NULL ,  
    [Status] int  NULL ,  
    [Width] int  NULL ,  
    [Height] int  NULL ,  
    [OrderIndex] int  NULL 
);  
ALTER TABLE [dbo].[UT_Unit_BuildArea] ADD CONSTRAINT PK_UT_Unit_BuildArea PRIMARY KEY  ([ID]);
```

### 关联表: UT_Unit_Build (建筑物表)

该表与分区表形成一对多关系，一个建筑物可以包含多个分区。

| 字段名 | 数据类型 | 说明 |
|--------|----------|------|
| ID | uniqueidentifier | 主键ID |
| UnitID | uniqueidentifier | 项目ID |
| BuildName | nvarchar(50) | 建筑名称 |
| BuildType | int | 建筑类型 |
| BuildArea | decimal | 建筑面积 |

## 视图结构

### UV_Unit_BuildArea (分区信息视图)
- 整合分区基本信息和建筑物信息
- 提供分区数据的统一查询接口
- 包含项目ID用于数据权限控制

**视图创建SQL**:
```sql
CREATE VIEW dbo.UV_Unit_BuildArea
AS
SELECT   NEWID() AS keyID, dbo.UT_Unit_BuildArea.ID, dbo.UT_Unit_BuildArea.BuildID, 
         dbo.UT_Unit_BuildArea.BuildAreaName, dbo.UT_Unit_BuildArea.BuildAreaSite, 
         dbo.UT_Unit_BuildArea.BuildAreaImg, dbo.UT_Unit_BuildArea.Status, 
         dbo.UT_Unit_BuildArea.Width, dbo.UT_Unit_BuildArea.Height, 
         dbo.UT_Unit_BuildArea.OrderIndex, dbo.UT_Unit_Build.UnitID
FROM      dbo.UT_Unit_BuildArea LEFT OUTER JOIN
          dbo.UT_Unit_Build ON dbo.UT_Unit_BuildArea.BuildID = dbo.UT_Unit_Build.ID
```

### UV_Unit_BuildArea_Beacon (分区信标视图)
- 整合分区信息和信标设备统计
- 支持物联网定位功能
- 提供分区内信标设备数量统计

**视图创建SQL**:
```sql
CREATE VIEW dbo.UV_Unit_BuildArea_Beacon
AS
SELECT   NEWID() AS KeyID, dbo.UT_Unit_BuildArea.ID, dbo.UT_Unit_BuildArea.BuildID, 
         dbo.UT_Unit_BuildArea.BuildAreaName, dbo.UT_Unit_BuildArea.BuildAreaSite, 
         dbo.UT_Unit_BuildArea.BuildAreaImg, dbo.UT_Unit_BuildArea.Status, 
         dbo.UT_Unit_BuildArea.Width, dbo.UT_Unit_BuildArea.Height, 
         dbo.UT_Unit_BuildArea.OrderIndex, A.AreaID, A.BeaconCount, 
         dbo.UT_Unit_Build.UnitID, dbo.UT_Unit_Build.BuildName
FROM      dbo.UT_Unit_BuildArea LEFT OUTER JOIN
          dbo.UT_Unit_Build ON dbo.UT_Unit_BuildArea.BuildID = dbo.UT_Unit_Build.ID LEFT OUTER JOIN
          (SELECT   AreaID, COUNT(1) AS BeaconCount
           FROM      dbo.UT_LW_LYBeacon
           GROUP BY AreaID) AS A ON dbo.UT_Unit_BuildArea.ID = A.AreaID
```

## 存储过程

### UP_NHBuildingByArea
**功能**: 按分区统计能耗数据

**参数**:
- @ProjectID uniqueidentifier - 项目ID
- @BuildingID uniqueidentifier - 建筑ID
- @NHtype int - 能耗类型
- @DateE Datetime - 结束日期
- @DateS DateTime - 开始日期

**SQL实现**:
```sql
CREATE PROCEDURE [dbo].[UP_NHBuildingByArea]
@ProjectID uniqueidentifier,
@BuildingID uniqueidentifier,
@NHtype int ,
@DateE Datetime,
@DateS DateTime
AS
if(@NHtype=1)
begin
    select SUM(ISNULL(A.ReadNum,0))ReadNum,SUM(ISNULL(A.Area,0))Area from (
    SELECT sum(isnull([dbo].[UT_NH_ElectricTask].ReadNum,0)-ISNULL([dbo].[UT_NH_ElectricTask].LastNum,0))ReadNum,ISNULL( Area,0)Area
    FROM [dbo].[UT_NH_ElectricTask]
    left join [dbo].[UT_NH_ElectricMeter]
    on [dbo].[UT_NH_ElectricTask].ElectricMeterID =  [dbo].[UT_NH_ElectricMeter].ID
    left join [dbo].[UT_NH_ElectricMeterReProject]
    on [dbo].[UT_NH_ElectricMeter].ID = [dbo].[UT_NH_ElectricMeterReProject].ElectricMeterID
    left join [dbo].[UT_Unit_Room] on  [dbo].[UT_NH_ElectricMeterReProject].RoomID= [dbo].[UT_Unit_Room].ID
    left join [dbo].[UT_Unit_Build] on  [dbo].[UT_Unit_Room].BuildID=[dbo].[UT_Unit_Build].ID
    left join [dbo].[UT_Base_Code] on [dbo].[UT_Unit_Build].NHFunction=[dbo].[UT_Base_Code].CodeID
    where [dbo].[UT_Base_Code].CodeGroupID='302D57FB-94B2-4C9C-A47F-AA6F00274B5C'
         and [dbo].[UT_NH_ElectricMeter].UnitID=@ProjectID  and ([dbo].[UT_Unit_Build].ID=@BuildingID or  @BuildingID is null)
    and  (ReadDate>=@DateS or @DateS is null)
    and  (ReadDate<=@DateE or @DateE is null)
    group by Area
    ) A
end
```

## 业务流程

### 分区创建流程
1. 用户选择项目和建筑物
2. 填写分区基本信息（名称、位置）
3. 上传分区背景图片（可选）
4. 系统自动获取图片尺寸
5. 保存分区信息到数据库
6. 更新分区排序索引

### 分区编辑流程
1. 从分区列表选择要编辑的分区
2. 加载现有分区信息
3. 修改分区信息或更换背景图片
4. 验证数据完整性
5. 保存更新后的分区信息

### 分区删除流程
1. 选择要删除的分区
2. 检查分区下是否存在房间信息
3. 如果存在房间，提示先删除房间
4. 如果无关联数据，执行删除操作
5. 更新相关缓存和索引

### 图片管理流程
1. 用户选择图片文件上传
2. 系统验证文件格式和大小
3. 保存图片到指定目录（ImgBuildArea）
4. 自动获取图片宽度和高度
5. 更新数据库中的图片路径和尺寸信息

## 权限控制

### 功能权限
- 分区创建权限
- 分区编辑权限
- 分区删除权限
- 图片上传权限

### 权限验证机制
```csharp
// 权限检查逻辑
IsOperate = 0;
UTBasePostAuth UTBasePostAuthEnt = serviceUTBasePostAuth.GetFirst(ExpressionWrapper<UTBasePostAuth>.Where(x => x.MenuValue == 10));
if (UTBasePostAuthEnt != null)
{
    IList<long?> UserTypeList = serviceUTBasePostAuthDetail.ListBy(ExpressionWrapper<UTBasePostAuthDetail>.Where(x => x.PostAuthID == UTBasePostAuthEnt.ID && x.UserID != null)).Select(x => x.UserID).Distinct().ToList();
    if (UserTypeList != null && UserTypeList.Contains(SessionManager.CurrentUserAccount.UserID))
    {
        IsOperate = 1;
    }
}
```

### 数据权限
- 按项目隔离分区数据
- 用户只能管理所属项目的分区
- 支持跨项目分区查看（特殊权限）

## 技术特点

1. **多项目支持**: 支持多项目分区数据隔离和管理
2. **图片处理**: 自动处理图片上传和尺寸获取
3. **层级管理**: 支持建筑物-分区的层级结构
4. **关联检查**: 删除前检查关联数据完整性
5. **权限控制**: 基于岗位的细粒度权限控制
6. **树形展示**: 动态加载的树形结构展示

## 扩展功能

1. **分区模板**: 支持分区信息模板化管理
2. **批量导入**: 支持Excel批量导入分区信息
3. **分区统计**: 提供分区使用情况统计分析
4. **3D展示**: 集成3D分区展示功能
5. **设备关联**: 与设备设施管理深度集成
6. **定位服务**: 支持室内定位和导航服务

## 关联模块

1. **建筑物管理**: 分区隶属于建筑物
2. **房间管理**: 房间隶属于分区
3. **设备设施管理**: 设备安装在分区内
4. **物联网应用**: 分区作为设备定位基础
5. **巡检管理**: 按分区组织巡检任务
6. **维修管理**: 按分区统计维修工单
