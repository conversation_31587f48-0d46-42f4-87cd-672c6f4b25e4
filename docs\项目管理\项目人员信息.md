# 项目人员信息模块分析

## 模块概述

项目人员信息模块是项目管理系统的重要组成部分，负责管理项目内的组织架构和人员信息。该模块提供项目级别的组织架构管理、部门设置、人员分配等功能，为人事管理、权限控制、工作流程等其他业务模块提供组织架构基础支撑。

## 路由信息

- **子菜单名**: 项目人员信息
- **路由地址**: ~/ModuleSys/SysOrgnization/ProjectTree
- **模块名**: ModuleSys
- **视图名**: SysOrgnization

## 页面逻辑分析

### 主要功能

1. **组织架构管理**
   - 项目级组织架构的创建和维护
   - 部门层级结构管理
   - 部门基本信息维护
   - 组织架构树形展示

2. **部门信息管理**
   - 部门基本信息（名称、编号、类型）
   - 部门联系信息（电话、邮箱、地址）
   - 部门负责人和联系人管理
   - 部门状态和权限设置

3. **项目树形结构**
   - 多项目支持和项目选择
   - 项目-部门层级展示
   - 动态加载的组织架构树

4. **特殊部门标识**
   - 维修组标识管理
   - 管理员组标识
   - 最后一级部门标识

### 控制器分析

#### SysOrgnizationController (ModuleSys)

**主要方法**:
- `ProjectTree()`: 项目组织架构树形结构页面
- `Index()`: 组织架构管理主页面
- `JTLoadChildren()`: 加载子级组织架构
- `LoadChildren()`: 动态加载组织架构节点

**核心业务逻辑**:
```csharp
// 项目组织架构树形结构
public ActionResult ProjectTree()
{
    IList<UTBaseUnitManage> UTBaseUnitManageList = SessionManager.CurrentUserProjects;
    if (UTBaseUnitManageList == null || UTBaseUnitManageList.Count == 0 || UTBaseUnitManageList.Count == 1)
    {
        return RedirectToAction("Index", new { UnitID = SessionManager.CurrentUserAccount.UnitID });
    }
    else
    {
        return View();
    }
}

// 动态加载组织架构子节点
[OutputCache(NoStore = true, Duration = 0)]
public JsonResult JTLoadChildren(string id, int? orgtype, Guid? UnitID)
{
    UTSYSOrganization clickedOrg = null;
    IList<UTSYSOrganization> childrenCollection = null;
    string nodeType = string.Empty;
    IList<JsonTreeData> treeDataCollection = new List<JsonTreeData>();
    
    if (id.StartsWith("root"))
    {
        if (UnitID == null || UnitID == Guid.Empty)
        {
            UnitID = SessionManager.CurrentUserAccount.UnitID;
        }
        nodeType = "folder";
        UTBaseUnitManage UTBaseUnitManageEnt = serviceUTBaseUnitManage.GetByID(UnitID);
        if (UTBaseUnitManageEnt.IsProject == 1)
        {
            IList<UTBaseUnitManage> unitList = SessionManager.CurrentUserProjects;
            if (unitList != null && unitList.Count > 1)
            {
                foreach (var item in unitList)
                {
                    JsonTreeData temp = new JsonTreeData()
                    {
                        id = "Project" + item.ID.ToString(),
                        text = item.UnitName,
                        children = serviceUTSYSOrganization.CountBy(ExpressionWrapper<UTSYSOrganization>.Where(x => x.UnitID == item.ID && (x.IsDeleted == null || x.IsDeleted == false))) > 0,
                        type = nodeType
                    };
                    treeDataCollection.Add(temp);
                }
                return Json(treeDataCollection, JsonRequestBehavior.AllowGet);
            }
        }
    }
    // ... 其他节点加载逻辑
}
```

### 模型分析

#### ModelSysOrganizationIndex
- 负责组织架构列表页面的数据检索和展示
- 支持按项目筛选组织架构信息
- 权限控制和操作按钮显示控制

#### ModelSysOrganizationCreate
- 负责组织架构创建和编辑功能
- 层级关系处理和验证
- 数据保存和更新操作

**核心保存逻辑**:
```csharp
public string Save()
{
    try
    {
        if (ParentID == "root" || (ParentID != null && ParentID.Contains("Project")))
        {
            UTSYSOrganizationEntity.Level = 0;
            UTSYSOrganizationEntity.OrgParentID = 0;
        }
        else if (ParentID != "root" && OrgID == null)
        {
            ParentSYSOrganizationEntity = serviceUTSYSOrganization.GetByID(long.Parse(ParentID));
            UTSYSOrganizationEntity.Level = ParentSYSOrganizationEntity.Level + 1;
            UTSYSOrganizationEntity.OrgParentID = ParentSYSOrganizationEntity.OrgID;
        }
        // ... 保存逻辑
    }
    catch (Exception ex)
    {
        return ex.Message;
    }
}
```

## 数据表结构

### 主表: UT_SYS_Organization (组织机构部门表)

| 字段名 | 数据类型 | 长度 | 允许空 | 说明 |
|--------|----------|------|--------|------|
| OrgID | bigint | - | NO | 主键ID |
| OrgName | nvarchar | 50 | YES | 部门名称 |
| OrgType | int | - | YES | 部门类型（0公司级，1分公司级，2项目级） |
| OrgNumber | nvarchar | 50 | YES | 部门编号 |
| OrgParentID | bigint | - | YES | 父级部门ID |
| OrgEmail | nvarchar | 50 | YES | 部门邮箱 |
| OrgPhone | nvarchar | 50 | YES | 部门电话 |
| OrgAddress | nvarchar | 200 | YES | 机构地址 |
| OrgLegal | nvarchar | 50 | YES | 法人代表 |
| OrgFax | nvarchar | 50 | YES | 传真 |
| ChargePerson | nvarchar | 50 | YES | 负责人 |
| OrgLinkman | nvarchar | 50 | YES | 部门联系人 |
| LinkPhone | nvarchar | 50 | YES | 联系电话 |
| LinkMobile | nvarchar | 50 | YES | 联系手机 |
| LinkEmail | nvarchar | 50 | YES | 联系邮箱 |
| SortIndex | int | - | YES | 排序号 |
| Remark | ntext | - | YES | 备注 |
| AddDate | datetime | - | YES | 添加时间 |
| UpdateDate | datetime | - | YES | 更新时间 |
| IsDeleted | bit | - | YES | 删除标记 |
| Level | int | - | YES | 层级数 |
| CreateUser | bigint | - | YES | 创建用户 |
| UpdateUser | bigint | - | YES | 更新用户 |
| UnitID | uniqueidentifier | - | YES | 所属项目ID |
| IsLast | int | - | YES | 是否最后一级 |
| MaintenanceNY | int | - | YES | 是否维修组 |
| Status | int | - | YES | 状态 |
| ManagerIDStr | nvarchar | 500 | YES | 上级领导ID字符串 |
| ManagerNameStr | nvarchar | 500 | YES | 上级领导姓名字符串 |
| IsAdmin | int | - | YES | 是否管理员组 |

**建表SQL**:
```sql
create table [dbo].[UT_SYS_Organization] (  
    [OrgID] bigint IDENTITY(1,1) NOT NULL ,  
    [OrgName] nvarchar(50)  NULL ,  
    [OrgType] int  NULL ,  
    [OrgNumber] nvarchar(50)  NULL ,  
    [OrgParentID] bigint  NULL ,  
    [OrgEmail] nvarchar(50)  NULL ,  
    [OrgPhone] nvarchar(50)  NULL ,  
    [OrgAddress] nvarchar(200)  NULL ,  
    [OrgLegal] nvarchar(50)  NULL ,  
    [OrgFax] nvarchar(50)  NULL ,  
    [ChargePerson] nvarchar(50)  NULL ,  
    [OrgLinkman] nvarchar(50)  NULL ,  
    [LinkPhone] nvarchar(50)  NULL ,  
    [LinkMobile] nvarchar(50)  NULL ,  
    [LinkEmail] nvarchar(50)  NULL ,  
    [SortIndex] int  NULL ,  
    [Remark] ntext  NULL ,  
    [AddDate] datetime  NULL ,  
    [UpdateDate] datetime  NULL ,  
    [IsDeleted] bit  NULL ,  
    [Level] int  NULL ,  
    [CreateUser] bigint  NULL ,  
    [UpdateUser] bigint  NULL ,  
    [UnitID] uniqueidentifier  NULL ,  
    [IsLast] int  NULL ,  
    [MaintenanceNY] int  NULL ,  
    [Status] int  NULL ,  
    [ManagerIDStr] nvarchar(500)  NULL ,  
    [ManagerNameStr] nvarchar(500)  NULL ,  
    [IsAdmin] int  NULL 
);  
ALTER TABLE [dbo].[UT_SYS_Organization] ADD CONSTRAINT PK_UT_SYS_Organization PRIMARY KEY  ([OrgID]);
```

### 关联表: UT_Base_UnitManage (项目基本信息表)

该表与组织架构表形成一对多关系，一个项目可以包含多个部门。

| 字段名 | 数据类型 | 说明 |
|--------|----------|------|
| ID | uniqueidentifier | 主键ID |
| UnitCode | nvarchar(50) | 项目编码 |
| UnitName | nvarchar(50) | 项目名称 |
| UnitLevel | int | 项目级别 |
| IsProject | int | 是否项目 |

## 视图结构

### UV_SYS_Organization (组织架构信息视图)
- 整合组织架构基本信息和层级关系
- 提供组织架构数据的统一查询接口
- 包含多级父级部门信息

**视图创建SQL**:
```sql
CREATE VIEW [dbo].[UV_SYS_Organization]
AS
SELECT  NEWID() AS KeyID, dbo.UT_SYS_Organization.OrgID, dbo.UT_SYS_Organization.UnitID, 
        dbo.UT_SYS_Organization.OrgName, dbo.UT_SYS_Organization.OrgType, dbo.UT_SYS_Organization.OrgNumber, 
        dbo.UT_SYS_Organization.OrgParentID, dbo.UT_SYS_Organization.OrgEmail, dbo.UT_SYS_Organization.OrgPhone, 
        dbo.UT_SYS_Organization.OrgAddress, dbo.UT_SYS_Organization.OrgLegal, dbo.UT_SYS_Organization.OrgFax, 
        dbo.UT_SYS_Organization.ChargePerson, dbo.UT_SYS_Organization.OrgLinkman, dbo.UT_SYS_Organization.LinkPhone, 
        dbo.UT_SYS_Organization.LinkMobile, dbo.UT_SYS_Organization.LinkEmail, dbo.UT_SYS_Organization.SortIndex, 
        dbo.UT_SYS_Organization.Remark, dbo.UT_SYS_Organization.AddDate, dbo.UT_SYS_Organization.IsDeleted, 
        dbo.UT_SYS_Organization.UpdateDate, dbo.UT_SYS_Organization.IsLast, dbo.UT_SYS_Organization.[Level], 
        dbo.UT_SYS_Organization.MaintenanceNY, UT_SYS_Organization_1.OrgID AS OrgID1, 
        UT_SYS_Organization_2.OrgID AS OrgID2, UT_SYS_Organization_3.OrgID AS OrgID3, dbo.UT_SYS_Organization.IsAdmin, 
        dbo.UT_SYS_Organization.Status, dbo.UT_SYS_Organization.ManagerIDStr, dbo.UT_SYS_Organization.ManagerNameStr, 
        UT_SYS_Organization_1.OrgName AS OrgID1Name, UT_SYS_Organization_2.OrgName AS OrgID2Name, 
        UT_SYS_Organization_3.OrgName AS OrgID3Name
FROM      dbo.UT_SYS_Organization LEFT OUTER JOIN
          dbo.UT_SYS_Organization AS UT_SYS_Organization_1 ON 
          dbo.UT_SYS_Organization.OrgParentID = UT_SYS_Organization_1.OrgID LEFT OUTER JOIN
          dbo.UT_SYS_Organization AS UT_SYS_Organization_2 ON 
          UT_SYS_Organization_1.OrgParentID = UT_SYS_Organization_2.OrgID LEFT OUTER JOIN
          dbo.UT_SYS_Organization AS UT_SYS_Organization_3 ON 
          UT_SYS_Organization_2.OrgParentID = UT_SYS_Organization_3.OrgID
```

## 业务流程

### 组织架构创建流程
1. 用户选择项目
2. 选择父级部门（或根节点）
3. 填写部门基本信息
4. 设置部门类型和特殊标识
5. 系统自动计算层级关系
6. 保存部门信息到数据库

### 部门编辑流程
1. 从组织架构树选择要编辑的部门
2. 加载现有部门信息
3. 修改部门信息
4. 验证数据完整性和层级关系
5. 保存更新后的部门信息

### 部门删除流程
1. 选择要删除的部门
2. 检查是否存在子部门
3. 检查是否存在关联用户
4. 如果存在关联数据，提示先处理关联关系
5. 执行软删除操作（设置IsDeleted标记）

### 组织架构树加载流程
1. 根据用户权限加载可访问的项目列表
2. 动态加载项目下的组织架构
3. 按层级展示部门结构
4. 支持节点的展开和收缩

## 权限控制

### 功能权限
- 组织架构查看权限
- 部门创建权限
- 部门编辑权限
- 部门删除权限
- 特殊部门设置权限

### 数据权限
- 按项目隔离组织架构数据
- 用户只能管理所属项目的组织架构
- 支持跨项目组织架构查看（特殊权限）

### 层级权限
- 上级部门可以管理下级部门
- 部门负责人可以管理本部门信息
- 系统管理员拥有全部权限

## 特殊功能

### 维修组标识
- 通过MaintenanceNY字段标识维修组
- 维修组用于设备维修工单分配
- 支持维修任务的自动分派

### 管理员组标识
- 通过IsAdmin字段标识管理员组
- 管理员组拥有特殊权限
- 用于系统权限控制

### 层级管理
- 自动计算和维护部门层级
- 支持多级部门结构
- 层级深度可配置

## 技术特点

1. **多项目支持**: 支持多项目组织架构数据隔离
2. **层级管理**: 自动维护组织架构层级关系
3. **树形展示**: 动态加载的树形结构展示
4. **软删除**: 采用软删除机制保护历史数据
5. **权限集成**: 与系统权限管理深度集成
6. **特殊标识**: 支持维修组、管理员组等特殊标识

## 扩展功能

1. **部门模板**: 支持组织架构模板化管理
2. **批量导入**: 支持Excel批量导入组织架构
3. **部门统计**: 提供部门人员统计分析
4. **组织图**: 可视化组织架构图展示
5. **历史追踪**: 组织架构变更历史记录
6. **同步集成**: 与第三方系统组织架构同步

## 关联模块

1. **用户管理**: 用户隶属于组织架构
2. **角色权限**: 基于组织架构的权限控制
3. **人事管理**: 员工档案与组织架构关联
4. **工作流程**: 审批流程基于组织架构
5. **设备管理**: 设备维修组织分配
6. **考勤管理**: 考勤组织架构管理

## 数据字典

### 部门类型 (OrgType)
- 0: 公司级
- 1: 分公司级
- 2: 项目级

### 特殊标识
- MaintenanceNY: 是否维修组 (0否, 1是)
- IsAdmin: 是否管理员组 (0否, 1是)
- IsLast: 是否最后一级 (0否, 1是)

### 状态值 (Status)
- 0: 正常
- 1: 停用
- 2: 待审核

## 性能优化

1. **索引优化**: 在UnitID、OrgParentID等字段建立索引
2. **缓存机制**: 组织架构树结构缓存
3. **分页加载**: 大型组织架构分页加载
4. **异步加载**: 树节点异步动态加载
5. **数据压缩**: 层级关系数据压缩存储
