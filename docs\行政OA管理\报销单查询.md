# 报销单查询

## 基本信息
- **子菜单名**: 报销单查询
- **路由**: ~/ModuleXZ/FeeApply/SIndex
- **模块名**: ModuleXZ
- **视图名**: FeeApply
- **功能描述**: 用于查询和统计报销申请记录，支持费用分析和财务管理

## 页面逻辑分析

### 主要Controller方法
**FeeApplyController.cs**

1. **SIndex(ModelFeeApplyIndex model, ExportHelper export)** - 报销单查询列表
   - 功能：显示报销申请查询结果
   - 逻辑：
     - 设置导出对象model.ExportObject = export
     - 调用model.SearchIndex()获取查询数据
     - 返回查询结果视图

2. **QueryDetail(ModelFeeApplyCreate model)** - 报销单详情查询
   - 功能：查看报销申请详细信息
   - 逻辑：调用model.RetriveDataShow()获取详情数据

3. **FeeStatistics(ModelFeeStatistics model)** - 费用统计查询
   - 功能：查询费用统计分析数据
   - 逻辑：调用model.RetriveData()获取统计数据

4. **ExportFeeData(ModelFeeApplyIndex model)** - 导出报销数据
   - 功能：导出报销申请数据到Excel
   - 逻辑：
     - 调用model.SearchIndex()获取数据
     - 生成Excel文件
     - 返回文件下载

### 主要Model类
**ModelFeeApply.cs**

1. **ModelFeeApplyIndex** - 报销申请查询模型
   - 属性：
     - SearchEntity：查询条件实体
     - GridDataSources：查询结果数据
     - Count：总记录数
     - ExportObject：导出对象
   - 方法：
     - SearchIndex()：执行查询操作
     - 支持多种查询条件组合

2. **ModelFeeApplyCreate** - 报销申请详情模型
   - 方法：
     - RetriveDataShow()：获取报销申请详情用于查看

## 数据库使用情况

### 主要数据表
1. **UTUnitFeeApply** - 报销申请主表
   - 查询字段：
     - ApplyUserID：申请人
     - UnitID：单位
     - ApplyCode：申请编号
     - ApplyTitle：申请标题
     - TotalAmount：总金额
     - ApplyDate：申请日期
     - Status：状态
     - CreateDate：创建时间
   - 操作：多条件查询、统计分析

2. **UTUnitFeeApplyDetail** - 报销申请明细表
   - 查询字段：
     - FeeID：报销申请ID
     - FeeType：费用类型
     - Amount：金额
     - FeeDate：费用发生日期
     - Description：费用说明
     - InvoiceNumber：发票号码
   - 操作：明细查询、费用分析

3. **UVUnitFeeApply** - 报销申请视图（如果存在）
   - 包含关联的用户信息、单位信息等
   - 用于复杂查询和报表

### 主要Service接口
1. **IServiceUTUnitFeeApply** - 报销申请服务
   - 查询相关方法：
     - SearchFeeApply()：按条件查询报销记录
     - GetFeeStatistics()：获取费用统计数据
     - ExportFeeData()：导出报销数据

2. **IServiceUTUnitFeeApplyDetail** - 报销明细服务
   - 方法：明细查询和统计

## 查询功能
### 基本查询条件
1. **时间范围**：
   - 申请时间范围
   - 费用发生时间范围
   - 审批时间范围
   - 付款时间范围

2. **人员条件**：
   - 申请人
   - 部门
   - 审批人

3. **金额条件**：
   - 金额范围
   - 费用类型
   - 预算科目

4. **状态条件**：
   - 申请状态
   - 审批状态
   - 付款状态

### 高级查询功能
1. **费用类型查询**：按具体费用类型查询
2. **发票查询**：按发票号码或供应商查询
3. **关键字查询**：在申请标题和说明中搜索
4. **组合查询**：支持多个条件的组合查询

## 查询结果展示
### 列表显示
- **申请信息**：申请人、申请时间、申请编号
- **费用信息**：总金额、费用类型、费用说明
- **状态信息**：当前状态、审批进度、付款状态
- **时间信息**：申请时间、审批时间、付款时间

### 详情显示
- **完整的报销申请信息**
- **详细的费用明细**
- **审批历史记录**
- **相关发票和附件**

## 统计分析功能
### 基础统计
1. **金额统计**：
   - 总报销金额
   - 各类型费用金额
   - 各部门费用统计

2. **数量统计**：
   - 总申请数量
   - 各状态申请数量
   - 各类型申请数量

3. **时间统计**：
   - 月度费用统计
   - 季度费用趋势
   - 年度费用分析

### 高级分析
1. **费用类型分析**：各类型费用的分布和趋势
2. **部门费用分析**：各部门费用支出对比
3. **个人费用分析**：个人费用支出统计
4. **预算执行分析**：预算执行情况分析

## 财务分析功能
### 成本分析
1. **成本中心分析**：按成本中心统计费用
2. **项目成本分析**：按项目统计费用
3. **费用趋势分析**：费用支出趋势分析
4. **预算对比分析**：实际支出与预算对比

### 合规分析
1. **发票合规性**：检查发票的合规性
2. **费用标准**：检查是否符合费用标准
3. **审批合规性**：检查审批流程合规性
4. **预算控制**：检查预算控制情况

## 导出功能
### 支持格式
- **Excel格式**：详细的费用记录表
- **PDF格式**：费用报告
- **CSV格式**：财务系统导入格式

### 导出内容
1. **基础数据导出**：报销申请基本信息
2. **明细数据导出**：包含所有明细的详细数据
3. **统计数据导出**：费用统计分析结果
4. **财务报表导出**：标准财务报表格式

## 权限控制
### 查询权限
- **个人查询**：员工只能查询自己的报销记录
- **部门查询**：部门负责人可查询本部门记录
- **财务查询**：财务人员可查询所有记录
- **全局查询**：管理员可查询所有记录

### 数据权限
- **金额保护**：根据权限显示不同详细程度的金额信息
- **敏感信息保护**：保护敏感的费用信息

## 报表功能
1. **费用统计报表**：各类费用的统计分析
2. **部门费用报表**：各部门费用支出报表
3. **预算执行报表**：预算执行情况报表
4. **费用趋势报表**：费用支出趋势分析报表

## 审计功能
1. **费用审计**：费用支出的审计跟踪
2. **合规检查**：费用报销的合规性检查
3. **异常检测**：检测异常的费用支出
4. **风险评估**：评估费用支出风险

## 性能优化
1. **索引优化**：在常用查询字段上建立索引
2. **分页查询**：大数据量时使用分页
3. **缓存机制**：常用统计结果缓存
4. **异步处理**：大数据量导出使用异步处理

## 相关枚举
- **EnumFeeApplyStatus**：报销申请状态枚举
- **EnumFeeType**：费用类型枚举
- **EnumPaymentStatus**：付款状态枚举
