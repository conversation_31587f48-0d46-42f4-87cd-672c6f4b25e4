# 设备设施管理模块UML类图

## 类结构说明

### 实体类层次结构

#### 设备管理核心类
- **UTEQEquipment**: 设备信息管理，包含设备基本信息、技术参数、维保信息等
- **UTBaseEQClass**: 设备分类管理，支持树形分类结构和多种检查周期配置
- **UTBaseEQSystem**: 设备系统管理，定义消防、特种设备等系统类型

#### 巡检管理类
- **UTEQInspectTaskDetial**: 巡检任务详情，管理设备巡检计划和执行
- **UTEQInspectTaskDetialInfo**: 巡检内容详情，记录检查项和发现的问题
- **UTBaseCheckInfo**: 检查项标准，定义各设备分类的检查要求

#### 报修管理类
- **UTEQMaintain**: 维修单主体，管理报修流程和状态
- **UTEQMaintainDetial**: 维修处理详情，记录维修过程和结果

#### 基础管理类
- **UTBaseUnitManage**: 项目管理，支持多层级组织架构
- **UTUnitBuild**: 建筑物管理，提供设备安装位置信息

### 业务层类结构
- **Biz层**: 业务逻辑处理层，封装设备管理核心业务规则
- **Dal层**: 数据访问层，处理设备相关数据库操作
- **Model层**: 模型层，定义设备管理数据传输对象

### 设计模式应用
- **Repository模式**: 统一数据访问接口
- **Service模式**: 封装业务逻辑
- **Factory模式**: 创建设备对象实例

## UML类图

```mermaid
classDiagram
    class UTBaseUnitManage {
        +Guid ID
        +Guid? PID
        +String UnitCode
        +String UnitName
        +Int32? UnitLevel
        +String Manager
        +String Tel
        +String UnitAddress
        +Decimal? UnitArea
        +DateTime? CreateDate
        +GetChildUnits() List~UTBaseUnitManage~
        +GetBuildings() List~UTUnitBuild~
        +GetEquipments() List~UTEQEquipment~
        +IsProject() Boolean
    }
    
    class UTUnitBuild {
        +Guid ID
        +Guid? UnitID
        +Int32? BuildType
        +String BuildName
        +String Contacts
        +String ContactsTel
        +Decimal? BuildArea
        +Decimal? UsedArea
        +Int32? UpFloor
        +Int32? DownFloor
        +Int32? BuildLevel
        +GetEquipments() List~UTEQEquipment~
        +GetBuildTypeDescription() String
        +GetTotalArea() Decimal
        +GetFloorCount() Int32
    }
    
    class UTBaseEQSystem {
        +Guid ID
        +String SystemCode
        +String SystemName
        +Int32? SystemType
        +Int32? IsFire
        +Int32? IsSpecial
        +String Remark
        +Int32? OrderIndex
        +GetEQClasses() List~UTBaseEQClass~
        +IsFireSystem() Boolean
        +IsSpecialEquipment() Boolean
        +GetSystemTypeDescription() String
    }
    
    class UTBaseEQClass {
        +Guid ID
        +Guid? EQSystemID
        +Int32? ClassLevel
        +Guid? PClassID
        +String ClassCode
        +String ClassName
        +Int32? IsLast
        +Int32? OrderIndex
        +Int32? IsDayCheck
        +Int32? IsWeekCheck
        +Int32? IsMonthCheck
        +Int32? IsQuarterCheck
        +Int32? IsHalfYearCheck
        +Int32? IsYearCheck
        +GetParentClass() UTBaseEQClass
        +GetChildClasses() List~UTBaseEQClass~
        +GetEquipments() List~UTEQEquipment~
        +GetCheckInfos() List~UTBaseCheckInfo~
        +GetCheckCycles() List~String~
        +IsLeafClass() Boolean
    }
    
    class UTEQEquipment {
        +Guid ID
        +Guid? UnitID
        +Guid? BuildID
        +Guid? EQClassID
        +String EQCode
        +String EQName
        +String Manager
        +Decimal? Count
        +String CountUnit
        +Int32? EQStatus
        +String EQType
        +Int32? IsNeedInspection
        +String InstallSite
        +DateTime? InstallDate
        +DateTime? WarrantyDate
        +String Brand
        +String EQModel
        +Decimal? Amount
        +GetStatusDescription() String
        +IsUnderWarranty() Boolean
        +GetInspectTasks() List~UTEQInspectTaskDetial~
        +GetMaintainHistory() List~UTEQMaintain~
        +NeedInspection() Boolean
        +GetEQClass() UTBaseEQClass
    }
    
    class UTBaseCheckInfo {
        +Guid ID
        +Guid? UnitID
        +Guid? EQClassID
        +String CheckItem
        +String CheckMethod
        +String CheckStandard
        +Int32? OrderIndex
        +String Remark
        +GetEQClass() UTBaseEQClass
        +GetInspectTaskInfos() List~UTEQInspectTaskDetialInfo~
        +ValidateCheck() Boolean
    }
    
    class UTEQInspectTaskDetial {
        +Guid ID
        +Guid? UnitID
        +Guid? InspectPlanID
        +Guid? InspectPlanDetialID
        +Guid? EQID
        +Int32? CycleInt
        +Int32? CycleUnit
        +Int32? CycleCount
        +Int32? IndexOrder
        +DateTime? StartDate
        +DateTime? EndDate
        +String NeedInspectUser
        +Int64? InspectUser
        +DateTime? InspectDate
        +Int32? ISOK
        +Int32? Status
        +GetEquipment() UTEQEquipment
        +GetInspectInfos() List~UTEQInspectTaskDetialInfo~
        +IsCompleted() Boolean
        +IsOverdue() Boolean
        +GetStatusDescription() String
        +GenerateMaintain() UTEQMaintain
    }
    
    class UTEQInspectTaskDetialInfo {
        +Guid ID
        +Guid? InspectTaskDetialID
        +Guid? CheckInfoID
        +String CheckInfo
        +String CheckMethod
        +String BadDesc
        +Int32? OrderInt
        +Int32? IsFJ
        +DateTime? FJTime
        +String FJDesc
        +Int64? FJUser
        +Int32? IsNeedRepair
        +Int64? IsNeedZG
        +Int32? Status
        +GetInspectTask() UTEQInspectTaskDetial
        +GetCheckInfo() UTBaseCheckInfo
        +NeedRepair() Boolean
        +NeedRectification() Boolean
        +IsRectified() Boolean
        +CreateMaintainRecord() UTEQMaintain
    }
    
    class UTEQMaintain {
        +Guid ID
        +Guid? UnitID
        +Guid? CustomerUserID
        +Int64? UserID
        +String MaintainCode
        +Int32? EQSystemType
        +Int32? FromType
        +Int32? BaseType
        +String BadDesc
        +String BadSite
        +Guid? EqID
        +Guid? BuildID
        +String SendUser
        +DateTime? SendTime
        +Int32? IsUrgent
        +Int32? Status
        +DateTime? CreateDate
        +GetEquipment() UTEQEquipment
        +GetMaintainDetails() List~UTEQMaintainDetial~
        +GetFromTypeDescription() String
        +GetStatusDescription() String
        +IsUrgent() Boolean
        +IsCompleted() Boolean
        +GetProcessingTime() TimeSpan
    }
    
    class UTEQMaintainDetial {
        +Guid ID
        +Guid? MaintainID
        +Int32? DetialType
        +String BeginDeal
        +String EndDeal
        +DateTime? DealTime
        +Int64? DealUserID
        +String DealUserName
        +String Remark
        +GetMaintain() UTEQMaintain
        +GetDetialTypeDescription() String
        +IsCompleted() Boolean
        +GetDealDuration() TimeSpan
    }

    %% 继承关系
    UTBaseUnitManage --|> BaseEntity : 继承基础实体
    UTUnitBuild --|> BaseEntity : 继承基础实体
    UTBaseEQSystem --|> BaseEntity : 继承基础实体
    UTBaseEQClass --|> BaseEntity : 继承基础实体
    UTEQEquipment --|> BaseEntity : 继承基础实体
    UTBaseCheckInfo --|> BaseEntity : 继承基础实体
    UTEQInspectTaskDetial --|> BaseEntity : 继承基础实体
    UTEQInspectTaskDetialInfo --|> BaseEntity : 继承基础实体
    UTEQMaintain --|> BaseEntity : 继承基础实体
    UTEQMaintainDetial --|> BaseEntity : 继承基础实体

    %% 组合关系
    UTBaseUnitManage "1" *-- "0..*" UTBaseUnitManage : 包含子项目
    UTBaseUnitManage "1" *-- "0..*" UTUnitBuild : 包含建筑
    UTBaseUnitManage "1" *-- "0..*" UTEQEquipment : 管理设备
    UTBaseEQSystem "1" *-- "0..*" UTBaseEQClass : 包含设备分类
    UTBaseEQClass "1" *-- "0..*" UTBaseEQClass : 包含子分类
    UTBaseEQClass "1" *-- "0..*" UTEQEquipment : 分类设备
    UTBaseEQClass "1" *-- "0..*" UTBaseCheckInfo : 定义检查项
    UTEQEquipment "1" *-- "0..*" UTEQInspectTaskDetial : 产生巡检任务
    UTEQEquipment "1" *-- "0..*" UTEQMaintain : 产生维修单
    UTEQInspectTaskDetial "1" *-- "0..*" UTEQInspectTaskDetialInfo : 包含检查内容
    UTEQMaintain "1" *-- "0..*" UTEQMaintainDetial : 包含处理详情

    %% 关联关系
    UTUnitBuild "1" o-- "0..*" UTEQEquipment : 安装设备
    UTBaseCheckInfo "1" o-- "0..*" UTEQInspectTaskDetialInfo : 检查依据
    UTEQInspectTaskDetialInfo "1" o-- "0..*" UTEQMaintain : 巡检发现问题

    %% 抽象基类
    class BaseEntity {
        <<abstract>>
        +Guid ID
        +DateTime? CreateDate
        +Int64? CreateUser
        +DateTime? UpdateDate
        +Int64? UpdateUser
        +GetCreateUserName() String
        +GetUpdateUserName() String
        +IsNew() Boolean
    }
```

## 核心业务流程

### 设备台账管理流程
1. **设备分类定义**: UTBaseEQSystem → UTBaseEQClass（树形结构）
2. **设备信息录入**: UTEQEquipment关联分类和位置
3. **检查标准配置**: UTBaseCheckInfo定义各分类的检查要求

### 设备巡检业务流程
1. **任务生成**: 根据设备分类和检查周期生成UTEQInspectTaskDetial
2. **任务执行**: 创建UTEQInspectTaskDetialInfo记录检查结果
3. **问题处理**: 发现问题时自动生成UTEQMaintain维修单
4. **统计分析**: 支持按项目、系统、设备、个人等维度统计

### 设备报修业务流程
1. **报修受理**: 创建UTEQMaintain记录报修信息
2. **处理跟踪**: UTEQMaintainDetial记录每个处理环节
3. **结果反馈**: 完成维修并更新设备状态
4. **数据分析**: 支持多维度报修统计和故障率分析

### 数据分析支持
- **按项目分析**: 通过UnitID实现项目维度统计
- **按系统分析**: 通过EQSystemID实现系统维度统计
- **按设备分析**: 通过EqID实现单设备维度统计
- **按个人分析**: 通过InspectUser/DealUserID实现人员维度统计

## 设计特点

### 灵活的分类体系
- 支持多级设备系统和分类
- 可配置的检查周期（日、周、月、季、半年、年）
- 灵活的设备属性扩展

### 完整的业务闭环
- 设备台账 → 巡检计划 → 问题发现 → 维修处理 → 数据分析
- 支持多种报修来源和处理流程
- 提供丰富的统计分析功能

### 良好的扩展性
- 基于接口的分层架构
- 统一的实体基类设计
- 支持业务规则的灵活配置 